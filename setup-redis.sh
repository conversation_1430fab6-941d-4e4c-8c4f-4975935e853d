#!/bin/bash

echo "🚀 Setting up Redis for High-Performance Cache Flow..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Redis container is already running
if docker ps | grep -q redis; then
    echo "✅ Redis container is already running"
else
    echo "🐳 Starting Redis container..."
    
    # Stop and remove existing Redis container if it exists
    docker stop billiard-redis 2>/dev/null || true
    docker rm billiard-redis 2>/dev/null || true
    
    # Start new Redis container
    docker run --name billiard-redis \
        -p 6379:6379 \
        -d redis:7-alpine \
        redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    echo "⏳ Waiting for Redis to start..."
    sleep 5
fi

# Test Redis connection
echo "🔍 Testing Redis connection..."
if docker exec billiard-redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis is running and responding"
else
    echo "❌ Redis is not responding"
    exit 1
fi

# Install Redis client for Node.js if not already installed
echo "📦 Installing Redis client for Node.js..."
if [ -f "package.json" ]; then
    npm install redis
    echo "✅ Redis client installed"
else
    echo "⚠️ No package.json found. Please run 'npm install redis' manually."
fi

# Set environment variables
echo "🔧 Setting up environment variables..."
if [ ! -f ".env.local" ]; then
    touch .env.local
fi

# Add Redis URL to environment if not already present
if ! grep -q "REDIS_URL" .env.local; then
    echo "REDIS_URL=redis://localhost:6379" >> .env.local
    echo "✅ Added REDIS_URL to .env.local"
else
    echo "ℹ️ REDIS_URL already exists in .env.local"
fi

echo ""
echo "🎉 Redis setup completed successfully!"
echo ""
echo "📊 Cache Flow Architecture:"
echo "   UI State → Memory Cache → Redis Cache → PostgreSQL"
echo ""
echo "🔧 Configuration:"
echo "   - Redis URL: redis://localhost:6379"
echo "   - Memory TTL: 5-10 minutes"
echo "   - Redis TTL: 30-60 minutes"
echo "   - Max Memory: 256MB with LRU eviction"
echo ""
echo "📈 Performance Benefits:"
echo "   - 🚀 Ultra-fast memory cache (microseconds)"
echo "   - ⚡ Fast Redis cache (milliseconds)"
echo "   - 💾 Reliable PostgreSQL persistence"
echo "   - 🔄 Automatic cache invalidation"
echo "   - 📊 Performance monitoring"
echo ""
echo "🛠️ Management Commands:"
echo "   - View Redis logs: docker logs billiard-redis"
echo "   - Redis CLI: docker exec -it billiard-redis redis-cli"
echo "   - Stop Redis: docker stop billiard-redis"
echo "   - Restart Redis: docker restart billiard-redis"
echo ""
echo "🌐 Next Steps:"
echo "   1. Restart your development server"
echo "   2. The high-performance cache will initialize automatically"
echo "   3. Monitor cache performance at /api/cache?action=stats"
echo "   4. Check cache health at /api/cache?action=health"
echo ""
echo "✨ Your app now has enterprise-level caching!"
