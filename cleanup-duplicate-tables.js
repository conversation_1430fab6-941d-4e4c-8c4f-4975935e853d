const { Pool } = require('pg')

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: 'postgres123',
  port: 5431,
})

async function cleanupDuplicateTables() {
  try {
    console.log('🔍 Checking for duplicate game tables...')
    
    // Find duplicate tables (same number + same user assignment)
    const duplicatesQuery = `
      SELECT 
        number, 
        assigned_user_id, 
        assigned_username,
        COUNT(*) as count,
        array_agg(id ORDER BY id) as ids
      FROM gametables 
      WHERE assigned_user_id IS NOT NULL
      GROUP BY number, assigned_user_id, assigned_username
      HAVING COUNT(*) > 1
      ORDER BY number, assigned_user_id
    `
    
    const duplicates = await pool.query(duplicatesQuery)
    
    if (duplicates.rows.length === 0) {
      console.log('✅ No duplicate tables found!')
      return
    }
    
    console.log(`🔧 Found ${duplicates.rows.length} sets of duplicate tables:`)
    
    for (const duplicate of duplicates.rows) {
      const { number, assigned_user_id, assigned_username, count, ids } = duplicate
      console.log(`  - Table ${number} (User: ${assigned_username}) has ${count} duplicates`)
      console.log(`    IDs: ${ids.join(', ')}`)
      
      // Keep the first one (lowest ID) and delete the rest
      const idsToDelete = ids.slice(1) // Remove first element, keep the rest for deletion
      
      if (idsToDelete.length > 0) {
        console.log(`    Deleting duplicate IDs: ${idsToDelete.join(', ')}`)
        
        await pool.query(
          `DELETE FROM gametables WHERE id = ANY($1)`,
          [idsToDelete]
        )
        
        console.log(`    ✅ Deleted ${idsToDelete.length} duplicate entries`)
      }
    }
    
    console.log('\n🎯 Cleanup completed!')
    
    // Show final table count
    const finalCount = await pool.query('SELECT COUNT(*) as count FROM gametables')
    console.log(`📊 Final table count: ${finalCount.rows[0].count}`)
    
    // Show tables by user
    const tablesByUser = await pool.query(`
      SELECT 
        COALESCE(assigned_username, 'Unassigned') as username,
        COUNT(*) as table_count,
        array_agg(DISTINCT number ORDER BY number) as table_numbers
      FROM gametables 
      GROUP BY assigned_username
      ORDER BY assigned_username
    `)
    
    console.log('\n📋 Tables by user:')
    for (const userTables of tablesByUser.rows) {
      console.log(`  ${userTables.username}: ${userTables.table_count} tables (${userTables.table_numbers.join(', ')})`)
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error)
  } finally {
    await pool.end()
  }
}

cleanupDuplicateTables()
