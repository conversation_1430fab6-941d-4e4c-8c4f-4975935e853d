/**
 * Ultra-fast in-memory cache with TTL support
 * Used for API response caching and performance optimization
 */

interface CacheEntry<T = any> {
  value: T
  timestamp: number
  ttl: number
  hits: number
}

interface CacheStats {
  totalEntries: number
  totalHits: number
  totalMisses: number
  hitRate: number
  memoryUsage: number
}

class UltraCache {
  private cache = new Map<string, CacheEntry>()
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0
  }

  /**
   * Set a value in cache with optional TTL
   */
  set<T>(key: string, value: T, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl: ttlMs,
      hits: 0
    })
    this.stats.sets++
  }

  /**
   * Get a value from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      this.stats.misses++
      return null
    }

    entry.hits++
    this.stats.hits++
    return entry.value as T
  }

  /**
   * Delete a specific key
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalOperations = this.stats.hits + this.stats.misses
    const hitRate = totalOperations > 0 ? (this.stats.hits / totalOperations) * 100 : 0
    
    // Estimate memory usage
    let memoryUsage = 0
    for (const [key, entry] of this.cache.entries()) {
      memoryUsage += key.length * 2 // UTF-16 characters
      memoryUsage += JSON.stringify(entry.value).length * 2
      memoryUsage += 64 // Overhead for entry object
    }

    return {
      totalEntries: this.cache.size,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      hitRate,
      memoryUsage
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    let cleaned = 0
    const now = Date.now()
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }
    
    return cleaned
  }

  /**
   * Get all keys matching a pattern
   */
  getKeys(pattern?: string): string[] {
    const keys = Array.from(this.cache.keys())
    
    if (!pattern) return keys
    
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return keys.filter(key => regex.test(key))
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size
  }
}

// Global cache instance
export const ultraCache = new UltraCache()

/**
 * Cache decorator for functions
 */
export function cached<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttlMs: number = 5 * 60 * 1000
): T {
  return ((...args: Parameters<T>) => {
    const key = keyGenerator(...args)
    
    // Try to get from cache first
    const cached = ultraCache.get(key)
    if (cached !== null) {
      return cached
    }
    
    // Execute function and cache result
    const result = fn(...args)
    ultraCache.set(key, result, ttlMs)
    
    return result
  }) as T
}

/**
 * Async cache decorator
 */
export function cachedAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttlMs: number = 5 * 60 * 1000
): T {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args)
    
    // Try to get from cache first
    const cached = ultraCache.get(key)
    if (cached !== null) {
      return cached
    }
    
    // Execute function and cache result
    const result = await fn(...args)
    ultraCache.set(key, result, ttlMs)
    
    return result
  }) as T
}

/**
 * Invalidate cache entries by pattern
 */
export function invalidateCache(pattern: string): number {
  const keys = ultraCache.getKeys(pattern)
  let invalidated = 0
  
  for (const key of keys) {
    if (ultraCache.delete(key)) {
      invalidated++
    }
  }
  
  return invalidated
}

/**
 * Cache middleware for API routes
 */
export function withCache(
  handler: (req: any, res: any) => Promise<any>,
  keyGenerator: (req: any) => string,
  ttlMs: number = 5 * 60 * 1000
) {
  return async (req: any, res: any) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return handler(req, res)
    }
    
    const key = keyGenerator(req)
    
    // Try to get from cache
    const cached = ultraCache.get(key)
    if (cached !== null) {
      return new Response(JSON.stringify(cached), {
        headers: {
          'Content-Type': 'application/json',
          'X-Cache': 'HIT'
        }
      })
    }
    
    // Execute handler
    const result = await handler(req, res)
    
    // Cache successful responses
    if (result && result.status === 200) {
      const data = await result.json()
      ultraCache.set(key, data, ttlMs)
    }
    
    return result
  }
}

/**
 * Periodic cleanup of expired entries
 */
setInterval(() => {
  const cleaned = ultraCache.cleanup()
  if (cleaned > 0) {
    console.log(`Cache cleanup: removed ${cleaned} expired entries`)
  }
}, 60 * 1000) // Run every minute

// Export for debugging
if (typeof window !== 'undefined') {
  (window as any).ultraCache = ultraCache
}
