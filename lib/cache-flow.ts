import { redisManager } from './redis'
import pool from './db'
import { ultraCache } from './cache'

/**
 * High-Performance Cache Flow Implementation
 * Flow: UI State → Memory Cache → Redis Cache → PostgreSQL
 * 
 * This implements the recommended architecture for maximum speed + no data loss
 */

export interface CacheOptions {
  memoryTTL?: number    // Memory cache TTL in ms (default: 5 minutes)
  redisTTL?: number     // Redis cache TTL in seconds (default: 1 hour)
  skipMemory?: boolean  // Skip memory cache
  skipRedis?: boolean   // Skip Redis cache
  forceRefresh?: boolean // Force refresh from database
}

export interface CacheResult<T> {
  data: T | null
  source: 'memory' | 'redis' | 'database' | 'none'
  success: boolean
  error?: string
  timing: {
    total: number
    memory?: number
    redis?: number
    database?: number
  }
}

/**
 * High-Performance Cache Manager
 */
class CacheFlow {
  private static instance: CacheFlow
  
  private constructor() {}

  public static getInstance(): CacheFlow {
    if (!CacheFlow.instance) {
      CacheFlow.instance = new CacheFlow()
    }
    return CacheFlow.instance
  }

  /**
   * Initialize cache flow (connect to Redis)
   */
  async initialize(): Promise<void> {
    try {
      await redisManager.connect()
      console.log('🚀 Cache flow initialized successfully')
    } catch (error) {
      console.warn('⚠️ Redis unavailable, falling back to memory + database')
    }
  }

  /**
   * Get data with full cache flow
   * 1. Check memory cache
   * 2. Check Redis cache
   * 3. Check PostgreSQL
   * 4. Cache results in reverse order
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<CacheResult<T>> {
    const startTime = performance.now()
    const timing: any = {}
    
    try {
      // Step 1: Check memory cache
      if (!options.skipMemory && !options.forceRefresh) {
        const memoryStart = performance.now()
        const memoryData = ultraCache.get<T>(key)
        timing.memory = performance.now() - memoryStart
        
        if (memoryData !== null) {
          return {
            data: memoryData,
            source: 'memory',
            success: true,
            timing: { total: performance.now() - startTime, memory: timing.memory }
          }
        }
      }

      // Step 2: Check Redis cache
      if (!options.skipRedis && !options.forceRefresh && redisManager.isAvailable()) {
        const redisStart = performance.now()
        const redisData = await redisManager.getJSON<T>(key)
        timing.redis = performance.now() - redisStart
        
        if (redisData !== null) {
          // Cache in memory for next time
          if (!options.skipMemory) {
            ultraCache.set(key, redisData, options.memoryTTL || 5 * 60 * 1000)
          }
          
          return {
            data: redisData,
            source: 'redis',
            success: true,
            timing: { 
              total: performance.now() - startTime, 
              memory: timing.memory,
              redis: timing.redis 
            }
          }
        }
      }

      // Step 3: Check PostgreSQL
      const dbStart = performance.now()
      const dbData = await this.getFromDatabase<T>(key)
      timing.database = performance.now() - dbStart
      
      if (dbData !== null) {
        // Cache in Redis and memory for next time
        if (!options.skipRedis && redisManager.isAvailable()) {
          await redisManager.setJSON(key, dbData, options.redisTTL || 3600)
        }
        
        if (!options.skipMemory) {
          ultraCache.set(key, dbData, options.memoryTTL || 5 * 60 * 1000)
        }
        
        return {
          data: dbData,
          source: 'database',
          success: true,
          timing: { 
            total: performance.now() - startTime,
            memory: timing.memory,
            redis: timing.redis,
            database: timing.database
          }
        }
      }

      // Not found anywhere
      return {
        data: null,
        source: 'none',
        success: false,
        timing: { 
          total: performance.now() - startTime,
          memory: timing.memory,
          redis: timing.redis,
          database: timing.database
        }
      }

    } catch (error) {
      console.error(`Cache flow GET error for key ${key}:`, error)
      return {
        data: null,
        source: 'none',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timing: { total: performance.now() - startTime }
      }
    }
  }

  /**
   * Set data with full cache flow
   * 1. Save to PostgreSQL (source of truth)
   * 2. Cache in Redis
   * 3. Cache in memory
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<boolean> {
    try {
      // Step 1: Save to PostgreSQL (source of truth)
      const dbSuccess = await this.saveToDatabase(key, data)
      if (!dbSuccess) {
        console.error(`Failed to save ${key} to database`)
        return false
      }

      // Step 2: Cache in Redis
      if (!options.skipRedis && redisManager.isAvailable()) {
        await redisManager.setJSON(key, data, options.redisTTL || 3600)
      }

      // Step 3: Cache in memory
      if (!options.skipMemory) {
        ultraCache.set(key, data, options.memoryTTL || 5 * 60 * 1000)
      }

      console.log(`✅ Cache flow SET completed: ${key}`)
      return true

    } catch (error) {
      console.error(`Cache flow SET error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete from all cache layers
   */
  async delete(key: string): Promise<boolean> {
    try {
      // Delete from all layers
      const dbSuccess = await this.deleteFromDatabase(key)
      
      if (redisManager.isAvailable()) {
        await redisManager.del(key)
      }
      
      ultraCache.delete(key)

      console.log(`🗑️ Cache flow DELETE completed: ${key}`)
      return dbSuccess

    } catch (error) {
      console.error(`Cache flow DELETE error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Bulk operations for better performance
   */
  async getBulk<T>(keys: string[], options: CacheOptions = {}): Promise<Record<string, CacheResult<T>>> {
    const results: Record<string, CacheResult<T>> = {}
    
    // Process in parallel for better performance
    const promises = keys.map(async (key) => {
      const result = await this.get<T>(key, options)
      return { key, result }
    })
    
    const resolvedResults = await Promise.all(promises)
    resolvedResults.forEach(({ key, result }) => {
      results[key] = result
    })
    
    return results
  }

  /**
   * Invalidate cache for a key (remove from memory and Redis, keep in DB)
   */
  async invalidate(key: string): Promise<void> {
    ultraCache.delete(key)
    
    if (redisManager.isAvailable()) {
      await redisManager.del(key)
    }
    
    console.log(`🔄 Cache invalidated: ${key}`)
  }

  /**
   * Warm up cache (preload from database to Redis and memory)
   */
  async warmUp(keys: string[]): Promise<void> {
    console.log(`🔥 Warming up cache for ${keys.length} keys...`)
    
    for (const key of keys) {
      await this.get(key, { forceRefresh: true })
    }
    
    console.log(`✅ Cache warm-up completed`)
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    const memoryStats = ultraCache.getStats()
    const redisStats = redisManager.isAvailable() ? await redisManager.getStats() : null
    
    return {
      memory: memoryStats,
      redis: redisStats,
      flow: {
        initialized: redisManager.isAvailable(),
        layers: {
          memory: true,
          redis: redisManager.isAvailable(),
          database: true
        }
      }
    }
  }

  /**
   * Private: Get data from PostgreSQL
   */
  private async getFromDatabase<T>(key: string): Promise<T | null> {
    try {
      const result = await pool.query(
        'SELECT storage_value FROM user_storage WHERE storage_key = $1 LIMIT 1',
        [key]
      )
      
      if (result.rows.length === 0) {
        return null
      }
      
      const value = result.rows[0].storage_value
      return typeof value === 'string' ? JSON.parse(value) : value
    } catch (error) {
      console.error(`Database GET error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Private: Save data to PostgreSQL
   */
  private async saveToDatabase<T>(key: string, data: T): Promise<boolean> {
    try {
      const value = JSON.stringify(data)
      await pool.query(
        `INSERT INTO user_storage (user_id, storage_key, storage_value, created_at, updated_at)
         VALUES (1, $1, $2, NOW(), NOW())
         ON CONFLICT (user_id, storage_key)
         DO UPDATE SET storage_value = $2, updated_at = NOW()`,
        [key, value]
      )
      return true
    } catch (error) {
      console.error(`Database SET error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Private: Delete data from PostgreSQL
   */
  private async deleteFromDatabase(key: string): Promise<boolean> {
    try {
      await pool.query(
        'DELETE FROM user_storage WHERE storage_key = $1',
        [key]
      )
      return true
    } catch (error) {
      console.error(`Database DELETE error for key ${key}:`, error)
      return false
    }
  }
}

// Export singleton instance
export const cacheFlow = CacheFlow.getInstance()
export default cacheFlow
