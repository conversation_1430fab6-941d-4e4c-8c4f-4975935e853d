import { NextRequest } from 'next/server'
import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'

export interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
}

export interface AuthResult {
  success: boolean
  user?: User
  error?: string
}

/**
 * Verify JWT token and return user data
 */
export function verifyToken(token: string): User | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    return {
      id: decoded.userId,
      username: decoded.username,
      role: decoded.role,
      fullName: decoded.fullName
    }
  } catch (error) {
    return null
  }
}

/**
 * Get token from Authorization header
 */
export function getAuthHeader(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7) // Remove 'Bearer ' prefix
}

/**
 * Get token from HttpOnly cookie
 */
export function getAuthCookie(): string | null {
  try {
    const cookieStore = cookies()
    return cookieStore.get('auth_token')?.value || null
  } catch (error) {
    // cookies() might not be available in all contexts
    return null
  }
}

/**
 * Get token from HttpOnly cookie (alternative method for request context)
 */
export function getAuthCookieFromRequest(request: NextRequest): string | null {
  try {
    return request.cookies.get('auth_token')?.value || null
  } catch (error) {
    return null
  }
}

/**
 * Get authentication token from multiple sources
 * Priority: Authorization header → HttpOnly cookie
 */
export function getAuthToken(request: NextRequest): string | null {
  // First try Authorization header (for API calls)
  const headerToken = getAuthHeader(request)
  if (headerToken) {
    return headerToken
  }

  // Then try HttpOnly cookie (for browser requests)
  const cookieToken = getAuthCookieFromRequest(request)
  if (cookieToken) {
    return cookieToken
  }

  return null
}

/**
 * Require authentication and return user data
 */
export async function requireAuth(request: NextRequest): Promise<AuthResult> {
  try {
    const token = getAuthToken(request)
    
    if (!token) {
      return {
        success: false,
        error: 'No authentication token provided'
      }
    }

    const user = verifyToken(token)
    
    if (!user) {
      return {
        success: false,
        error: 'Invalid authentication token'
      }
    }

    return {
      success: true,
      user
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Authentication error'
    }
  }
}

/**
 * Require admin authentication
 */
export async function requireAdmin(request: NextRequest): Promise<AuthResult> {
  const authResult = await requireAuth(request)

  if (!authResult.success || !authResult.user) {
    return authResult
  }

  if (authResult.user.role !== 'admin') {
    return {
      success: false,
      error: 'Admin access required'
    }
  }

  return authResult
}

/**
 * Create JWT token
 */
export function createToken(user: User): string {
  return jwt.sign(
    {
      userId: user.id,
      username: user.username,
      role: user.role,
      fullName: user.fullName
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  )
}

/**
 * Cookie configuration for auth tokens
 */
export const AUTH_COOKIE_CONFIG = {
  name: 'auth_token',
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  maxAge: 24 * 60 * 60, // 24 hours in seconds
  path: '/'
}

/**
 * Set auth cookie in NextResponse
 */
export function setAuthCookie(response: any, token: string): void {
  if (response.cookies && typeof response.cookies.set === 'function') {
    // Use NextResponse cookies API
    response.cookies.set(AUTH_COOKIE_CONFIG.name, token, {
      httpOnly: AUTH_COOKIE_CONFIG.httpOnly,
      secure: AUTH_COOKIE_CONFIG.secure,
      sameSite: AUTH_COOKIE_CONFIG.sameSite,
      maxAge: AUTH_COOKIE_CONFIG.maxAge,
      path: AUTH_COOKIE_CONFIG.path
    })
  } else {
    // Fallback to manual header setting
    const cookieValue = `${AUTH_COOKIE_CONFIG.name}=${token}; HttpOnly; Path=${AUTH_COOKIE_CONFIG.path}; Max-Age=${AUTH_COOKIE_CONFIG.maxAge}; SameSite=${AUTH_COOKIE_CONFIG.sameSite}${AUTH_COOKIE_CONFIG.secure ? '; Secure' : ''}`
    response.headers.set('Set-Cookie', cookieValue)
  }
}

/**
 * Clear auth cookie in NextResponse
 */
export function clearAuthCookie(response: any): void {
  if (response.cookies && typeof response.cookies.set === 'function') {
    // Use NextResponse cookies API
    response.cookies.set(AUTH_COOKIE_CONFIG.name, '', {
      httpOnly: AUTH_COOKIE_CONFIG.httpOnly,
      secure: AUTH_COOKIE_CONFIG.secure,
      sameSite: AUTH_COOKIE_CONFIG.sameSite,
      maxAge: 0,
      path: AUTH_COOKIE_CONFIG.path
    })
  } else {
    // Fallback to manual header setting
    const cookieValue = `${AUTH_COOKIE_CONFIG.name}=; HttpOnly; Path=${AUTH_COOKIE_CONFIG.path}; Max-Age=0; SameSite=${AUTH_COOKIE_CONFIG.sameSite}${AUTH_COOKIE_CONFIG.secure ? '; Secure' : ''}`
    response.headers.set('Set-Cookie', cookieValue)
  }
}
