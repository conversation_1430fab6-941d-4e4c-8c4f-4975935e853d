import { Pool } from 'pg'

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5431/postgres',
  // Fallback to individual config if DATABASE_URL is not available
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5431'),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres123',
})

// Export query function for convenience
export const query = (text: string, params?: any[]) => pool.query(text, params)

export default pool
