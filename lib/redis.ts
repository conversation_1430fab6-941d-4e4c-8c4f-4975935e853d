import { createClient, RedisClientType } from 'redis'

/**
 * Redis Cache Manager for High-Performance Data Flow
 * Implements: UI State → Memory → Redis → PostgreSQL
 */
class RedisManager {
  private static instance: RedisManager
  private client: RedisClientType | null = null
  private isConnected = false
  private connectionPromise: Promise<void> | null = null

  private constructor() {}

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager()
    }
    return RedisManager.instance
  }

  /**
   * Initialize Redis connection
   */
  async connect(): Promise<void> {
    if (this.isConnected) return
    if (this.connectionPromise) return this.connectionPromise

    this.connectionPromise = this._connect()
    return this.connectionPromise
  }

  private async _connect(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379'
      
      this.client = createClient({
        url: redisUrl,
        socket: {
          connectTimeout: 5000,
          lazyConnect: true,
        },
        // Retry strategy
        retry_delay_on_failover: 100,
        retry_delay_on_cluster_down: 300,
      })

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err)
        this.isConnected = false
      })

      this.client.on('connect', () => {
        console.log('✅ Redis connected successfully')
        this.isConnected = true
      })

      this.client.on('disconnect', () => {
        console.log('⚠️ Redis disconnected')
        this.isConnected = false
      })

      await this.client.connect()
      this.isConnected = true
      console.log('🚀 Redis cache layer initialized')
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error)
      this.isConnected = false
      this.connectionPromise = null
      throw error
    }
  }

  /**
   * Check if Redis is available
   */
  isAvailable(): boolean {
    return this.isConnected && this.client !== null
  }

  /**
   * Get value from Redis cache
   */
  async get(key: string): Promise<string | null> {
    if (!this.isAvailable()) {
      console.warn('Redis not available for GET operation')
      return null
    }

    try {
      const value = await this.client!.get(key)
      if (value) {
        console.log(`🎯 Redis cache HIT: ${key}`)
      }
      return value
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set value in Redis cache with TTL
   */
  async set(key: string, value: string, ttlSeconds: number = 3600): Promise<boolean> {
    if (!this.isAvailable()) {
      console.warn('Redis not available for SET operation')
      return false
    }

    try {
      await this.client!.setEx(key, ttlSeconds, value)
      console.log(`💾 Redis cache SET: ${key} (TTL: ${ttlSeconds}s)`)
      return true
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete value from Redis cache
   */
  async del(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      console.warn('Redis not available for DEL operation')
      return false
    }

    try {
      const result = await this.client!.del(key)
      console.log(`🗑️ Redis cache DEL: ${key}`)
      return result > 0
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Set JSON object in Redis
   */
  async setJSON(key: string, value: any, ttlSeconds: number = 3600): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString, ttlSeconds)
    } catch (error) {
      console.error(`Redis setJSON error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Get JSON object from Redis
   */
  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      if (!value) return null
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`Redis getJSON error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Check if key exists in Redis
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isAvailable()) return false

    try {
      const result = await this.client!.exists(key)
      return result === 1
    } catch (error) {
      console.error(`Redis EXISTS error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Set multiple values at once (pipeline)
   */
  async mset(keyValuePairs: Record<string, string>): Promise<boolean> {
    if (!this.isAvailable()) return false

    try {
      const pipeline = this.client!.multi()
      Object.entries(keyValuePairs).forEach(([key, value]) => {
        pipeline.set(key, value)
      })
      await pipeline.exec()
      console.log(`📦 Redis bulk SET: ${Object.keys(keyValuePairs).length} keys`)
      return true
    } catch (error) {
      console.error('Redis MSET error:', error)
      return false
    }
  }

  /**
   * Get multiple values at once
   */
  async mget(keys: string[]): Promise<Record<string, string | null>> {
    if (!this.isAvailable()) return {}

    try {
      const values = await this.client!.mGet(keys)
      const result: Record<string, string | null> = {}
      keys.forEach((key, index) => {
        result[key] = values[index]
      })
      console.log(`📦 Redis bulk GET: ${keys.length} keys`)
      return result
    } catch (error) {
      console.error('Redis MGET error:', error)
      return {}
    }
  }

  /**
   * Clear all cache (use with caution)
   */
  async flushAll(): Promise<boolean> {
    if (!this.isAvailable()) return false

    try {
      await this.client!.flushAll()
      console.log('🧹 Redis cache cleared')
      return true
    } catch (error) {
      console.error('Redis FLUSHALL error:', error)
      return false
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    if (!this.isAvailable()) return null

    try {
      const info = await this.client!.info('memory')
      return {
        connected: this.isConnected,
        memory: info,
        keyspace: await this.client!.info('keyspace')
      }
    } catch (error) {
      console.error('Redis STATS error:', error)
      return null
    }
  }

  /**
   * Disconnect from Redis
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.quit()
      this.isConnected = false
      this.connectionPromise = null
      console.log('👋 Redis disconnected')
    }
  }
}

// Export singleton instance
export const redisManager = RedisManager.getInstance()
export default redisManager
