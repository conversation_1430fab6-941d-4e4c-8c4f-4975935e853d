#!/bin/bash

echo "🔍 Verifying Billiard Dashboard Database..."

# Test database connection
if PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Check tables exist
echo ""
echo "📋 Checking database tables..."

TABLES=(
    "games"
    "orders"
    "menu_items"
    "receipts"
    "tables"
    "gametables"
    "categories"
    "businessinfo"
)

for table in "${TABLES[@]}"; do
    if PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "\dt $table" > /dev/null 2>&1; then
        COUNT=$(PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM $table;" | xargs)
        echo "✅ $table ($COUNT records)"
    else
        echo "❌ $table (missing)"
    fi
done

echo ""
echo "�️ Bar Tables in Database:"
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "
SELECT
    id,
    number,
    name,
    CASE WHEN is_active THEN 'Active' ELSE 'Inactive' END as status,
    hourly_rate || ' L/hour' as rate
FROM tables
ORDER BY number;
"

echo ""
echo "🎱 Game Tables in Database:"
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "
SELECT
    id,
    number,
    name,
    CASE WHEN is_active THEN 'Active' ELSE 'Inactive' END as status,
    hourly_rate || ' L/hour' as rate,
    table_type
FROM gametables
ORDER BY number;
"

echo ""
echo "🍺 Menu Categories:"
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "
SELECT
    name,
    key,
    CASE WHEN is_active THEN 'Active' ELSE 'Inactive' END as status,
    display_order
FROM categories
ORDER BY display_order;
"

echo ""
echo "🏢 Business Information:"
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "
SELECT
    name,
    address,
    phone,
    email
FROM businessinfo
LIMIT 1;
"
