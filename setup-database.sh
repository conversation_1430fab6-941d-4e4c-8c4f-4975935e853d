#!/bin/bash

echo "🎱 Setting up Billiard Dashboard Database..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if PostgreSQL container is running
if ! docker ps | grep -q postgres; then
    echo "🐳 Starting PostgreSQL container..."
    docker run --name billiard-postgres -e POSTGRES_PASSWORD=postgres123 -p 5431:5432 -d postgres:15
    echo "⏳ Waiting for PostgreSQL to start..."
    sleep 10
else
    echo "✅ PostgreSQL container is already running"
fi

# Test database connection
echo "🔌 Testing database connection..."
if PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed. Please check your PostgreSQL container."
    exit 1
fi

# Create tables
echo "📋 Creating database tables..."
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -f schema.sql

if [ $? -eq 0 ]; then
    echo "✅ Database tables created successfully"
else
    echo "❌ Failed to create database tables"
    exit 1
fi

# Insert initial data
echo "📊 Inserting initial data..."
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres << EOF
-- Insert some initial menu items
INSERT INTO menu_items (name, price, category, available) VALUES
('Espresso', 120, 'coffee', true),
('Cappuccino', 150, 'coffee', true),
('Latte', 160, 'coffee', true),
('Americano', 130, 'coffee', true),
('Coca Cola', 100, 'soft_drinks', true),
('Water', 80, 'soft_drinks', true),
('Orange Juice', 120, 'soft_drinks', true),
('Energy Drink', 150, 'soft_drinks', true),
('Beer', 200, 'alcohol', true),
('Wine Glass', 300, 'alcohol', true),
('Whiskey', 500, 'alcohol', true),
('Chips', 150, 'snacks', true),
('Nuts', 200, 'snacks', true),
('Sandwich', 350, 'snacks', true)
ON CONFLICT DO NOTHING;

-- Insert default tables
INSERT INTO tables (number, name, is_active, hourly_rate) VALUES
(1, 'Table 1', true, 400),
(2, 'Table 2', true, 400),
(3, 'Table 3', true, 400),
(4, 'Table 4', true, 400),
(5, 'Table 5', true, 400),
(6, 'Table 6', true, 400),
(7, 'Table 7', true, 400),
(8, 'Table 8', true, 400)
ON CONFLICT (number) DO NOTHING;

-- Insert default categories
INSERT INTO categories (name, key, icon, is_active, display_order) VALUES
('Coffee', 'coffee', 'Coffee', true, 1),
('Soft Drinks', 'soft_drinks', 'Coffee', true, 2),
('Alcohol', 'alcohol', 'Coffee', true, 3),
('Snacks', 'snacks', 'Coffee', true, 4)
ON CONFLICT (key) DO NOTHING;

-- Insert default business info
INSERT INTO businessinfo (name, address, phone, email, vat_number) VALUES
('Billiard Club', 'Tirana, Albania', '+355 69 123 4567', '<EMAIL>', 'AL123456789')
ON CONFLICT DO NOTHING;

-- Insert default users (for demo purposes with simple passwords)
INSERT INTO users (username, password_hash, role, full_name, is_active) VALUES
('admin', 'admin123', 'admin', 'Administrator', true),
('waiter1', 'waiter1', 'waiter', 'Waiter One', true),
('waiter2', 'waiter2', 'waiter', 'Waiter Two', true)
ON CONFLICT (username) DO NOTHING;
EOF

if [ $? -eq 0 ]; then
    echo "✅ Initial data inserted successfully"
else
    echo "❌ Failed to insert initial data"
    exit 1
fi

# Verify setup
echo "🔍 Verifying database setup..."
TABLES_COUNT=$(PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM tables;")
MENU_COUNT=$(PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM menu_items;")
CATEGORIES_COUNT=$(PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM categories;")
USERS_COUNT=$(PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -t -c "SELECT COUNT(*) FROM users;")

echo "📊 Database Summary:"
echo "   - Tables: $TABLES_COUNT"
echo "   - Menu Items: $MENU_COUNT"
echo "   - Categories: $CATEGORIES_COUNT"
echo "   - Users: $USERS_COUNT"

echo ""
echo "🎉 Database setup completed successfully!"
echo "🔗 Connection string: postgresql://postgres:postgres123@localhost:5431/postgres"
echo ""
echo "You can now start your Next.js application with: npm run dev"
