// Simple script to run the database migration
const fetch = require('node-fetch');

async function runMigration() {
  try {
    console.log('Running database migration...');
    
    const response = await fetch('http://localhost:3000/api/migrate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();
    console.log('Migration result:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Migration completed successfully!');
    } else {
      console.log('❌ Migration failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Error running migration:', error.message);
  }
}

runMigration();
