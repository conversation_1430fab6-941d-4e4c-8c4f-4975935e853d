const { Pool } = require('pg')

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: 'postgres123',
  port: 5431,
})

async function fixTableAssignments() {
  try {
    console.log('🔧 Fixing table assignments...\n')
    
    // Step 1: Get all users
    const users = await pool.query('SELECT id, username, full_name FROM users ORDER BY username')
    console.log('👥 Users found:')
    users.rows.forEach(user => {
      console.log(`  - ${user.username} (${user.full_name}) - ID: ${user.id}`)
    })
    console.log()
    
    // Step 2: Check current Table 1 assignments
    const currentT1s = await pool.query(`
      SELECT id, number, name, assigned_user_id, assigned_username, is_active
      FROM gametables 
      WHERE number = 1
      ORDER BY assigned_username
    `)
    
    console.log('🎮 Current Table 1 assignments:')
    if (currentT1s.rows.length === 0) {
      console.log('  ❌ No Table 1 entries found')
    } else {
      currentT1s.rows.forEach(table => {
        const assignment = table.assigned_username || 'Unassigned'
        const status = table.is_active ? 'Active' : 'Inactive'
        console.log(`  - Table 1 (ID: ${table.id}) → ${assignment} (${status})`)
      })
    }
    console.log()
    
    // Step 3: Find unassigned Table 1s
    const unassignedT1s = await pool.query(`
      SELECT id, number, name, assigned_user_id, assigned_username, is_active
      FROM gametables 
      WHERE number = 1 AND assigned_user_id IS NULL
      ORDER BY id
    `)
    
    console.log(`🔓 Found ${unassignedT1s.rows.length} unassigned Table 1 entries`)
    
    // Step 4: Assign Table 1 to each user
    let assignmentsMade = 0
    
    for (const user of users.rows) {
      // Check if user already has a Table 1
      const existingT1 = await pool.query(`
        SELECT id FROM gametables 
        WHERE number = 1 AND assigned_user_id = $1
      `, [user.id])
      
      if (existingT1.rows.length === 0) {
        console.log(`🔧 ${user.username} needs Table 1...`)
        
        if (unassignedT1s.rows.length > assignmentsMade) {
          // Assign existing unassigned table
          const tableToAssign = unassignedT1s.rows[assignmentsMade]
          
          await pool.query(`
            UPDATE gametables 
            SET assigned_user_id = $1, 
                assigned_username = $2,
                is_active = true,
                name = $3
            WHERE id = $4
          `, [user.id, user.username, `Table 1`, tableToAssign.id])
          
          console.log(`  ✅ Assigned existing Table 1 (ID: ${tableToAssign.id}) to ${user.username}`)
          assignmentsMade++
        } else {
          // Create new Table 1
          const newTable = await pool.query(`
            INSERT INTO gametables (number, name, table_type, hourly_rate, is_active, assigned_user_id, assigned_username)
            VALUES (1, 'Table 1', 'billiard', 400.00, true, $1, $2)
            RETURNING id
          `, [user.id, user.username])
          
          console.log(`  ✅ Created new Table 1 (ID: ${newTable.rows[0].id}) for ${user.username}`)
        }
      } else {
        console.log(`  ✅ ${user.username} already has Table 1 (ID: ${existingT1.rows[0].id})`)
      }
    }
    
    console.log()
    
    // Step 5: Final verification
    const finalT1s = await pool.query(`
      SELECT id, number, name, assigned_user_id, assigned_username, is_active
      FROM gametables 
      WHERE number = 1 AND assigned_user_id IS NOT NULL
      ORDER BY assigned_username
    `)
    
    console.log('🎯 Final Table 1 assignments:')
    finalT1s.rows.forEach(table => {
      const status = table.is_active ? 'Active' : 'Inactive'
      console.log(`  ✅ Table 1 (ID: ${table.id}) → ${table.assigned_username} (${status})`)
    })
    
    console.log()
    console.log('📊 Summary:')
    console.log(`  - Total users: ${users.rows.length}`)
    console.log(`  - Assignments made: ${assignmentsMade}`)
    console.log(`  - Final Table 1 count: ${finalT1s.rows.length}`)
    
    if (finalT1s.rows.length === users.rows.length) {
      console.log('  ✅ Perfect! Each user now has their own Table 1')
    } else {
      console.log('  ⚠️  Some users may still be missing Table 1')
    }
    
  } catch (error) {
    console.error('❌ Error during fix:', error)
  } finally {
    await pool.end()
  }
}

fixTableAssignments()
