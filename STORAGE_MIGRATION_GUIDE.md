# Storage System Migration Guide

This guide explains how to migrate from the old storage system to the new unified storage system with security, performance, and scalability improvements.

## Overview of Changes

### 🔒 Security Improvements
- **HttpOnly Cookies**: Authentication now uses secure HttpOnly cookies instead of sessionStorage
- **No Client-Side Tokens**: JWT tokens are no longer stored in client-side storage
- **Storage Rules Enforcement**: Prevents auth data from being stored in localStorage/sessionStorage

### ⚡ Performance Improvements
- **Debounced Operations**: All database writes are debounced to prevent excessive API calls
- **Memory Cache**: Fast in-memory storage for temporary data
- **Fallback Chain**: Automatic fallback from memory → localStorage → database

### 📈 Scalability Improvements
- **Unified Interface**: Single interface for all storage operations
- **Auto Storage Selection**: Automatically routes data to appropriate storage based on type
- **Database Storage**: Persistent storage in PostgreSQL for cross-device sync

## Migration Steps

### 1. Update Imports

**Before:**
```typescript
import { storageManager } from '../utils/storageManager'
import { dbStorageManager } from '../utils/databaseStorageManager'
```

**After:**
```typescript
import { 
  getStorageManager, 
  setUIPreference, 
  getUIPreference, 
  setTempData, 
  getTempData,
  setUserSetting,
  getUserSetting,
  configureStorage
} from '../utils/storage'
import { debounce } from '../utils/debounce'
```

### 2. Configure Storage with Auth Token

**Before:**
```typescript
// Manual token management
dbStorageManager.setAuthToken(token)
```

**After:**
```typescript
useEffect(() => {
  if (token) {
    configureStorage({ authToken: token })
  }
}, [token])
```

### 3. Replace Storage Operations

#### UI Preferences (localStorage)

**Before:**
```typescript
storageManager.setItem('theme', 'dark')
const theme = storageManager.getItem('theme')
```

**After:**
```typescript
await setUIPreference('theme', 'dark')
const theme = await getUIPreference('theme', 'light') // with default
```

#### Temporary Data (memory)

**Before:**
```typescript
// No equivalent - was mixed with localStorage
```

**After:**
```typescript
await setTempData('draft_order_1', orderData)
const orderData = await getTempData('draft_order_1')
```

#### User Settings (database with fallback)

**Before:**
```typescript
dbStorageManager.setUserPreference('selected_table', '1')
const table = dbStorageManager.getCachedPreference('selected_table')
```

**After:**
```typescript
await setUserSetting('selected_table', 1)
const table = await getUserSetting('selected_table')
```

### 4. Add Debouncing for Performance

**Before:**
```typescript
useEffect(() => {
  // Immediate save on every change
  dbStorageManager.setUserPreference('search_query', searchQuery)
}, [searchQuery])
```

**After:**
```typescript
const debouncedSave = debounce(async (key: string, value: any) => {
  await setUserSetting(key, value)
}, 500) // 500ms debounce

useEffect(() => {
  if (searchQuery) {
    debouncedSave('search_query', searchQuery)
  }
}, [searchQuery, debouncedSave])
```

### 5. Update Authentication Context

**Before (sessionStorage):**
```typescript
sessionStorage.setItem('auth_token', token)
sessionStorage.setItem('user', JSON.stringify(user))
```

**After (HttpOnly cookies):**
```typescript
// Cookies are set automatically by the server
// No client-side storage needed
```

### 6. Handle Storage Rules

The new system enforces storage rules automatically:

- **Auth/session data** → HttpOnly cookies only (enforced)
- **UI preferences** → LocalStorage
- **Temporary data** → Memory
- **Persistent state** → Database (if authenticated)

**Example:**
```typescript
// These will automatically route to the correct storage
await setUIPreference('theme', 'dark')           // → localStorage
await setTempData('draft_order', data)           // → memory
await setUserSetting('notification_prefs', prefs) // → database
```

## Storage Types and When to Use

### Memory Storage (`getStorageManager('memory')`)
- **Use for**: Draft orders, form state, temporary UI data
- **Characteristics**: Fast, lost on page refresh
- **Example**: `setTempData('draft_order_1', orderData)`

### Local Storage (`getStorageManager('local')`)
- **Use for**: UI preferences, theme, non-sensitive settings
- **Characteristics**: Persists across sessions, client-side only
- **Example**: `setUIPreference('theme', 'dark')`

### Database Storage (`getStorageManager('database')`)
- **Use for**: User preferences, persistent state, cross-device sync
- **Characteristics**: Requires authentication, syncs across devices
- **Example**: `setUserSetting('notification_prefs', prefs)`

## Fallback Chain

The `getWithFallback()` function tries storage in this order:
1. **Memory** (fastest)
2. **LocalStorage** (persistent, client-side)
3. **Database** (persistent, server-side)

If data is found in a lower-tier storage, it's automatically synced upward for faster future access.

## Error Handling

The new system includes comprehensive error handling:

```typescript
const result = await getWithFallback('user_preference')
if (result.success) {
  console.log('Value:', result.data)
  console.log('Source:', result.source) // 'memory', 'local', or 'database'
} else {
  console.error('Error:', result.error)
}
```

## Debugging

Use the storage statistics function to debug storage usage:

```typescript
import { getStorageStats } from '../utils/storage'

const stats = await getStorageStats()
console.log('Storage Statistics:', stats)
```

## Files to Remove After Migration

Once migration is complete, these files can be removed:
- `app/utils/storageManager.ts`
- `app/utils/databaseStorageManager.ts`
- `app/utils/noStorageManager.ts`
- `app/utils/cacheManager.ts`

## Database Migration

Run the database migration to create the new storage table:

```bash
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -f database/migrations/create_user_storage_table.sql
```

This will:
- Create the `user_storage` table
- Migrate existing data from `user_preferences` and `draft_orders` tables
- Set up cleanup functions for old data

## Testing

After migration, test these scenarios:
1. **Authentication**: Login/logout with cookies
2. **UI Preferences**: Theme changes, category selection
3. **Draft Orders**: Create, modify, and submit orders
4. **Cross-Device Sync**: Login from different devices
5. **Offline Behavior**: Fallback to localStorage when database unavailable
