-- Create login_settings table
CREATE TABLE IF NOT EXISTS login_settings (
    id SERIAL PRIMARY KEY,
    waiters_section_enabled BOOLEAN NOT NULL DEFAULT true,
    waiters_section_title VARCHAR(100) NOT NULL DEFAULT 'Waiters Accounts:',
    waiter1_display_name VARCHAR(100) NOT NULL DEFAULT 'Waiter One:',
    waiter1_username VARCHAR(50) NOT NULL DEFAULT 'waiter1',
    waiter1_password VARCHAR(50) NOT NULL DEFAULT 'waiter1',
    waiter1_enabled BOOLEAN NOT NULL DEFAULT true,
    waiter2_display_name VARCHAR(100) NOT NULL DEFAULT 'Waiter Two:',
    waiter2_username VARCHAR(50) NOT NULL DEFAULT 'waiter2',
    waiter2_password VARCHAR(50) NOT NULL DEFAULT 'waiter2',
    waiter2_enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings if table is empty
INSERT INTO login_settings (
    waiters_section_enabled,
    waiters_section_title,
    waiter1_display_name,
    waiter1_username,
    waiter1_password,
    waiter1_enabled,
    waiter2_display_name,
    waiter2_username,
    waiter2_password,
    waiter2_enabled
) 
SELECT 
    true,
    'Waiters Accounts:',
    'Waiter One:',
    'waiter1',
    'waiter1',
    true,
    'Waiter Two:',
    'waiter2',
    'waiter2',
    true
WHERE NOT EXISTS (SELECT 1 FROM login_settings);
