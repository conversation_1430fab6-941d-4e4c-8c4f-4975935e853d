-- Create game_settings table
CREATE TABLE IF NOT EXISTS game_settings (
    id SERIAL PRIMARY KEY,
    default_hourly_rate INTEGER NOT NULL DEFAULT 400,
    minimum_game_time INTEGER NOT NULL DEFAULT 15,
    auto_end_after_hours INTEGER NOT NULL DEFAULT 8,
    auto_print_receipts BOOLEAN NOT NULL DEFAULT true,
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 20.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default game settings if none exist
INSERT INTO game_settings (default_hourly_rate, minimum_game_time, auto_end_after_hours, auto_print_receipts, tax_rate)
SELECT 400, 15, 8, true, 20.00
WHERE NOT EXISTS (SELECT 1 FROM game_settings);
