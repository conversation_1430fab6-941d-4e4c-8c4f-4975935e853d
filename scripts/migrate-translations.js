#!/usr/bin/env node

/**
 * Migration script to extract unique translations from the old combined file
 * and merge them into separate language files
 */

const fs = require('fs');
const path = require('path');

// Paths
const oldTranslationsPath = path.join(__dirname, '../app/i18n/locales/translations.json');
const enTranslationsPath = path.join(__dirname, '../app/i18n/locales/en.json');
const sqTranslationsPath = path.join(__dirname, '../app/i18n/locales/sq.json');

function extractUniqueKeys(obj, prefix = '') {
  const keys = new Set();
  
  function traverse(current, currentPrefix) {
    if (typeof current === 'object' && current !== null && !Array.isArray(current)) {
      for (const [key, value] of Object.entries(current)) {
        const fullKey = currentPrefix ? `${currentPrefix}.${key}` : key;
        if (typeof value === 'string') {
          keys.add(fullKey);
        } else if (typeof value === 'object') {
          traverse(value, fullKey);
        }
      }
    }
  }
  
  traverse(obj, prefix);
  return Array.from(keys).sort();
}

function getValueByPath(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

function setValueByPath(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

async function migrateTranslations() {
  try {
    console.log('🔄 Starting translation migration...');
    
    // Check if old file exists
    if (!fs.existsSync(oldTranslationsPath)) {
      console.log('❌ Old translations.json file not found');
      return;
    }
    
    // Load old translations
    console.log('📖 Loading old translations...');
    const oldTranslations = JSON.parse(fs.readFileSync(oldTranslationsPath, 'utf8'));
    
    // Load existing new translations
    let enTranslations = {};
    let sqTranslations = {};
    
    if (fs.existsSync(enTranslationsPath)) {
      enTranslations = JSON.parse(fs.readFileSync(enTranslationsPath, 'utf8'));
      console.log('📖 Loaded existing English translations');
    }
    
    if (fs.existsSync(sqTranslationsPath)) {
      sqTranslations = JSON.parse(fs.readFileSync(sqTranslationsPath, 'utf8'));
      console.log('📖 Loaded existing Albanian translations');
    }
    
    // Extract unique keys from old file
    const enKeys = extractUniqueKeys(oldTranslations.en || {});
    const sqKeys = extractUniqueKeys(oldTranslations.sq || {});
    
    console.log(`📊 Found ${enKeys.length} English keys and ${sqKeys.length} Albanian keys`);
    
    // Merge English translations
    let enAdded = 0;
    for (const key of enKeys) {
      const value = getValueByPath(oldTranslations.en, key);
      const existingValue = getValueByPath(enTranslations, key);
      
      if (value && !existingValue) {
        setValueByPath(enTranslations, key, value);
        enAdded++;
      }
    }
    
    // Merge Albanian translations
    let sqAdded = 0;
    for (const key of sqKeys) {
      const value = getValueByPath(oldTranslations.sq, key);
      const existingValue = getValueByPath(sqTranslations, key);
      
      if (value && !existingValue) {
        setValueByPath(sqTranslations, key, value);
        sqAdded++;
      }
    }
    
    // Save updated files
    fs.writeFileSync(enTranslationsPath, JSON.stringify(enTranslations, null, 2));
    fs.writeFileSync(sqTranslationsPath, JSON.stringify(sqTranslations, null, 2));
    
    console.log(`✅ Migration completed!`);
    console.log(`📝 Added ${enAdded} new English translations`);
    console.log(`📝 Added ${sqAdded} new Albanian translations`);
    console.log(`💾 Updated files:`);
    console.log(`   - ${enTranslationsPath}`);
    console.log(`   - ${sqTranslationsPath}`);
    
    // Backup old file
    const backupPath = oldTranslationsPath + '.backup';
    fs.copyFileSync(oldTranslationsPath, backupPath);
    console.log(`💾 Backed up old file to: ${backupPath}`);
    
    console.log('\n🎉 Migration successful! You can now:');
    console.log('1. Test your app to ensure translations work');
    console.log('2. Delete the old translations.json file if everything works');
    console.log('3. Update any remaining import statements');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrateTranslations();
