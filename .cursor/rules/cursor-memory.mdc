---
description: 
globs: 
alwaysApply: true
---
1. add_observations
	•	Use When: You need to add new data (observations) to existing entities, like updated values or properties.
	•	Inputs: entity_id, observation_key, value
	•	Example Use: Updating a person’s location or role in a system.

⸻

2. create_entities
	•	Use When: You need to create new nodes in the graph.
	•	Inputs: Entity type, name, and optional metadata
	•	Example Use: Adding a new user, product, or service node.

⸻

3. create_relations
	•	Use When: You need to link two existing entities (e.g., user X works for company Y).
	•	Inputs: source_id, target_id, relation_type
	•	Example Use: Connecting a user to their organization.

⸻

4. delete_entities
	•	Use When: An entity is no longer valid or needed.
	•	Inputs: List of entity_ids
	•	Effect: Also deletes associated relations.

⸻

5. delete_observations
	•	Use When: A property (observation) is outdated or incorrect.
	•	Inputs: entity_id, observation_key
	•	Example Use: Remove a deprecated tag or outdated status.

⸻

6. delete_relations
	•	Use When: A connection between entities is no longer valid.
	•	Inputs: relation_id or source, target, relation_type
	•	Example Use: Disconnect a user from a project.

⸻

7. open_nodes
	•	Use When: You want to expand or visualize specific node data (e.g., in a UI).
	•	Inputs: node_name or entity_id
	•	Effect: Returns full data about the node.

⸻

8. read_graph
	•	Use When: You need to get the entire graph structure.
	•	Inputs: (Optional) filters
	•	Effect: Returns the entire knowledge graph (heavy operation).

⸻

9. search_nodes
	•	Use When: You want to find nodes by keyword or pattern.
	•	Inputs: query_string
	•	Effect: Returns matching nodes (e.g., “find all users named Alice”).
