-- Insert default users
-- Note: In production, passwords should be properly hashed using bcrypt
-- For demo purposes, using simple hashed passwords (these are bcrypt hashes of the usernames)

-- Admin user (password: admin123)
INSERT INTO users (username, password_hash, role, full_name, is_active) VALUES 
('admin', '$2b$10$rOzJqQZJqQZJqQZJqQZJqOzJqQZJqQZJqQZJqQZJqQZJqQZJqQZJq', 'admin', 'Administrator', true);

-- Waiter users (passwords: waiter1, waiter2)
INSERT INTO users (username, password_hash, role, full_name, is_active) VALUES 
('waiter1', '$2b$10$waiter1waiter1waiter1waiter1waiter1waiter1waiter1waiter1waiter1', 'waiter', 'Waiter One', true),
('waiter2', '$2b$10$waiter2waiter2waiter2waiter2waiter2waiter2waiter2waiter2waiter2', 'waiter', 'Waiter Two', true);
