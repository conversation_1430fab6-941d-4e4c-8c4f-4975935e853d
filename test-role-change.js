// Test script to verify admin role change functionality
const testRoleChange = async () => {
  try {
    console.log('🧪 Testing admin role change functionality...')
    
    // First, let's check the current user profile
    const profileResponse = await fetch('http://localhost:3000/api/user-profile', {
      credentials: 'include'
    })
    
    if (profileResponse.ok) {
      const profile = await profileResponse.json()
      console.log('✅ Current user profile:', profile)
    } else {
      console.log('❌ Failed to get user profile:', profileResponse.status)
      return
    }
    
    // Test the admin users API
    const usersResponse = await fetch('http://localhost:3000/api/admin/users', {
      credentials: 'include'
    })
    
    if (usersResponse.ok) {
      const users = await usersResponse.json()
      console.log('✅ Users list:', users)
      
      // Find Gesti user
      const gesti = users.find(u => u.username === 'gesti')
      if (gesti) {
        console.log('👤 Found Gesti:', gesti)
        
        // Try to change <PERSON><PERSON><PERSON>'s role to waiter
        const updateResponse = await fetch('http://localhost:3000/api/admin/users', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            userId: gesti.id,
            username: gesti.username,
            fullName: gesti.fullName,
            role: 'waiter',
            isActive: true
          })
        })
        
        if (updateResponse.ok) {
          const updatedUser = await updateResponse.json()
          console.log('🎉 SUCCESS! Role changed successfully:', updatedUser)
        } else {
          const error = await updateResponse.text()
          console.log('❌ Failed to change role:', updateResponse.status, error)
        }
      } else {
        console.log('❌ Gesti user not found')
      }
    } else {
      const error = await usersResponse.text()
      console.log('❌ Failed to get users:', usersResponse.status, error)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testRoleChange()
