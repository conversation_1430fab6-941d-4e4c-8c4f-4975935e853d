#!/bin/bash

# Connect to PostgreSQL and create tables
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres -f schema.sql

# Insert some initial menu items
PGPASSWORD=postgres123 psql -h localhost -p 5431 -U postgres -d postgres << EOF
INSERT INTO menu_items (name, price, category, available) VALUES
('Espresso', 120, 'coffee', true),
('Cappuccino', 150, 'coffee', true),
('Latte', 160, 'coffee', true),
('Americano', 130, 'coffee', true),
('Coca Cola', 100, 'soft_drinks', true),
('Water', 80, 'soft_drinks', true),
('Orange Juice', 120, 'soft_drinks', true),
('Energy Drink', 150, 'soft_drinks', true),
('Beer', 200, 'alcohol', true),
('Wine Glass', 300, 'alcohol', true),
('Whiskey', 500, 'alcohol', true),
('Chips', 150, 'snacks', true),
('Nuts', 200, 'snacks', true),
('Sandwich', 350, 'snacks', true);

-- Insert default bar tables
INSERT INTO tables (number, name, is_active, hourly_rate) VALUES
(1, 'Bar Table 1', true, 0),
(2, 'Bar Table 2', true, 0),
(3, 'Bar Table 3', true, 0),
(4, 'Bar Table 4', true, 0)
ON CONFLICT (number) DO NOTHING;

-- Insert default game tables
INSERT INTO gametables (number, name, is_active, hourly_rate, table_type) VALUES
(1, 'Billiard Table 1', true, 400, 'billiard'),
(2, 'Billiard Table 2', true, 400, 'billiard'),
(3, 'Billiard Table 3', true, 400, 'billiard'),
(4, 'Billiard Table 4', true, 400, 'billiard'),
(5, 'Billiard Table 5', true, 400, 'billiard'),
(6, 'Billiard Table 6', true, 400, 'billiard'),
(7, 'Billiard Table 7', true, 400, 'billiard'),
(8, 'Billiard Table 8', true, 400, 'billiard')
ON CONFLICT (number) DO NOTHING;

-- Insert default categories
INSERT INTO categories (name, key, icon, is_active, display_order) VALUES
('Coffee', 'coffee', 'Coffee', true, 1),
('Soft Drinks', 'soft_drinks', 'Coffee', true, 2),
('Alcohol', 'alcohol', 'Coffee', true, 3),
('Snacks', 'snacks', 'Coffee', true, 4);

-- Insert default business info
INSERT INTO businessinfo (name, address, phone, email, vat_number) VALUES
('Billiard Club', 'Tirana, Albania', '+355 69 123 4567', '<EMAIL>', 'AL123456789');
EOF