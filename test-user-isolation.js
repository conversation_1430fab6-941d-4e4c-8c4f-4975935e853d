const { Pool } = require('pg')

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: 'postgres123',
  port: 5431,
})

async function testUserIsolation() {
  try {
    console.log('🔍 Testing User Table Isolation...\n')
    
    // Get all users
    const users = await pool.query('SELECT id, username, full_name FROM users ORDER BY username')
    console.log('👥 Users in system:')
    users.rows.forEach(user => {
      console.log(`  - ${user.username} (${user.full_name}) - ID: ${user.id}`)
    })
    console.log()
    
    // Check game tables by user
    console.log('🎮 Game Tables by User:')
    for (const user of users.rows) {
      const userTables = await pool.query(
        'SELECT * FROM gametables WHERE assigned_user_id = $1 ORDER BY number',
        [user.id]
      )
      
      console.log(`  ${user.username}:`)
      if (userTables.rows.length === 0) {
        console.log(`    ❌ No tables assigned`)
      } else {
        userTables.rows.forEach(table => {
          console.log(`    ✅ Table ${table.number} - "${table.name}" (ID: ${table.id})`)
        })
      }
    }
    console.log()
    
    // Check for duplicate table numbers across users
    console.log('🔍 Checking for Table Number Conflicts:')
    const tableNumbers = await pool.query(`
      SELECT 
        number,
        COUNT(*) as user_count,
        array_agg(assigned_username ORDER BY assigned_username) as users
      FROM gametables 
      WHERE assigned_user_id IS NOT NULL
      GROUP BY number
      HAVING COUNT(*) > 1
      ORDER BY number
    `)
    
    if (tableNumbers.rows.length === 0) {
      console.log('  ✅ No table number conflicts found')
    } else {
      console.log('  📊 Table numbers used by multiple users (this is OK):')
      tableNumbers.rows.forEach(conflict => {
        console.log(`    Table ${conflict.number}: Used by ${conflict.user_count} users (${conflict.users.join(', ')})`)
      })
    }
    console.log()
    
    // Check unassigned tables
    const unassigned = await pool.query('SELECT * FROM gametables WHERE assigned_user_id IS NULL ORDER BY number')
    console.log('🔓 Unassigned Tables:')
    if (unassigned.rows.length === 0) {
      console.log('  ✅ No unassigned tables (perfect isolation)')
    } else {
      console.log('  ⚠️  Found unassigned tables:')
      unassigned.rows.forEach(table => {
        console.log(`    Table ${table.number} - "${table.name}" (ID: ${table.id})`)
      })
    }
    console.log()
    
    // Test what each user would see with the new API
    console.log('🔬 Testing API Results for Each User:')
    for (const user of users.rows) {
      const apiResult = await pool.query(
        'SELECT * FROM gametables WHERE assigned_user_id = $1 ORDER BY number',
        [user.id]
      )
      
      console.log(`  ${user.username} would see:`)
      if (apiResult.rows.length === 0) {
        console.log(`    ❌ No tables (needs table assignment)`)
      } else {
        apiResult.rows.forEach(table => {
          console.log(`    ✅ Table ${table.number} - "${table.name}"`)
        })
      }
    }
    console.log()
    
    // Summary
    console.log('📋 Summary:')
    console.log(`  - Total users: ${users.rows.length}`)
    console.log(`  - Total game tables: ${(await pool.query('SELECT COUNT(*) FROM gametables')).rows[0].count}`)
    console.log(`  - Unassigned tables: ${unassigned.rows.length}`)
    console.log(`  - Users with tables: ${users.rows.filter(u => users.rows.some(user => user.id === u.id)).length}`)
    
    if (unassigned.rows.length === 0) {
      console.log('  ✅ Perfect isolation achieved!')
    } else {
      console.log('  ⚠️  Some tables are unassigned - assign them to users for complete isolation')
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error)
  } finally {
    await pool.end()
  }
}

testUserIsolation()
