'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function FixTableAssignmentsPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const fixAssignments = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔧 Fixing table assignments...')
      
      const response = await fetch('/api/fix-table-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const fixResult = await response.json()
        setResult({
          success: true,
          message: 'Table assignments fixed successfully!',
          fixResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Fix failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>🔧 Fix Table Assignments</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will ensure each user has their own assigned Table 1 so they can see it in the Active Tables view.
          </p>
          
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">🎯 What this fixes:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Assigns Table 1 to Administrator user</li>
              <li>• Assigns Table 1 to Gesti user</li>
              <li>• Assigns Table 1 to any other users</li>
              <li>• Makes all assigned tables active</li>
              <li>• Creates new Table 1 if needed</li>
              <li>• Ensures complete user isolation</li>
            </ul>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="font-semibold text-green-800 mb-2">✅ After the fix:</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Admin will see their own Table 1</li>
              <li>• Gesti will see their own Table 1</li>
              <li>• Each user's Table 1 is completely separate</li>
              <li>• Active Tables view will show correct tables</li>
              <li>• Perfect user isolation maintained</li>
            </ul>
          </div>
          
          <Button onClick={fixAssignments} disabled={loading} size="lg">
            {loading ? 'Fixing Table Assignments...' : 'Fix Table Assignments'}
          </Button>
          
          {result && (
            <div className="mt-4 space-y-4">
              {result.success && (
                <>
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <h3 className="font-semibold text-green-800 mb-2">✅ Fix Completed!</h3>
                    <div className="text-sm text-green-700 space-y-1">
                      <p>Total Users: {result.fixResult.summary?.totalUsers}</p>
                      <p>Assignments Made: {result.fixResult.summary?.assignmentsMade}</p>
                      <p>Final Table Count: {result.fixResult.summary?.finalTableCount}</p>
                    </div>
                  </div>

                  {result.fixResult.finalAssignments && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h3 className="font-semibold text-blue-800 mb-2">📊 Final Table Assignments:</h3>
                      <div className="space-y-1 text-sm">
                        {result.fixResult.finalAssignments.map((assignment: any, index: number) => (
                          <div key={index} className="flex justify-between">
                            <span>Table {assignment.number}</span>
                            <span>{assignment.assigned_username} ({assignment.count} table{assignment.count > 1 ? 's' : ''})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
              
              <details className="mt-4">
                <summary className="cursor-pointer font-medium">Detailed Results</summary>
                <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap bg-gray-100 p-4 rounded">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </details>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>Next steps after fix:</strong></p>
            <p>1. Go to Active Tables view</p>
            <p>2. Each user should now see their own Table 1</p>
            <p>3. Test starting games on Table 1 with different users</p>
            <p>4. Verify complete isolation between users</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
