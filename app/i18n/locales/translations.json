{"en": {"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "none": "None", "all": "All", "of": "of", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "price": "Price", "quantity": "Quantity", "amount": "Amount", "date": "Date", "time": "Time", "name": "Name", "description": "Description", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "pending": "Pending", "completed": "Completed", "failed": "Failed", "processing": "Processing", "cancelled": "Cancelled", "approved": "Approved", "rejected": "Rejected", "draft": "Draft", "published": "Published", "archived": "Archived", "new": "New", "updated": "Updated", "created": "Created", "modified": "Modified", "deleted": "Deleted", "removed": "Removed", "added": "Added", "changed": "Changed", "saved": "Saved", "loaded": "Loaded", "uploaded": "Uploaded", "downloaded": "Downloaded", "sent": "<PERSON><PERSON>", "received": "Received", "opened": "Opened", "closed": "Closed", "started": "Started", "stopped": "Stopped", "paused": "Paused", "resumed": "Resumed", "finished": "Finished", "expired": "Expired", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "required": "Required", "optional": "Optional", "public": "Public", "private": "Private", "visible": "Visible", "hidden": "Hidden", "locked": "Locked", "unlocked": "Unlocked", "free": "Free", "paid": "Paid", "premium": "Premium", "basic": "Basic", "advanced": "Advanced", "professional": "Professional", "enterprise": "Enterprise", "personal": "Personal", "business": "Business", "unlimited": "Unlimited", "limited": "Limited", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "automatic": "Automatic", "manual": "Manual", "high": "High", "medium": "Medium", "low": "Low", "urgent": "<PERSON><PERSON>", "normal": "Normal", "critical": "Critical", "major": "Major", "minor": "Minor", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "lastWeek": "Last Week", "nextWeek": "Next Week", "thisMonth": "This Month", "lastMonth": "Last Month", "nextMonth": "Next Month", "thisYear": "This Year", "lastYear": "Last Year", "nextYear": "Next Year", "now": "Now", "recently": "Recently", "soon": "Soon", "later": "Later", "never": "Never", "always": "Always", "sometimes": "Sometimes", "often": "Often", "rarely": "Rarely", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "hourly": "Hourly", "minutes": "Minutes", "hours": "Hours", "days": "Days", "weeks": "Weeks", "months": "Months", "years": "Years", "seconds": "Seconds", "minute": "Minute", "hour": "Hour", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "second": "Second", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "hoursShort": "h", "minutesShort": "m", "left": "left"}, "navigation": {"dashboard": "BBM", "games": "Games", "bar": "Bar", "analytics": "Analytics", "settings": "Settings", "logout": "Logout", "home": "Home", "menu": "<PERSON><PERSON>", "reports": "Reports", "users": "Users", "profile": "Profile", "help": "Help", "about": "About", "contact": "Contact", "support": "Support"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "email": "Email", "loginButton": "<PERSON><PERSON>", "logoutButton": "Logout", "loginError": "Invalid username or password", "sessionExpired": "Session expired. Please login again.", "waitersAccounts": "Waiter Accounts", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "signUp": "Sign Up", "signIn": "Sign In", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "resetPassword": "Reset Password", "changePassword": "Change Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "currentPassword": "Current Password", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters", "invalidEmail": "Invalid email address", "accountCreated": "Account created successfully", "passwordChanged": "Password changed successfully", "passwordReset": "Password reset successfully"}, "dashboard": {"title": "BBM", "welcome": "Welcome", "overview": "Overview", "todaysOrders": "Today's Orders", "todaysSessions": "Today's Sessions", "activeTables": "Active Tables", "recentActivity": "Recent Activity", "quickStats": "Quick Stats", "totalRevenue": "Total Revenue", "activeGames": "Active Billiard", "completedOrders": "Completed Orders", "totalSales": "Total Sales", "totalCustomers": "Total Customers", "averageOrderValue": "Average Order Value", "topSellingItems": "Top Selling Items", "recentTransactions": "Recent Transactions", "monthlyRevenue": "Monthly Revenue", "weeklyRevenue": "Weekly Revenue", "dailyRevenue": "Daily Revenue", "salesTrend": "Sales Trend", "customerSatisfaction": "Customer Satisfaction", "performanceMetrics": "Performance Metrics"}, "games": {"title": "Games", "activeTables": "Active Tables", "liveControlMonitoring": "Live control & monitoring", "startGame": "Start Game", "stopGame": "Stop Game", "stop": "STOP", "table": "Table", "tableName": "Table Name", "tableNumber": "Table {{number}}", "tableShort": "T", "duration": "Duration", "player": "Player", "price": "Price", "cost": "Cost", "time": "Time", "end": "End", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "now": "Now", "recently": "Recently", "unknown": "Unknown", "defaultTimeLimit": "Default Time Limit", "soundOn": "Sound On", "soundOff": "Sound Off", "makeAllAvailable": "Make All Available", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "todaysSessions": "Today's Sessions", "timeLimitReached": "Time Limit Reached", "timeLimitReachedBody": "One or more billiard sessions have reached their time limit.", "noGamesToday": "No billiard sessions today", "gameSessionReceipt": "Billiard Session Receipt", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax ({{rate}}.0%)", "total": "Total", "unlimited": "Unlimited", "oneHour": "1 Hour", "twoHours": "2 Hours", "threeHours": "3 Hours", "fourHours": "4 Hours", "fiveHours": "5 Hours", "sixHours": "6 Hours", "tables": "tables", "playing": "playing", "gamesCount": "sessions", "readyToPlay": "Ready to Play", "tapToStart": "Tap to Start", "perHour": "/hr", "change": "Change", "offline": "Offline", "contactStaff": "Contact Staff", "timesUp": "Time's Up!", "secondsLeft": "{{seconds}}s left", "minutesLeft": "{{minutes}}m left", "hoursMinutesLeft": "{{hours}}h {{minutes}}m left", "default": "<PERSON><PERSON><PERSON>", "noLimit": "No limit", "thankYouForPlaying": "Thank you for playing!", "visitUsAgainSoon": "Visit us again soon", "scanForMoreInfo": "Scan for more info", "scanForContactInfo": "Scan for contact info", "servedBy": "Served by", "limit": "limit", "live": "LIVE", "ready": "READY", "off": "OFF", "status": {"free": "Free", "occupied": "Occupied", "held": "Held"}, "albanianLek": "Albanian Lek", "billiardClub": "BILLIARD CLUB", "address": "Address", "vat": "VAT", "reportGenerated": "Report generated", "noGamesCompletedToday": "No games completed today", "noGamesCompletedThisMonth": "No games completed this month", "billiardGames": "BILLIARD GAMES", "gamesSubtotal": "Games Subtotal", "dailyTotal": "DAILY TOTAL", "monthlyTotal": "MONTHLY TOTAL", "refreshPage": "Refresh Page", "clearCache": "<PERSON>ache", "somethingWentWrong": "Something went wrong", "errorLoadingComponent": "There was an error loading this component. This is often caused by corrupted browser data.", "reloadPage": "Reload Page", "failedToInitialize": "Failed to initialize the application", "sine": "sine", "active": "active", "completed": "completed", "excellent": "Excellent", "good": "Good", "needsImprovement": "Needs Improvement", "high": "High", "medium": "Medium", "low": "Low", "growing": "Growing", "stable": "Stable", "auto": "Auto", "refresh": "Refresh", "today": "Today", "week": "Week", "month": "Month", "overview": "Overview", "revenue": "Revenue", "performance": "Performance", "insights": "Insights", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "monthlyRevenue": "Monthly Revenue", "revenueOverview": "Revenue Overview", "excellentDay": "🔥 Excellent day!", "growingTrend": "📈 Growing", "needsAttention": "📉 Needs attention", "optimalTiming": "⚡ Optimal timing", "roomForImprovement": "🎯 Room for improvement", "rushHourNow": "🔥 Rush Hour", "normalTime": "📊 Normal", "peakTimeNow": "⚡ Peak time now!", "planForPeak": "📈 Plan for peak", "noDataAvailableForTimeRange": "No {{timeRange}} data available", "dataWillAppearHere": "Data will appear here once transactions are recorded", "gamesIcon": "🎱 Games", "barIcon": "🍺 Bar", "barOrdersIcon": "🍺 Bar Orders", "revenueBreakdown": "💰 Revenue Breakdown", "revenueInsights": "Revenue Insights", "smartRecommendations": "Smart Recommendations", "businessHealthScore": "Business Health Score", "overallHealthScore": "Overall Health Score", "revenueTrend": "Revenue Trend", "efficiency": "Efficiency", "utilization": "Utilization", "peakPerformance": "⚡ Peak Performance", "mostUsedTable": "Most Used Table", "bestPerformer": "Best Performer", "opportunity": "Opportunity", "gamesAreDrivingRevenue": "🎱 Games are driving revenue today", "barSalesAreLeading": "🍺 Bar sales are leading today", "excellentPerformance": "Excellent Performance!", "revenueIsUp": "Revenue is up {changeText}. Keep up the great work!", "optimizationOpportunity": "Optimization Opportunity", "considerOptimizingGameDurations": "Consider optimizing game durations to improve table turnover.", "marketingSuggestion": "Marketing Suggestion", "tableUtilizationIsLow": "Table utilization is low. Consider running promotions during off-peak hours.", "topItem": "Top Item", "peakHour": "Peak Hour", "avgDurationMin": "Avg Duration (min)", "gamesToday": "Games Today", "ordersToday": "Orders Today", "avgOrder": "Avg Order", "billiard": "<PERSON><PERSON><PERSON>"}, "bar": {"title": "Bar", "selectTable": "Select Table", "menu": "<PERSON><PERSON>", "addToOrder": "Add to Order", "placeOrder": "Place Order", "orderTotal": "Order Total", "onHold": "on hold", "clearAll": "Clear All", "pending": "Pending", "clear": "Clear", "selectTableToOrder": "Select a table to start ordering", "tablesOnHold": "tables on hold", "currentOrder": "Current Order", "submitOrderAndPrint": "Submit Order & Print", "todaysOrders": "Today's Orders", "noOrdersToday": "No orders today", "noOrdersCompletedToday": "No orders completed today", "smartBar": "Smart Bar", "activeTables": "active tables", "utilization": "utilization", "smartMenu": "Smart Menu", "items": "items", "reports": "Daily Reports", "monthlyReports": "Monthly Reports", "smartAnalytics": "Smart Analytics", "realTimeInsights": "Real-time insights", "lastUpdated": "Last updated", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "searchMenuItems": "Search menu items...", "all": "All", "quick": "Quick", "popular": "Popular", "recent": "Recent", "selectTableToStartOrdering": "Select a Table", "chooseTableToStartOrdering": "Choose a table to start ordering", "chooseFromAvailableTables": "Choose from {{count}} available tables above", "noItemsFound": "No items found", "noItemsMatch": "No items match \"{{query}}\"", "noItemsInCategory": "No items in this category", "clearSearch": "Clear search", "noMenuItems": "No menu items", "addProductsInSettings": "Add products in Settings", "addItemsFromMenu": "Add items from the menu", "tableReady": "Table {{number}} is ready", "each": "each", "avgPerItem": "Avg {{amount}}L/item", "table": "Table", "unknown": "Unknown", "item": "item", "avgItems": "avg items", "noTablesAvailable": "No Tables Available", "noTablesAssigned": "No tables are assigned to your user or available for use.", "checkTablesInSettings": "Check if you have tables assigned to your user in Settings", "ensureTablesActive": "Ensure tables are marked as \"Active\"", "contactAdminForTables": "Contact admin to assign tables to your account", "daily": "Daily", "monthly": "Monthly", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "chooseWhatToInclude": "Choose what to include in the {{reportType}} report:", "gamesOnly": "Games Only", "ordersOnly": "Orders Only", "bothGamesAndBar": "Both Games and Bar", "cancel": "Cancel", "generateReport": "Generate Report", "barOrderReceipt": "Bar Order Receipt", "receipt": "Receipt", "date": "Date", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax", "total": "Total", "thankYouForOrder": "Thank you for your order!", "enjoyYourDrinks": "Enjoy your drinks!", "scanForMoreInfo": "Scan for more information", "scanForContactInfo": "Scan for contact information", "drinks": "Drinks", "snacks": "Snacks", "food": "Food", "albanianLek": "Albanian Lek", "completed": "completed", "selectTableFirst": "Select a table first", "noItemsInOrder": "No items in order", "orderDetails": "Order Details", "printReceipt": "Print Receipt", "gamesReportOnly": "Billiard Report Only", "barReportOnly": "Bar Report Only", "dailyBarReport": "Daily Bar Report", "monthlyBarReport": "Monthly Bar Report", "billiardGames": "BILLIARD GAMES", "barOrders": "BAR ORDERS", "noGamesCompletedToday": "No games completed today", "noGamesCompletedThisMonth": "No games completed this month", "noOrdersCompletedThisMonth": "No orders completed this month", "gamesSubtotal": "Games Subtotal:", "ordersSubtotal": "Orders Subtotal:", "dailyTotal": "DAILY TOTAL:", "monthlyTotal": "MONTHLY TOTAL:", "printDailyReport": "Print Daily Report", "printMonthlyReport": "Print Monthly Report", "reportGenerated": "Report generated:", "address": "Address", "vat": "VAT", "selectReportType": "Select Report Type", "chooseWhatToIncludeDaily": "Choose what to include in your daily report:", "gamesReportOnlyIcon": "🎱 Billiard Report Only", "barReportOnlyIcon": "🍺 Bar Report Only", "bothGamesBarIcon": "📊 Both (Billiard + Bar)", "category": "Category", "search": "Search", "searchItems": "Search items...", "coffee": "Coffee", "barMenuUpdated": "Bar Menu (Updated with New Storage System)", "searchProducts": "Search products...", "searchTablesByNameOrNumber": "Search tables by name or number...", "allUsers": "All Users", "unassigned": "Unassigned", "selectUser": "Select user", "sharedNoSpecificUser": "Shared (no specific user)", "onlyAssignedUserCanSeeTable": "Only assigned user can see this table", "tel": "Tel", "noProductsFound": "No products found", "addFirstMenuItem": "Add your first menu item to get started", "noProductsMatch": "No products match your search", "tryAdjustingSearch": "Try adjusting your search or filters", "barTables": "Bar Tables", "tableNamePrefix": "Table Name Prefix", "tableNumber": "Table Number", "tableName": "Table Name", "enterTableName": "Enter table name", "assignToUser": "Assign to User", "addTable": "Add Table", "noBarTablesFound": "No bar tables found", "addFirstTableToGetStarted": "Add your first table to get started", "editBarTable": "Edit Bar Table", "tableNumberAlreadyInUse": "Table number {{number}} is already in use. Each table must have a unique number globally.", "confirmDeleteTable": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "tableDeletedSuccessfully": "Table \"{{name}}\" deleted successfully", "failedToDeleteTable": "Failed to delete table: {{error}}", "failedToDeleteTableGeneric": "Failed to delete table", "assignedTo": "Assigned to", "confirmDeleteGameTables": "Are you sure you want to delete {{count}} game table(s)?\\n\\nTables: {{tables}}\\n\\nThis action cannot be undone.", "failedToDeleteGameTable": "Failed to delete game table {{tableId}}: {{error}}", "successfullyUpdatedTableNames": "Successfully updated {{count}} table names to consistent format!", "failedToFixTableNames": "Failed to fix table names. Please try again.", "failedToDeleteProductWithError": "Failed to delete product {{productId}}: {{error}}", "failedToSaveGameSettings": "Failed to save game settings", "failedToSaveBusinessInfo": "Failed to save business info", "failedToSaveCurrencySettings": "Failed to save currency settings", "failedToAddGameTableWithError": "Failed to add game table: {{error}}", "successfullyCreatedGameTables": "Successfully created {{count}} game tables", "failedToCreateAnyTables": "Failed to create any tables", "failedToCreateTables": "Failed to create tables", "confirmDeleteGameTable": "Are you sure you want to delete \"{{name}}\" (Table #{{number}})?\\n\\nThis action cannot be undone.", "failedToUpdateGameTableWithError": "Failed to update game table: {{error}}", "failedToDeleteGameTableWithError": "Failed to delete game table: {{error}}", "loginSettingsSavedSuccessfully": "Login settings saved successfully!", "failedToSaveLoginSettings": "Failed to save login settings", "allTablesWillBeCreatedBasedOnSetting": "All tables will be created as active/inactive based on this setting", "manageBarTablesAndSeating": "Manage bar tables and seating arrangements", "manageMenuItemsPricingAvailability": "Manage menu items, pricing, and availability", "configureBilliardGameSettings": "Configure billiard game settings and table management", "order": "Order", "createTables": "Create {{count}} Tables", "saveChanges": "Save Changes", "active": "Active", "inactive": "Inactive"}, "settings": {"title": "Settings", "businessInfo": "Business Information", "currency": "<PERSON><PERSON><PERSON><PERSON>", "gameSettings": "Game Settings", "loginSettings": "<PERSON><PERSON>", "language": "Language", "selectLanguage": "Select Language", "english": "English", "albanian": "Albanian", "accessDenied": "Access Denied", "noPermission": "You don't have permission to access settings", "adminOnly": "Admin access required", "categories": "Categories", "tables": "Tables", "barMenu": "Bar Menu", "permissions": "Permissions", "cacheManagement": "Cache Management", "categoryManagement": "Category Management", "addCategory": "Add Category", "active": "Active", "inactive": "Inactive", "edit": "Edit", "addTable": "Add Table", "addNewTable": "Add New Table", "createNewTableDescription": "Create a new table for your establishment", "tablesSelected": "{{count}} table(s) selected", "activate": "Activate", "deactivate": "Deactivate", "noTablesMatchFilters": "No tables match the current filters", "noTablesFound": "No tables found", "tableNumber": "Table Number", "gameSettingsSaved": "Billiard settings saved successfully", "gameSettingsError": "Failed to save billiard settings", "businessInfoSaved": "Business information saved successfully", "businessInfoError": "Failed to save business information", "barCategoriesManagement": "Bar Categories Management", "selectAll": "Select all", "selectAllGameTables": "Select All Game Tables", "noGameTablesMatch": "No game tables match the current filters", "noGameTablesFound": "No game tables found", "noProductsMatch": "No products match the current filters", "noProductsFound": "No products found", "categoryKey": "Category Key", "categoryKeyPlaceholder": "Enter category key", "categoryLabel": "Category Label", "categoryLabelPlaceholder": "Enter category label", "displayOrder": "Display Order", "currencySettingsSaved": "Currency settings saved successfully", "currencySettingsError": "Failed to save currency settings", "loginSettingsSaved": "Login settings saved successfully", "loginSettingsError": "Failed to save login settings", "generalGameSettings": "General Game Settings", "defaultHourlyRate": "Default Hourly Rate (L)", "minimumGameTime": "Minimum Game Time (minutes)", "autoEndAfterHours": "Auto-end After (hours)", "taxRate": "Tax Rate (%)", "autoPrintReceipts": "Auto-print receipts when games end", "saveSettings": "Save Settings", "saveUpdateAllTables": "Save & Update All Tables", "updateAllTablesNote": "\"Update All Tables\" will apply the new hourly rate to all existing game tables", "gameTablesCount": "Game Tables", "addGameTable": "Add Game Table", "addNewGameTable": "Add New Game Table", "createNewGameTableDescription": "Create a new game table for billiard sessions", "editGameTable": "Edit Game Table", "updateGameTable": "Update Game Table", "tableAddedSuccessfully": "Table '{{name}}' added successfully", "tableUpdatedSuccessfully": "Table '{{name}}' updated successfully", "failedToAddTable": "Failed to add table", "failedToDeleteTable": "Failed to delete table", "someTablesCouldNotBeDeleted": "Some tables could not be deleted", "tableName": "Table Name", "gameTableNamePlaceholder": "Enter table name", "displayNameForTable": "Display name for this table", "tableType": "Table Type", "selectTableType": "Select table type", "billiard": "Billiard", "pool": "Pool", "snooker": "Snooker", "typeOfGameTable": "Type of game table", "hourlyRate": "Hourly Rate", "costPerHourForTable": "Cost per hour for this table", "activeTable": "Active Table", "tableAvailableWhenActive": "Table is available for games when active", "customAlertSound": "Custom Alert Sound", "uploadSoundFile": "Upload a sound file or enter URL", "bulkCreateTables": "Bulk Create Tables", "createMultipleTablesAtOnce": "Create multiple tables at once", "autoGeneratedUniqueNumber": "Auto-generated unique number", "clickToGenerateNew": "Click to generate new", "assignToUser": "Assign to User", "noSpecificUserShared": "No specific user (shared)", "onlyAssignedUserWillSee": "Only the assigned user will be able to see and use this table. Leave unassigned for shared tables.", "allTablesWillBeAssigned": "All tables will be assigned to this user. Leave unassigned for shared tables.", "configureGameRulesAndPricing": "Configure game rules and pricing", "gameTables": "Game Tables", "manageBilliardTables": "Manage billiard tables", "fix": "Fix", "reset": "Reset", "searchTables": "Search tables...", "all": "All", "selected": "Selected", "clear": "Clear", "noTablesMatchSearch": "No tables match your search", "tryAdjustingSearch": "Try adjusting your search", "addFirstTableToStart": "Add your first table to get started", "rate": "Rate", "type": "Type", "assigned": "Assigned", "shared": "Shared", "testSound": "Test Sound", "removeSound": "Remove", "uploadingSoundFile": "Uploading sound file...", "soundUploadedSuccessfully": "Sound uploaded successfully", "failedToUploadSound": "Failed to upload sound file", "invalidSoundFile": "Invalid sound file. Please upload an audio file (MP3, WAV, OGG, M4A, WebM, AAC)", "soundFileTooLarge": "Sound file too large. Maximum size is 5MB", "failedToPlaySound": "Failed to play sound. Please check the file", "hideSoundLibrary": "Hide Sound Library", "showSoundLibrary": "Show Sound Library", "previouslyUploadedSounds": "Previously Uploaded Sounds", "noSoundFilesUploaded": "No sound files uploaded yet. Upload your first sound file above", "useThisSound": "Use this sound", "deleteSound": "Delete sound", "currentlySelected": "currently selected", "currentlyAssigned": "Currently Assigned Sound", "clickToSelect": "Click to select this sound", "clickToUse": "Click to use", "failedToDeleteSound": "Failed to delete sound file", "soundFileDescription": "Upload an audio file (MP3, WAV, OGG, M4A, WebM, AAC) up to 5MB, or enter a URL to an online sound file", "soundFileStoredLocally": "This file is stored locally in your app", "hourlyRateLabel": "Hourly Rate:", "tableTypeLabel": "Table Type:", "statusLabel": "Status:", "availableForGames": "Available for Games", "order": "Order", "totalTables": "Total Tables", "activeTables": "Active Tables", "barTables": "Bar Tables", "tableNumberLabel": "Table Number", "barMenuManagement": "Bar Menu Management", "addNewBarMenuItem": "Add New Bar Menu Item", "addNewMenuItemDescription": "Add a new item to your bar menu", "editProduct": "Edit Product", "updateProduct": "Update Product", "addProduct": "Add Product", "productName": "Product Name", "productPrice": "Product Price", "productCategory": "Product Category", "productDescription": "Product Description", "briefProductDescription": "Brief description of the product", "failedToDeleteProduct": "Failed to delete product", "someProductsCouldNotBeDeleted": "Some products could not be deleted", "itemsCount": "{{count}} items", "allCategories": "All Categories", "allProducts": "All Products", "add": "Add", "price": "Price", "category": "Category", "addNewBarCategory": "Add New Bar Category", "createNewCategory": "Create a new category for organizing menu items", "editCategory": "Edit Category", "updateCategory": "Update Category", "businessInformation": "Business Information", "businessName": "Business Name", "enterBusinessName": "Enter business name", "enterBusinessAddress": "Enter business address", "enterPhoneNumber": "Enter phone number", "enterEmailAddress": "Enter email address", "enterVatNumber": "Enter VAT number", "address": "Address", "phone": "Phone", "email": "Email", "vatNumber": "VAT Number", "saveBusinessInformation": "Save Business Information", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "currencyName": "Currency Name", "selectCurrency": "Select currency", "albanianLek": "Albanian Lek", "euro": "Euro", "usDollar": "US Dollar", "britishPound": "British Pound", "swissFranc": "Swiss Franc", "currencyUsedInBusiness": "The currency used in your business", "currencySymbol": "Currency Symbol", "symbolDisplayedWithPrices": "Symbol displayed with prices (e.g., L, €, $)", "taxPercentageApplied": "Tax percentage applied to all sales", "showDecimals": "Show Decimals", "displayPricesWithDecimals": "Display prices with .00 (e.g., 50.00 L vs 50 L)", "enableTax": "Enable Tax", "turnTaxCalculationOnOff": "Turn tax calculation on/off for all sales", "taxIncludedInPrices": "Tax Included in Product Prices", "taxIncludedNote": "Product prices include tax (tax will be shown separately on receipts)", "taxExcludedNote": "Product prices exclude tax (tax will be added at checkout)", "qrCodeSettings": "QR Code Settings", "includeQrCodeOnReceipts": "Include QR Code on Receipts", "addQrCodeToReceipts": "Add a QR code to all printed receipts", "qrCodeUrl": "QR Code URL", "qrCodeUrlPlaceholder": "https://example.com", "qrCodeUrlDescription": "Enter the URL that customers will be redirected to when they scan the QR code. Leave empty to show business contact information instead.", "saveCurrencySettings": "Save <PERSON><PERSON><PERSON><PERSON>", "loginSettingsTitle": "<PERSON><PERSON>", "database": "Database", "settingsLoadedFromDatabase": "Settings loaded from database and will persist across devices", "waitersAccounts": "Waiter Accounts", "waiterOne": "Waiter 1", "waiterTwo": "Waiter 2", "waiter1Placeholder": "waiter1", "waiter2Placeholder": "waiter2", "enableWaitersAccountsSection": "Enable Waiters Accounts Section", "showQuickLoginButtons": "Show quick login buttons for waiters on the login page", "sectionTitle": "Section Title", "titleDisplayedAboveButtons": "The title displayed above the waiter login buttons", "waiterOneSettings": "Waiter 1 Setting<PERSON>", "waiterTwoSettings": "Waiter 2 Set<PERSON><PERSON>", "displayName": "Display Name", "username": "Username", "password": "Password", "preview": "Preview", "saveLoginSettings": "Save Login <PERSON>tings", "configureWaiterLoginAccounts": "Configure waiter login accounts", "localStorage": "Local Storage", "default": "<PERSON><PERSON><PERSON>", "password1Placeholder": "password1", "password2Placeholder": "password2", "confirmDeleteProducts": "Are you sure you want to delete {{count}} product(s)?\\n\\nProducts: {{products}}\\n\\nThis action cannot be undone.", "confirmDeleteProduct": "Are you sure you want to delete \"{{name}}\"?\\n\\nThis action cannot be undone.", "addItem": "Add Item", "permissionsManagement": "Permissions Management", "permissionsDescription": "Control what different user roles can see and do in the system.", "waiters": "Waiters", "admins": "Admins", "waiterPermissions": "Waiter Permissions", "save": "Save", "gameManagement": "Game Management", "viewAllTables": "View All Tables", "viewAllTablesDescription": "Can see all active game tables from all users", "viewOwnTablesOnly": "View Own Tables Only", "viewOwnTablesOnlyDescription": "Can only see game tables they created", "createNewGames": "Create New Games", "createNewGamesDescription": "Can start new game sessions", "stopAnyGame": "Stop Any Game", "stopAnyGameDescription": "Can stop any active game table", "viewAllHistory": "View All History", "viewAllHistoryDescription": "Can see game history from all users", "denied": "Denied", "allowed": "Allowed", "barOrders": "Bar Orders", "viewAllOrders": "View All Orders", "viewAllOrdersDescription": "Can see all bar orders from all users", "viewOwnOrdersOnly": "View Own Orders Only", "viewOwnOrdersOnlyDescription": "Can only see bar orders they created", "createOrders": "Create Orders", "createOrdersDescription": "Can create new bar orders", "editAnyOrder": "Edit Any Order", "editAnyOrderDescription": "Can modify any bar order", "analytics": "Analytics", "viewAnalytics": "View Analytics", "viewAnalyticsDescription": "Can access analytics dashboard", "viewOwnStats": "View Own Stats", "viewOwnStatsDescription": "Can see personal performance statistics", "settingsPermissions": "Settings", "accessSettings": "Access Settings", "accessSettingsDescription": "Can access settings menu", "manageUsers": "Manage Users", "manageUsersDescription": "Can create, edit, and delete users", "managePermissions": "Manage Permissions", "managePermissionsDescription": "Can modify user permissions", "userManagement": "User Management", "manageUserAccountsAndRoles": "Manage user accounts and roles", "userManagementCenter": "User Management Center", "createEditManageUserAccounts": "Create, edit, and manage user accounts and their roles. Control access permissions and user settings.", "openUserManagement": "Open User Management", "billiardClub": "BILLIARD CLUB", "tiranAlbania": "Tiran, Albania", "phoneNumber": "+355 69 123 4567", "emailAddress": "<EMAIL>", "website": "www.billiardclub.al", "taxId": "K12345678A", "barCategories": "Bar Categories", "organizeMenuItemsIntoCategories": "Organize your menu items into categories", "organizeMenuItems": "Organize menu items"}, "activeGames": {"title": "Active Billiard", "active": "active", "noGamesYet": "No billiard sessions yet", "startGameToSeeActivity": "Start a billiard session to see activity", "recently": "Recently", "recentCompleted": "Recent Completed"}, "errors": {"networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "validationError": "Please check your input and try again.", "permissionDenied": "Permission denied. Contact administrator.", "notFound": "Resource not found.", "quotaExceeded": "Storage quota exceeded. Cleaning up...", "offlineMode": "You are offline. Changes will be synced when connection is restored."}, "success": {"saved": "Changes saved successfully", "deleted": "Item deleted successfully", "synced": "Data synchronized successfully", "cacheCleared": "<PERSON><PERSON> cleared successfully"}, "storage": {"online": "Online", "clearCache": "<PERSON>ache"}, "hardcoded": {"player": "Player", "now": "Now", "recently": "Recently", "timesUp": "Time's Up!", "left": "left", "secondsLeft": "{{seconds}}s left", "minutesLeft": "{{minutes}}m left", "hoursLeft": "{{hours}}h left", "albanianLek": "Albanian Lek", "barBilardo": "Bar-Bilardo", "billiardClub": "BILLIARD CLUB", "scanForMoreInfo": "Scan for more info", "scanForContactInfo": "Scan for contact info", "systemAutoPrint": "System Auto-Print", "system": "System", "courierNew": "Courier New", "libreBarcode": "Libre Barcode 39", "contentType": "Content-Type", "applicationJson": "application/json", "total": "TOTAL", "date": "Date"}, "receipts": {"title": "Receipts", "transactionHistory": "Transaction History", "filtersAndSearch": "Filters & Search", "searchTransactions": "Search transactions...", "refresh": "Refresh", "lastUpdated": "Last updated", "loading": "Loading transactions...", "noTransactions": "No transactions found", "type": "Type", "table": "Table", "user": "User", "time": "Time", "amount": "Amount", "actions": "Actions", "view": "View", "reprint": "Reprint", "delete": "Delete", "game": "Game", "order": "Order", "allTypes": "All Types", "allTables": "All Tables", "allUsers": "All Users", "dateRange": "Date Range", "selectDate": "Select date", "from": "From", "to": "To", "clearFilters": "Clear Filters", "exportData": "Export Data", "receiptDetails": "Receipt Details", "receiptNumber": "Receipt Number", "printedBy": "Printed By", "printedAt": "Printed At", "duration": "Duration", "startTime": "Start Time", "endTime": "End Time", "items": "Items", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "close": "Close", "receiptPreview": "Receipt Preview", "date": "Date", "pickDateRange": "Pick Date Range", "desc": "Description", "exportCSV": "Export CSV", "selected": "selected", "totalTransactions": "Total Transactions", "pageAmount": "Page Amount", "pageAvg": "Page Avg", "filterByType": "Filter by Type", "filterByTable": "Filter by Table", "filterByUser": "Filter by User", "gameSessions": "Game Sessions", "barOrders": "Bar Orders", "sortBy": "Sort by", "asc": "Ascending", "noPermission": "No Permission"}, "analytics": {"todaysRevenue": "Today's Revenue", "averageGameDuration": "Average Game Duration", "peakHours": "Peak Hours", "consistent": "Consistent", "barOrders": "Bar Orders", "orders": "Orders", "tableUsageByHour": "Table Usage by Hour", "weeklyRevenue": "Weekly Revenue", "games": "Billiard", "performanceMetrics": "Performance Metrics", "dailyOverview": "📅 Daily Overview", "mostUsedTable": "Most Used Table", "tableNA": "Table N/A", "topDrink": "Top Drink", "na": "N/A", "activeTables": "Active Tables", "gameRoomAnalytics": "🎱 Billiard Room Analytics", "tableUtilization": "Table Utilization", "avgGameDuration": "Avg Session Duration", "gamesToday": "Billiard Sessions Today", "peakHour": "Peak Hour", "barPerformance": "🍺 Bar Performance", "barRevenueToday": "Bar Revenue Today", "ordersToday": "Orders Today", "avgOrderValue": "Avg Order Value", "bestseller": "Bestseller", "monthlyReport": "📈 Monthly Report", "totalRevenue": "Total Revenue", "gamesRevenue": "🎱 Billiard Revenue", "barRevenue": "🍺 Bar Revenue", "peakActivity": "Peak Activity", "performanceMetricsTitle": "📊 Performance Metrics", "noDataAvailable": "No data available", "tableUsageDataWillAppear": "Table usage data will appear here once billiard sessions and orders are created", "accessDenied": "Access Denied", "noPermission": "You don't have permission to view analytics", "loadError": "Failed to load analytics data", "tables": "tables"}, "receipt": {"print": "Print", "reprint": "Reprint", "receipt": "Receipt", "gameReceipt": "Game Receipt", "orderReceipt": "Order Receipt", "receiptNumber": "Receipt #", "table": "Table", "date": "Date", "time": "Time", "duration": "Duration", "amount": "Amount", "total": "Total", "thankYou": "Thank you!", "visitAgain": "Visit us again soon"}, "profile": {"userProfile": "User Profile", "profile": "Profile", "security": "Security", "users": "Users", "profileInformation": "Profile Information", "changePhoto": "Change Photo", "fullName": "Full Name", "enterFullName": "Enter your full name", "username": "Username", "enterUsername": "Enter your username", "cancel": "Cancel", "saveChanges": "Save Changes", "changePassword": "Change Password", "currentPassword": "Current Password", "enterCurrentPassword": "Enter your current password", "newPassword": "New Password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm New Password", "enterConfirmNewPassword": "Confirm your new password", "passwordFieldsNote": "Password must be at least 8 characters long"}, "users": {"userManagement": "User Management", "usersTotal": "{{count}} users total", "addUser": "Add User", "user": "user", "users": "users", "total": "total", "active": "active", "loadingUsers": "Loading users...", "noUsersFound": "No users found", "clickAddUserToCreate": "Click \"Add User\" to create your first user", "createNewUser": "Create New User", "username": "Username", "enterUsername": "Enter username", "fullName": "Full Name", "enterFullName": "Enter full name", "role": "Role", "waiter": "Waiter", "admin": "Admin", "password": "Password", "enterPassword": "Enter password", "activeUser": "Active User", "cancel": "Cancel", "creating": "Creating...", "createUser": "Create User", "usernameAndNameRequired": "Username and full name are required", "passwordRequired": "Password is required", "userCreatedSuccessfully": "User created successfully", "failedToCreateUser": "Failed to create user", "userUpdatedSuccessfully": "User updated successfully", "failedToUpdateUser": "Failed to update user", "confirmDeleteUser": "Are you sure you want to delete user \"{{username}}\"?", "userDeletedSuccessfully": "User deleted successfully", "failedToDeleteUser": "Failed to delete user", "inactive": "Inactive", "created": "Created", "expand": "Expand", "collapse": "Collapse", "delete": "Delete", "editUserDetails": "Edit User Details", "saving": "Saving...", "save": "Save", "newPasswordOptional": "New Password (optional)", "enterNewPassword": "Enter new password", "userDetails": "User Details", "edit": "Edit", "status": "Status", "editUser": "Edit User"}, "database": {"online": "Online", "offline": "Offline", "offlineMessage": "Database is currently unavailable. Some features may be limited."}}, "sq": {"common": {"loading": "<PERSON> u ngarkuar...", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "delete": "Fshi", "edit": "<PERSON><PERSON><PERSON><PERSON>", "add": "Shto", "search": "K<PERSON><PERSON><PERSON>", "filter": "Filtro", "export": "Eksporto", "import": "Importo", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>ll", "confirm": "Konfirmo", "yes": "Po", "no": "<PERSON>", "ok": "OK", "error": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON>", "warning": "Paralajmërim", "info": "Informacion", "submit": "Dërgo", "reset": "Rivendos", "clear": "<PERSON><PERSON>", "select": "Zgjid<PERSON>", "none": "Asnjë", "all": "<PERSON><PERSON> gjitha", "total": "Totali", "subtotal": "Nëntotali", "tax": "<PERSON><PERSON><PERSON>", "discount": "Zbritje", "price": "<PERSON><PERSON><PERSON>", "quantity": "Sasia", "amount": "<PERSON><PERSON>", "date": "Data", "time": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "description": "Përshkrimi", "status": "Statusi", "active": "Aktiv", "inactive": "Joaktiv", "enabled": "I aktivizuar", "disabled": "I çaktivizuar", "available": "I disponues<PERSON><PERSON>m", "unavailable": "I padisponueshëm", "online": "Online", "offline": "Offline", "connected": "<PERSON> lidhur", "disconnected": "I shkëputur", "pending": "<PERSON><PERSON> pritje", "completed": "I përfunduar", "failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Duke u procesuar", "cancelled": "I anuluar", "approved": "I miratuar", "rejected": "I refuzuar", "draft": "Draft", "published": "I publikuar", "archived": "<PERSON> arkivuar", "new": "I ri", "updated": "I përditësuar", "created": "<PERSON> kri<PERSON>ar", "modified": "I modifikuar", "deleted": "I fshirë", "removed": "I hequr", "added": "I shtuar", "changed": "I ndryshuar", "saved": "I ruajtur", "loaded": "<PERSON> ng<PERSON>", "uploaded": "<PERSON> ng<PERSON>", "downloaded": "<PERSON> sh<PERSON><PERSON><PERSON>", "sent": "<PERSON> d<PERSON><PERSON><PERSON><PERSON>", "received": "I marrë", "opened": "<PERSON> hapur", "closed": "I mbyllur", "started": "I filluar", "stopped": "I n<PERSON>uar", "paused": "I ndalur përkohësisht", "resumed": "I vazhduar", "finished": "I përfunduar", "expired": "I skaduar", "valid": "I vlefshëm", "invalid": "I pavlefshëm", "required": "I detyrueshëm", "optional": "Opsional", "public": "Publik", "private": "Privat", "visible": "I dukshëm", "hidden": "I fshehur", "locked": "<PERSON> kyçur", "unlocked": "<PERSON> hapur", "free": "<PERSON><PERSON><PERSON>", "paid": "Me pagesë", "premium": "Premium", "basic": "Bazë", "advanced": "I avancuar", "professional": "Profesional", "enterprise": "Ndërmarrje", "personal": "Personal", "business": "Biznes", "unlimited": "I pakufizuar", "limited": "I kufizuar", "default": "Parazgjedhur", "custom": "I personalizuar", "automatic": "Automatik", "manual": "Manual", "high": "<PERSON> lartë", "medium": "Mesatar", "low": "<PERSON> <PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normal", "critical": "<PERSON><PERSON><PERSON>", "major": "<PERSON><PERSON><PERSON><PERSON>", "minor": "Dytësor", "today": "Sot", "yesterday": "<PERSON><PERSON>", "tomorrow": "<PERSON><PERSON><PERSON><PERSON>", "thisWeek": "Këtë javë", "lastWeek": "Javën e kaluar", "nextWeek": "Javën e ardhshme", "thisMonth": "<PERSON><PERSON><PERSON><PERSON> muaj", "lastMonth": "<PERSON><PERSON><PERSON> e kaluar", "nextMonth": "Muajin e ardhshëm", "thisYear": "Këtë vit", "lastYear": "Vitin e kaluar", "nextYear": "Vitin e ardhshëm", "now": "<PERSON><PERSON>", "recently": "Së fundmi", "soon": "<PERSON><PERSON>", "later": "<PERSON><PERSON>", "never": "Kurrë", "always": "Gjithmonë", "sometimes": "Ndonjëherë", "often": "<PERSON><PERSON><PERSON>", "rarely": "Rrallë", "daily": "Ditore", "weekly": "<PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON>", "hourly": "<PERSON><PERSON>", "minutes": "<PERSON>uta", "hours": "Orë", "days": "Ditë", "weeks": "Javë", "months": "<PERSON><PERSON>", "years": "Vite", "seconds": "Sekonda", "minute": "Minutë", "hour": "Orë", "day": "Ditë", "week": "Javë", "month": "<PERSON><PERSON>", "year": "Vit", "second": "Sekondë", "january": "<PERSON><PERSON>", "february": "Shkurt", "march": "Mars", "april": "Prill", "may": "Maj", "june": "<PERSON><PERSON><PERSON>", "july": "<PERSON><PERSON><PERSON>", "august": "<PERSON><PERSON>", "september": "<PERSON><PERSON><PERSON>", "october": "<PERSON><PERSON>", "november": "Nëntor", "december": "<PERSON><PERSON><PERSON><PERSON>", "jan": "Jan", "feb": "Shk", "mar": "Mar", "apr": "Pri", "jun": "<PERSON><PERSON>", "jul": "<PERSON><PERSON>", "aug": "<PERSON>", "sep": "Sht", "oct": "Tet", "nov": "Nën", "dec": "Dhj", "monday": "E Hënë", "tuesday": "E Martë", "wednesday": "E Mërkurë", "thursday": "<PERSON>", "friday": "E Premte", "saturday": "E Shtunë", "sunday": "<PERSON>", "mon": "Hën", "tue": "Mar", "wed": "<PERSON><PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Pre", "sat": "Sht", "sun": "Die", "hoursShort": "h", "minutesShort": "m", "left": "mbetur"}, "navigation": {"dashboard": "BBM", "games": "Loj<PERSON><PERSON>", "bar": "Bar", "analytics": "<PERSON><PERSON><PERSON>", "settings": "Cilësimet", "logout": "Dil", "home": "<PERSON><PERSON><PERSON>", "menu": "Menyja", "reports": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profili", "help": "Ndihmë", "about": "<PERSON><PERSON><PERSON>", "contact": "Kontakt", "support": "Mbështetje"}, "auth": {"login": "Hyr", "logout": "Dil", "username": "Emri i përdoruesit", "password": "Fjalëkalimi", "email": "Email", "loginButton": "Hyr", "logoutButton": "Dil", "loginError": "Emri i përdoruesit ose fjalëkalimi i gabuar", "sessionExpired": "Sesioni ka skaduar. Ju lutemi hyni pë<PERSON>ë<PERSON>.", "waitersAccounts": "Llogaritë e Kamarierëve", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> fja<PERSON><PERSON><PERSON><PERSON><PERSON>?", "rememberMe": "<PERSON><PERSON> mbaj mend", "signUp": "Regjistrohu", "signIn": "Hyr", "createAccount": "<PERSON><PERSON><PERSON>", "alreadyHaveAccount": "<PERSON>i tashmë një llogari?", "dontHaveAccount": "<PERSON>uk keni llogari?", "resetPassword": "Rivendos f<PERSON>", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "newPassword": "Fjalëkalimi i ri", "confirmPassword": "Konfirmo <PERSON>", "currentPassword": "Fjalëkalimi aktual", "passwordMismatch": "Fjalëkalimet nuk përputhen", "passwordTooShort": "Fjalëkalimi duhet të jetë të paktën 6 karaktere", "invalidEmail": "Adresa e email-it e pavlefshme", "accountCreated": "Llogaria u krijua me sukses", "passwordChanged": "Fjalëkalimi u ndryshua me sukses", "passwordReset": "Fjalëkalimi u rivendos me sukses"}, "dashboard": {"title": "BBM", "welcome": "Mirë se vini", "overview": "Përmbledhje", "todaysOrders": "Porositë e sotme", "todaysSessions": "Seancat e sotme", "activeTables": "Tavolinat aktive", "recentActivity": "Aktiviteti i fundit", "quickStats": "Statistika të shpejta", "totalRevenue": "Të ardhurat totale", "activeGames": "Biliardo Aktive", "completedOrders": "Porositë e përfunduara", "totalSales": "Shitjet totale", "totalCustomers": "Klientët totalë", "averageOrderValue": "Vlera mesatare e porosisë", "topSellingItems": "Artikujt më të shitur", "recentTransactions": "Transaksionet e fundit", "monthlyRevenue": "Të ardhurat mujore", "weeklyRevenue": "Të ardhurat javore", "dailyRevenue": "Të ardhurat ditore", "salesTrend": "Trendi i shitjeve", "customerSatisfaction": "Kënaqësia e klientit", "performanceMetrics": "Metrikat e performancës"}, "games": {"title": "Loj<PERSON><PERSON>", "activeTables": "Tavolinat Aktive", "liveControlMonitoring": "<PERSON><PERSON><PERSON> dhe <PERSON>im në kohë reale", "startGame": "<PERSON><PERSON>", "stopGame": "<PERSON><PERSON><PERSON>", "stop": "NDALO", "table": "Tavolina", "tableName": "Emri i Tavolinës", "tableNumber": "Tavolina {{number}}", "tableShort": "T", "duration": "Kohëzgjatja", "player": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "cost": "Kostoja", "time": "<PERSON><PERSON>", "end": "Fundi", "date": "Data", "startTime": "<PERSON><PERSON> <PERSON>llimit", "endTime": "Koha e Përfundimit", "now": "<PERSON><PERSON>", "recently": "Së fundmi", "unknown": "<PERSON> pan<PERSON>hur", "defaultTimeLimit": "Limitet", "soundOn": "<PERSON><PERSON><PERSON>", "soundOff": "<PERSON><PERSON><PERSON>", "makeAllAvailable": "Mbylli te gjitha", "dailyReport": "<PERSON><PERSON><PERSON>", "monthlyReport": "<PERSON><PERSON><PERSON>", "todaysSessions": "Seancat e Sotme", "timeLimitReached": "Kufiri i Kohës u Arrit", "timeLimitReachedBody": "Një ose më shumë seanca biliardoje kanë arritur kufirin e kohës.", "noGamesToday": "Nuk ka seanca biliardoje sot", "gameSessionReceipt": "Fatura e Biliardos", "subtotalExclTax": "Nëntotali (pa taksë)", "tax": "<PERSON><PERSON><PERSON> ({{rate}}%)", "total": "Totali", "unlimited": "<PERSON>", "oneHour": "1 Orë", "twoHours": "2 Orë", "threeHours": "3 Orë", "fourHours": "4 Orë", "fiveHours": "5 Orë", "sixHours": "6 Orë", "tables": "tavolina", "playing": "<PERSON>", "gamesCount": "<PERSON><PERSON><PERSON>", "readyToPlay": "Gati p<PERSON>r <PERSON>", "tapToStart": "Prek për të <PERSON>uar", "perHour": "/orë", "change": "<PERSON><PERSON><PERSON><PERSON>", "offline": "Offline", "contactStaff": "Kontakto Stafin", "timesUp": "<PERSON><PERSON>!", "secondsLeft": "{{seconds}}s mbetur", "minutesLeft": "{{minutes}}m mbetur", "hoursMinutesLeft": "{{hours}}h {{minutes}}m mbetur", "default": "Parazgjedhur", "noLimit": "<PERSON> kufizim", "receiptNumber": "Fatura #", "thankYouForPlaying": "Faleminderit që luajtët!", "visitUsAgainSoon": "Na vizitoni përsëri së shpejti", "scanForMoreInfo": "Skanoni për më shumë informacion", "scanForContactInfo": "--BBM--", "servedBy": "Shërbyer nga", "limit": "kufiri", "live": "LIVE", "ready": "GATI", "off": "FIKUR", "status": {"free": "I lirë", "occupied": "I zënë", "held": "I mbaj<PERSON>"}, "albanianLek": "Leku <PERSON>ptar", "billiardClub": "KLUBI I BILIARDOS", "address": "<PERSON><PERSON><PERSON>", "vat": "TVSH", "reportGenerated": "Raporti u gjenerua", "noGamesCompletedToday": "Nuk ka lojëra të përfunduara sot", "noGamesCompletedThisMonth": "Nuk ka lojëra të përfunduara këtë muaj", "billiardGames": "LOJËRAT E BILIARDOS", "gamesSubtotal": "Nëntotali i Lojërave", "dailyTotal": "TOTALI DITOR", "monthlyTotal": "TOTALI MUJOR", "refreshPage": "<PERSON><PERSON><PERSON><PERSON>", "clearCache": "<PERSON><PERSON>", "somethingWentWrong": "Diçka shkoi keq", "errorLoadingComponent": "Pati një gabim në ngarkimin e këtij komponenti. <PERSON><PERSON> shpesh shkaktohet nga të dhënat e korruptuara të shfletuesit.", "reloadPage": "<PERSON><PERSON><PERSON>", "failedToInitialize": "Dështoi inicializimi i aplikacionit", "sine": "sinusoidal", "active": "aktive", "completed": "e përfunduar", "excellent": "E <PERSON>", "good": "E mirë", "needsImprovement": "Ka nevojë për përmirësim", "high": "<PERSON> lartë", "medium": "Mesatare", "low": "<PERSON>", "growing": "<PERSON> u rritur", "stable": "E qëndrueshme", "auto": "Automatik", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "today": "Sot", "week": "Javë", "month": "<PERSON><PERSON>", "overview": "Përmbledhje", "revenue": "Të Ardhurat", "performance": "Performanca", "insights": "<PERSON><PERSON><PERSON><PERSON>", "todaysRevenueByHour": "Të Ardhurat e Sotme sipas Orës", "weeklyRevenue": "Të Ardhurat Javore", "monthlyRevenue": "Të Ardhurat Mujore", "revenueOverview": "Përmbledhja e të Ardhurave", "excellentDay": "🔥 Ditë e sh<PERSON>yer!", "growingTrend": "📈 Duke u rritur", "needsAttention": "📉 Ka nevojë për vëmendje", "optimalTiming": "⚡ Kohëzgja<PERSON><PERSON> optimale", "roomForImprovement": "🎯 Ka hapësirë për përm<PERSON>sim", "rushHourNow": "🔥 Ora e Pikut Tani!", "normalTime": "📊 Normale", "peakTimeNow": "⚡ Ora e pikut tani!", "planForPeak": "📈 Planif<PERSON> për pikun", "noDataAvailableForTimeRange": "<PERSON>uk ka të dhëna {{timeRange}} të disponueshme", "dataWillAppearHere": "Të dhënat do të shfaqen këtu pasi të regjistrohen transaksionet", "gamesIcon": "🎱 <PERSON><PERSON><PERSON><PERSON>", "barIcon": "🍺 Bari", "barOrdersIcon": "🍺 Porositë e Barit", "revenueBreakdown": "💰 Ndarja e të <PERSON>ave", "revenueInsights": "<PERSON><PERSON><PERSON><PERSON> mbi të <PERSON>", "smartRecommendations": "Rekomandime të Mençura", "businessHealthScore": "Rezultati i Shëndetit të Biznesit", "overallHealthScore": "Rezultati i Përgjithshëm i Shëndetit", "revenueTrend": "Trendi i të Ardhurave", "efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "utilization": "Shfry<PERSON><PERSON><PERSON><PERSON>", "peakPerformance": "⚡ Performanca e Pikut", "mostUsedTable": "Tavolina më e Përdorur", "bestPerformer": "Performuesi më i Mirë", "opportunity": "<PERSON><PERSON><PERSON><PERSON>", "gamesAreDrivingRevenue": "🎱 Lojërat po drejtojnë të ardhurat sot", "barSalesAreLeading": "🍺 Shitjet e barit po udhëheqin sot", "excellentPerformance": "Performancë e Shkëlqyer!", "revenueIsUp": "Të ardhurat janë rritur {changeText}. Vazhdoni punën e mirë!", "optimizationOpportunity": "Mundësi Optimizimi", "considerOptimizingGameDurations": "Konsideroni optimizimin e kohëzgjatjes së lojërave për të përmirësuar qarkullimin e tavolinave.", "marketingSuggestion": "<PERSON><PERSON><PERSON><PERSON>", "tableUtilizationIsLow": "Shfrytëzimi i tavolinave është i ulët. Konsideroni organizimin e promovimeve gjatë orëve jo-pik.", "topItem": "<PERSON><PERSON><PERSON><PERSON>", "peakHour": "Ora e Pikut", "barOrders": "POROSITË E BARIT", "noOrdersCompletedToday": "Nuk ka porosi të përfunduara sot", "noOrdersCompletedThisMonth": "Nuk ka porosi të përfunduara këtë muaj", "items": "artik<PERSON><PERSON>", "smartAnalytics": "Analitika e Mençur", "realTimeInsights": "Njohuri në kohë reale", "lastUpdated": "Përditësuar së fundmi", "avgDurationMin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (min)", "gamesToday": "Lojërat e Sotme", "ordersToday": "Porositë e Sotme", "avgOrder": "<PERSON><PERSON><PERSON>", "billiard": "<PERSON><PERSON><PERSON>"}, "bar": {"title": "Bar", "selectTable": "Zgjidh Tavolinë", "menu": "Menyja", "addToOrder": "Shto në Porosi", "placeOrder": "<PERSON><PERSON><PERSON>", "orderTotal": "Totali i Porosisë", "onHold": "në pritje", "clearAll": "Pastro të Gjitha", "pending": "<PERSON><PERSON> pritje", "clear": "<PERSON><PERSON>", "selectTableToOrder": "Zgjidh një tavolinë për të filluar porosinë", "tablesOnHold": "tavolina në pritje", "currentOrder": "Porosia Aktuale", "submitOrderAndPrint": "<PERSON><PERSON><PERSON> dhe <PERSON>", "todaysOrders": "Porositë e Sotme", "noOrdersToday": "Nuk ka porosi sot", "noOrdersCompletedToday": "Nuk ka porosi të përfunduara sot", "smartBar": "Bar i Mençur", "activeTables": "tavolina aktive", "utilization": "shfrytëzim", "smartMenu": "Menyja e Mençur", "items": "artik<PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "monthlyReports": "<PERSON><PERSON><PERSON>", "smartAnalytics": "Analitika e Mençur", "realTimeInsights": "Njohuri në kohë reale", "lastUpdated": "Përditësuar së fundmi", "todaysRevenueByHour": "Të Ardhurat e Sotme sipas Orës", "weeklyRevenue": "Të Ardhurat Javore", "searchMenuItems": "Kërko artikuj të menysë...", "all": "<PERSON><PERSON> gjitha", "quick": "<PERSON><PERSON> s<PERSON>", "popular": "Popullore", "recent": "Të fundit", "selectTableToStartOrdering": "Zgjidh një Tavolinë", "chooseTableToStartOrdering": "Zgjidh një tavolinë për të filluar porosinë", "chooseFromAvailableTables": "<PERSON><PERSON><PERSON>h nga {{count}} tavolina të disponueshme më sipër", "noItemsFound": "Nuk u gjetën artikuj", "noItemsMatch": "<PERSON><PERSON><PERSON><PERSON> artikull nuk përputhet me \"{{query}}\"", "noItemsInCategory": "Nuk ka artikuj në këtë kategori", "clearSearch": "<PERSON><PERSON>", "noMenuItems": "Nuk ka artikuj në meny", "addProductsInSettings": "Shto produkte në Cilësime", "addItemsFromMenu": "Shto artikuj nga menyja", "tableReady": "Tavolina {{number}} është gati", "each": "secili", "avgPerItem": "Mesatarja {{amount}}L/artikull", "table": "Tavolina", "unknown": "<PERSON> pan<PERSON>hur", "item": "<PERSON><PERSON><PERSON>", "dailyReport": "<PERSON><PERSON><PERSON>", "monthlyReport": "<PERSON><PERSON><PERSON>", "chooseWhatToInclude": "Zgjidh çfarë të përfshish në raportin {{reportType}}:", "gamesOnly": "<PERSON><PERSON><PERSON><PERSON>", "ordersOnly": "<PERSON><PERSON><PERSON><PERSON>", "bothGamesAndBar": "Të dyja Lojërat dhe Bari", "cancel": "<PERSON><PERSON>", "generateReport": "<PERSON><PERSON><PERSON><PERSON>", "barOrderReceipt": "Fatura e Porosisë së Barit", "receipt": "<PERSON><PERSON>", "date": "Data", "subtotalExclTax": "Nëntotali (pa taksë)", "tax": "<PERSON><PERSON><PERSON>", "total": "Totali", "thankYouForOrder": "Faleminderit për poro<PERSON>!", "enjoyYourDrinks": "Shijoni pijet tuaja!", "scanForMoreInfo": "Skanoni për më shumë informacion", "scanForContactInfo": "Skanoni për informacion kontakti", "drinks": "<PERSON><PERSON>", "snacks": "Snacks", "food": "<PERSON><PERSON><PERSON><PERSON>", "albanianLek": "Leku <PERSON>ptar", "completed": "e përfunduar", "avgItems": "artik<PERSON>j mesatar", "noTablesAvailable": "Nuk Ka Tavolina të Disponueshme", "noTablesAssigned": "Asnjë tavolinë nuk është caktuar për përdoruesin tuaj ose nuk është e disponueshme për përdorim.", "checkTablesInSettings": "Kontrolloni nëse keni tavolina të caktuara për pë<PERSON><PERSON>sin tuaj në Cilësime", "ensureTablesActive": "Sigurohuni që tavolinat janë shënuar si \"Aktive\"", "contactAdminForTables": "Kontaktoni administratorin për të caktuar tavolina në llogarinë tuaj", "dailyBarReport": "Raporti Ditor i <PERSON>t", "monthlyBarReport": "Raporti Mujor i Barit", "billiardGames": "LOJËRAT E BILIARDOS", "barOrders": "POROSITË E BARIT", "noGamesCompletedToday": "Nuk ka lojëra të përfunduara sot", "noGamesCompletedThisMonth": "Nuk ka lojëra të përfunduara këtë muaj", "noOrdersCompletedThisMonth": "Nuk ka porosi të përfunduara këtë muaj", "gamesSubtotal": "Nëntotali i Lojërave:", "ordersSubtotal": "Nëntotali i Porosive:", "dailyTotal": "TOTALI DITOR:", "monthlyTotal": "TOTALI MUJOR:", "printDailyReport": "<PERSON><PERSON>", "printMonthlyReport": "<PERSON><PERSON>", "reportGenerated": "Raporti u gjenerua:", "address": "<PERSON><PERSON><PERSON>", "vat": "TVSH", "gamesReportOnly": "Vetëm Raporti i Biliardos", "barReportOnly": "Vetëm Raporti i Barit", "category": "Kategoria", "search": "K<PERSON><PERSON><PERSON>", "searchItems": "Kërko artikuj...", "coffee": "<PERSON><PERSON>", "barMenuUpdated": "Menyja e Barit (E Përditësuar me Sistemin e Ri të Ruajtjes)", "searchProducts": "Kërko produkte...", "searchTablesByNameOrNumber": "Kërko tavolina sipas emrit ose numrit...", "allUsers": "Të Gjithë Përdoruesit", "unassigned": "Pa <PERSON>ar", "selectUser": "<PERSON><PERSON><PERSON><PERSON>", "sharedNoSpecificUser": "E Përbashkët (pa përdorues specifik)", "onlyAssignedUserCanSeeTable": "Vetëm përdoruesi i caktuar mund ta shohë këtë tavolinë", "tel": "Tel", "noProductsFound": "Nuk u gjetën produkte", "addFirstMenuItem": "<PERSON><PERSON><PERSON> artikullin tuaj të parë të menysë për të filluar", "noProductsMatch": "Asnjë produkt nuk përputhet me kërkimin tuaj", "tryAdjustingSearch": "Provoni të ndryshoni kërkimin ose filtrat", "barTables": "Tavolinat e Barit", "tableNamePrefix": "Prefiksi i Emrit të Tavolinës", "tableNumber": "Numri i Tavolinës", "tableName": "Emri i Tavolinës", "enterTableName": "Shkruani emrin e tavolinës", "assignToUser": "Cakto te Përdoruesi", "addTable": "Shto Tavolinë", "noBarTablesFound": "Nuk u gjetën tavolina bari", "addFirstTableToGetStarted": "Shto tavolinën tuaj të parë për të filluar", "editBarTable": "Ndrysho Tavolinën e Barit", "tableNumberAlreadyInUse": "Numri i tavolinës {{number}} është tashmë në përdorim. Çdo tavolinë duhet të ketë një numër unik globalisht.", "confirmDeleteTable": "<PERSON>i i sigurt që doni të fshini \"{{name}}\"? Ky veprim nuk mund të zhbëhet.", "tableDeletedSuccessfully": "<PERSON><PERSON><PERSON> \"{{name}}\" u fshi me sukses", "failedToDeleteTable": "<PERSON><PERSON><PERSON><PERSON>i fshirja e tavolinës: {{error}}", "failedToDeleteTableGeneric": "Dështoi fshirja e tavolinës", "assignedTo": "Caktuar te", "confirmDeleteGameTables": "<PERSON>i i sigurt që doni të fshini {{count}} tavolinë(a) loje?\\n\\nTavolinat: {{tables}}\\n\\nKy veprim nuk mund të zhbëhet.", "failedToDeleteGameTable": "<PERSON>ë<PERSON><PERSON><PERSON> fshir<PERSON> e tavolinës së lojës {{tableId}}: {{error}}", "successfullyUpdatedTableNames": "U përditësuan me sukses {{count}} emra ta<PERSON> në format të njëjtë!", "failedToFixTableNames": "Dështoi ndreqja e emrave të tavolinave. Ju lutemi provoni pë<PERSON>.", "failedToDeleteProductWithError": "<PERSON>ësh<PERSON>i fshirja e produktit {{productId}}: {{error}}", "failedToSaveGameSettings": "Dështoi ruajtja e cilësimeve të lojës", "failedToSaveBusinessInfo": "Dështoi ruajtja e informacionit të biznesit", "failedToSaveCurrencySettings": "Dështoi ruajtja e cilësimeve të monedhës", "failedToAddGameTableWithError": "Dësh<PERSON>i shtimi i tavolinës së lojës: {{error}}", "successfullyCreatedGameTables": "U krijuan me sukses {{count}} tavolina lojësh", "failedToCreateAnyTables": "Dështoi krijimi i çdo tavoline", "failedToCreateTables": "Dështoi krijimi i tavolinave", "confirmDeleteGameTable": "<PERSON>i i sigurt që doni të fshini \"{{name}}\" (Tavolina #{{number}})?\\n\\nKy veprim nuk mund të zhbëhet.", "failedToUpdateGameTableWithError": "Dështoi përditësimi i tavolinës së lojës: {{error}}", "failedToDeleteGameTableWithError": "Dësh<PERSON>i fshirja e tavolinës së lojës: {{error}}", "loginSettingsSavedSuccessfully": "Cilësimet e hyrjes u ruajtën me sukses!", "failedToSaveLoginSettings": "Dështoi ruajtja e cilësimeve të hyrjes", "allTablesWillBeCreatedBasedOnSetting": "Të gjitha tavolinat do të krijohen si aktive/joaktive bazuar në këtë cilësim", "manageBarTablesAndSeating": "Menaxhoni tavolinat e barit dhe vendo<PERSON>jen", "manageMenuItemsPricingAvailability": "Menaxhoni artikujt e menysë, çmimet dhe disponueshmërinë", "configureBilliardGameSettings": "Konfiguroni cilësimet e lojës së biliardos dhe menaxhimin e tavolinave", "order": "<PERSON><PERSON><PERSON>", "createTables": "<PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON><PERSON>", "active": "Aktiv", "inactive": "Joaktiv"}, "settings": {"title": "Cilësimet", "businessInfo": "Informacioni i Biznesit", "currency": "Cilësimet e Monedhës", "gameSettings": "Cilësimet e Biliardos", "loginSettings": "Cilësimet e Hyrjes", "language": "<PERSON><PERSON><PERSON>", "selectLanguage": "Zgjidh Gjuhën", "english": "<PERSON><PERSON><PERSON>", "albanian": "Shqip", "accessDenied": "Qasja u Refuzua", "noPermission": "Nuk keni leje për të hyrë në cilësime.", "adminOnly": "Vetëm administratorët mund të hyjnë në cilësimet e sistemit.", "categories": "Kategoritë", "tables": "<PERSON><PERSON><PERSON><PERSON>", "barMenu": "Menyja e Barit", "permissions": "Le<PERSON>", "cacheManagement": "Menaxhimi i Cache-it", "categoryManagement": "Menaxhimi i Kategorive", "addCategory": "<PERSON><PERSON><PERSON> Kategori", "active": "Aktiv", "inactive": "Joaktive", "tableNumber": "Numri i Tavolinës", "gameSettingsSaved": "Cilësimet e biliardos u ruajtën me sukses", "gameSettingsError": "Dështoi ruajtja e cilësimeve të biliardos", "businessInfoSaved": "Informacioni i biznesit u ruajt me sukses", "businessInfoError": "Dështoi ruajtja e informacionit të biznesit", "currencySettingsSaved": "Cilësimet e monedhës u ruajtën me sukses", "currencySettingsError": "Dështoi ruajtja e cilësimeve të monedhës", "loginSettingsSaved": "Cilësimet e hyrjes u ruajtën me sukses", "loginSettingsError": "Dështoi ruajtja e cilësimeve të hyrjes", "addTable": "Shto Tavolinë", "addNewTable": "Shto Tavolinë të Re", "createNewTableDescription": "K<PERSON>jo një tavolinë të re për vendosmërinë tuaj", "tablesSelected": "{{count}} tavolinë(a) të zgjedhura", "activate": "Aktivizo", "deactivate": "Çak<PERSON><PERSON>zo", "edit": "<PERSON><PERSON><PERSON><PERSON>", "generalGameSettings": "Cilësimet e Përgjithshme të Lojës", "defaultHourlyRate": "Tarifa e Paracaktuar për Orë (L)", "minimumGameTime": "Koha Minimale e Lojës (minuta)", "autoEndAfterHours": "Përfundim Automatik Pas (orë)", "taxRate": "Norma e Taksës (%)", "autoPrintReceipts": "Printim automatik i faturave kur përfundon loja", "saveSettings": "<PERSON><PERSON><PERSON>", "saveUpdateAllTables": "<PERSON><PERSON>j dhe Përditëso të Gjit<PERSON>", "updateAllTablesNote": "\"Përditëso të Gjitha Tavolinat\" do të aplikojë tarifën e re për orë në të gjitha tavolinat ekzistuese të lojës", "gameTablesCount": "<PERSON><PERSON><PERSON>", "addGameTable": "Shto Tavolinë Loje", "addNewGameTable": "Shto Tavolinë të Re Loje", "createNewGameTableDescription": "Krijo një tavolinë të re loje për sesionet e bilardos", "editGameTable": "Ndrysho <PERSON>", "updateGameTable": "Përditëso Tavolinë Loje", "tableAddedSuccessfully": "<PERSON><PERSON><PERSON> '{{name}}' u shtua me sukses", "tableUpdatedSuccessfully": "Ta<PERSON><PERSON> '{{name}}' u përditësua me sukses", "failedToAddTable": "Dështoi shtimi i tavolinës", "failedToDeleteTable": "Dështoi fshirja e tavolinës", "someTablesCouldNotBeDeleted": "Disa tavolina nuk mund të fshiheshin", "tableName": "Emri i Tavolinës", "gameTableNamePlaceholder": "Shkruaj emrin e tavolinës", "displayNameForTable": "Emri i shfaqur për këtë tavolinë", "tableType": "Lloji i Tavolinës", "selectTableType": "Zgjidh llojin e tavolinës", "billiard": "<PERSON><PERSON><PERSON>", "pool": "Pool", "snooker": "Snooker", "typeOfGameTable": "Lloji i tavolinës së lojës", "hourlyRate": "<PERSON><PERSON><PERSON>", "costPerHourForTable": "Kostoja për orë për këtë tavolinë", "activeTable": "Tavolinë Aktive", "tableAvailableWhenActive": "Tavolina është e disponueshme për lojëra kur është aktive", "customAlertSound": "Zëri i Personalizuar i Alarmit", "uploadSoundFile": "Ngarko një skedar zëri ose shkruaj URL", "bulkCreateTables": "Krijimi Masiv i Tavolinave", "createMultipleTablesAtOnce": "Krijo disa tavolina njëkohësisht", "autoGeneratedUniqueNumber": "Numër unik i gjeneruar automatikisht", "clickToGenerateNew": "Kliko për të gjeneruar të ri", "assignToUser": "Cakto tek Përdoruesi", "noSpecificUserShared": "Asnjë përdorues specifik (e ndarë)", "onlyAssignedUserWillSee": "Vetëm përdoruesi i caktuar do të mund të shohë dhe përdorë këtë tavolinë. Lëreni të pacaktuar për tavolina të ndara.", "allTablesWillBeAssigned": "Të gjitha tavolinat do të caktohen tek ky përdorues. Lëreni të pacaktuar për tavolina të ndara.", "configureGameRulesAndPricing": "Konfiguro rregullat e lojës dhe çmimet", "gameTables": "Tavolinat e Lojës", "manageBilliardTables": "Menaxho tavolinat e biliardos", "fix": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Rivendos", "searchTables": "Kërko tavolina...", "all": "<PERSON><PERSON> gjitha", "selected": "I Zgjedhur", "clear": "<PERSON><PERSON>", "noTablesMatchSearch": "Asnjë tavolinë nuk përputhet me kërkimin tuaj", "noGameTablesFound": "Nuk u gjetën tavolina loje", "tryAdjustingSearch": "<PERSON>voni të ndryshoni kërkimin", "addFirstTableToStart": "Shtoni tavolinën tuaj të parë për të filluar", "selectAll": "Zgjidh të gjitha", "rate": "<PERSON><PERSON><PERSON>", "type": "L<PERSON>ji", "assigned": "Caktuar", "shared": "E ndarë", "category": "Kategoria", "testSound": "<PERSON><PERSON>", "removeSound": "Hiq", "uploadingSoundFile": "<PERSON> ng<PERSON><PERSON><PERSON> s<PERSON> e <PERSON>...", "soundUploadedSuccessfully": "Zëri u ngarkua me sukses", "failedToUploadSound": "Dështoi ngarkimi i skedarit të zërit", "invalidSoundFile": "Skedar zëri i pavlefshëm. Ju lutemi ngarkoni një skedar audio (MP3, WAV, OGG, M4A, WebM, AAC)", "soundFileTooLarge": "Skedari i zërit është shumë i madh. Madhësia maksimale është 5MB", "failedToPlaySound": "Dështoi luajtja e zërit. Ju lutemi kontrolloni skedarin", "hideSoundLibrary": "Fshih Bibliotekën e Zërave", "showSoundLibrary": "Shfaq Bibliotekën e Zërave", "previouslyUploadedSounds": "Zërat e Ngarkuar Më Parë", "noSoundFilesUploaded": "Nuk ka skedarë zëri të ngarkuar ende. Ngarkoni skedarin tuaj të parë të zërit më sipër", "useThisSound": "Përdor këtë zë", "deleteSound": "<PERSON>shi zërin", "currentlySelected": "aktualisht i zgjedhur", "currentlyAssigned": "Zëri i Caktuar Aktualisht", "clickToSelect": "Kliko për të zgjedhur këtë zë", "clickToUse": "Kliko për të përdorur", "failedToDeleteSound": "Dështoi fshirja e skedarit të zërit", "soundFileDescription": "Ngarkoni një skedar audio (MP3, WAV, OGG, M4A, WebM, AAC) deri në 5MB, ose shkruani një URL për një skedar zëri online", "soundFileStoredLocally": "Ky skedar është ruajtur lokalisht në aplikacionin tuaj", "hourlyRateLabel": "<PERSON><PERSON><PERSON> për <PERSON>:", "tableTypeLabel": "Lloji i Tavolinës:", "statusLabel": "Statusi:", "availableForGames": "E Disponueshme p<PERSON>", "order": "<PERSON><PERSON><PERSON>", "totalTables": "Tavolina Gjithsej", "activeTables": "Tavolina Aktive", "barTables": "Tavolina Bari", "tableNumberLabel": "Numri i Tavolinës", "barMenuManagement": "Menaxhimi i Menysë së Barit", "addNewBarMenuItem": "Shto Artikull të Ri në Meny", "addNewMenuItemDescription": "Shto një artikull të ri në menynë e barit", "editProduct": "Ndrysho Produktin", "updateProduct": "Përditëso Produktin", "addProduct": "Shto Produkt", "productName": "Emri i Produktit", "productPrice": "Çmimi i Produktit", "productCategory": "Kategoria e Produktit", "productDescription": "Përshkrimi i Produktit", "briefProductDescription": "Përshkrim i shkurtër i produktit", "failedToDeleteProduct": "Dështoi fshirja e produktit", "someProductsCouldNotBeDeleted": "Disa produkte nuk mund të fshiheshin", "itemsCount": "{{count}} artikuj", "allCategories": "Të Gjitha <PERSON>", "allProducts": "Të Gjithë Produktet", "add": "Shto", "price": "<PERSON><PERSON><PERSON>", "addNewBarCategory": "Shto Kategori të Re Bar", "createNewCategory": "Krijo një kategori të re për organizimin e artikujve të menysë", "editCategory": "<PERSON><PERSON><PERSON><PERSON>", "updateCategory": "Përdi<PERSON><PERSON><PERSON>", "businessInformation": "Informacioni i Biznesit", "businessName": "Emri i Biznesit", "enterBusinessName": "Shkruaj emrin e biznesit", "enterBusinessAddress": "Shkruaj adresën e biznesit", "enterPhoneNumber": "Shkruaj numrin e telefonit", "enterEmailAddress": "Shkruaj adresën e email-it", "enterVatNumber": "Shkruaj numrin e TVSH", "address": "<PERSON><PERSON><PERSON>", "phone": "Telefoni", "email": "Email-i", "vatNumber": "Numri i TVSH-së", "saveBusinessInformation": "Ruaj Informacionin e Biznesit", "currencySettings": "Cilësimet e Monedhës", "currencyName": "<PERSON><PERSON>ed<PERSON>", "selectCurrency": "<PERSON><PERSON><PERSON><PERSON>", "albanianLek": "Leku <PERSON>ptar", "euro": "Euro", "usDollar": "Dollari Amerikan", "britishPound": "Paundi Britanik", "swissFranc": "Franga Zvicerane", "currencyUsedInBusiness": "Monedha e përdorur në biznesin tuaj", "currencySymbol": "Simboli i Monedhës", "symbolDisplayedWithPrices": "Simboli i shfaqur me çmimet (p.sh., L, €, $)", "taxPercentageApplied": "Përqindja e taksës e aplikuar në të gjitha shitjet", "showDecimals": "<PERSON><PERSON><PERSON><PERSON>", "displayPricesWithDecimals": "<PERSON><PERSON><PERSON>q çmimet me .00 (p.sh., 50.00 L kundrejt 50 L)", "enableTax": "Aktivizo Taksën", "turnTaxCalculationOnOff": "Ndiz/fik llogaritjen e taksës për të gjitha shitjet", "taxIncludedInPrices": "Taksa e Përfshirë në Çmimet e Produkteve", "taxIncludedNote": "Çmimet e produkteve përfshijnë taksën (taksa do të shfaqet veçmas në fatura)", "taxExcludedNote": "Çmimet e produkteve nuk përfshijnë taksën (taksa do të shtohet në checkout)", "qrCodeSettings": "Cilësimet e Kodit QR", "includeQrCodeOnReceipts": "Përfshi Kodin QR në Fatura", "addQrCodeToReceipts": "Shto një kod QR në të gjitha faturat e printuara", "qrCodeUrl": "URL-ja e Kodit QR", "qrCodeUrlPlaceholder": "https://example.com", "qrCodeUrlDescription": "Vendosni URL-në ku klientët do të ridrejtohen kur skanojnë kodin QR. Lëreni bosh për të shfaqur informacionin e kontaktit të biznesit.", "saveCurrencySettings": "Ruaj Cilësimet e Monedhës", "loginSettingsTitle": "Cilësimet e Hyrjes", "database": "Baza e të Dhënave", "settingsLoadedFromDatabase": "Cilësimet u ngarkuan nga baza e të dhënave dhe do të ruhen në të gjitha pajisjet", "waitersAccounts": "Llogaritë e Kamarier", "waiterOne": "Kamarieria 1", "waiterTwo": "Kamarieria 2", "waiter1Placeholder": "kamarieria1", "waiter2Placeholder": "kamarieria2", "enableWaitersAccountsSection": "Aktivizo Seksionin e Llogarive të Kamarierëve", "showQuickLoginButtons": "Sh<PERSON>q butonat e hyrjes së shpejtë për kamarierët në faqen e hyrjes", "sectionTitle": "Titulli i Seksionit", "titleDisplayedAboveButtons": "Titulli i shfaqur mbi butonat e hyrjes së kamarierit", "waiterOneSettings": "Cilësimet e Kamarierit 1", "waiterTwoSettings": "Cilësimet e Kamarierit 2", "displayName": "Emri i Shfaqjes", "username": "Emri i Përdoruesit", "password": "Fjalëkalimi", "preview": "Pamja e Paraprakme", "saveLoginSettings": "Ruaj Cilësimet e Hyrjes", "configureWaiterLoginAccounts": "Konfiguro llogaritë e hyrjes së kamarierëve", "localStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Parazgjedhur", "password1Placeholder": "fjalëkalimi1", "password2Placeholder": "fjalëkalimi2", "confirmDeleteProducts": "<PERSON>i i sigurt që doni të fshini {{count}} produkt(e)?\\n\\nProduktet: {{products}}\\n\\nKy veprim nuk mund të zhbëhet.", "confirmDeleteProduct": "<PERSON>i i sigurt që doni të fshini \"{{name}}\"?\\n\\nKy veprim nuk mund të zhbëhet.", "addItem": "<PERSON><PERSON><PERSON> Artik<PERSON>", "permissionsManagement": "Menaxhimi i Lejeve", "permissionsDescription": "Kontrolloni çfarë mund të shohin dhe bëjnë rolet e ndryshme të përdoruesve në sistem.", "waiters": "<PERSON><PERSON>ier<PERSON>", "admins": "<PERSON><PERSON>", "waiterPermissions": "Lejet e Kamarierit", "save": "<PERSON><PERSON><PERSON>", "gameManagement": "Menaxhimi i Lojërave", "viewAllTables": "Shiko të Gjit<PERSON>", "viewAllTablesDescription": "Mund të shohë të gjitha tavolinat aktive të lojës nga të gjithë përdoruesit", "viewOwnTablesOnly": "Shiko Vetëm Tavolinat e Veta", "viewOwnTablesOnlyDescription": "Mund të shohë vetëm tavolinat e lojës që ka krijuar", "createNewGames": "<PERSON><PERSON><PERSON> të Reja", "createNewGamesDescription": "Mund të fillojë seanca të reja loje", "stopAnyGame": "<PERSON>dalo <PERSON>", "stopAnyGameDescription": "Mund të ndalojë çdo tavolinë aktive loje", "viewAllHistory": "Shiko të Gjithë Historikun", "viewAllHistoryDescription": "Mund të shohë historikun e lojërave nga të gjithë përdo<PERSON>t", "denied": "E Mohuar", "allowed": "<PERSON>", "barOrders": "Porositë e Barit", "viewAllOrders": "Shiko të Gjitha Porositë", "viewAllOrdersDescription": "Mund të shohë të gjitha porositë e barit nga të gjithë përdoruesit", "viewOwnOrdersOnly": "Shiko Vetëm Porositë e Veta", "viewOwnOrdersOnlyDescription": "Mund të shohë vetëm porositë e barit që ka krijuar", "createOrders": "<PERSON><PERSON><PERSON>", "createOrdersDescription": "Mund të krijojë porosia të reja bari", "editAnyOrder": "Përpuno <PERSON>do <PERSON>", "editAnyOrderDescription": "Mund të modifikojë çdo porosi bari", "analytics": "<PERSON><PERSON><PERSON>", "viewAnalytics": "<PERSON><PERSON>", "viewAnalyticsDescription": "Mund të qaset në panelin e analitikës", "viewOwnStats": "Shiko Statistikat e Veta", "viewOwnStatsDescription": "Mund të shohë statistikat personale të performancës", "settingsPermissions": "Cilësimet", "accessSettings": "Qasje në Cilësime", "accessSettingsDescription": "Mund të qaset në menynë e cilësimeve", "manageUsers": "<PERSON><PERSON><PERSON>", "manageUsersDescription": "Mund të krijojë, përpunojë dhe fshijë përdorues", "managePermissions": "<PERSON><PERSON><PERSON>", "managePermissionsDescription": "Mund të modifikojë lejet e përdoruesve", "userManagement": "Menaxhimi i Përdoruesve", "manageUserAccountsAndRoles": "Menaxho llogaritë e përdoruesve dhe rolet", "userManagementCenter": "Qendra e Menaxhimit të Përdoruesve", "createEditManageUserAccounts": "<PERSON><PERSON><PERSON>, ndrysho dhe menaxho llogaritë e përdoruesve dhe rolet e tyre. Kontrollo lejet e aksesit dhe cilësimet e përdoruesve.", "openUserManagement": "Hap Menaxhimin e Përdoruesve", "barCategories": "Kategoritë e Barit", "organizeMenuItemsIntoCategories": "Organizoni artikujt e menysë në kategori", "organizeMenuItems": "Organizoni artikujt e menysë"}, "activeGames": {"title": "Biliardo Aktive", "active": "aktive", "noGamesYet": "Nuk ka seanca biliardoje ende", "startGameToSeeActivity": "Fillo një seancë biliardoje për të parë aktivitetin", "recently": "Së fundmi", "recentCompleted": "Të Përfunduara Së Fundmi"}, "errors": {"networkError": "Gabim në rrjet. Ju lutemi kontrolloni lidhjen tuaj.", "serverError": "Gabim në server. Ju lutemi provoni përs<PERSON><PERSON> më von<PERSON>.", "validationError": "Ju lutemi kontrolloni të dhënat tuaja dhe provoni përsëri.", "permissionDenied": "Leja u refuzua. Kontaktoni administratorin.", "notFound": "<PERSON><PERSON><PERSON> nuk u gjet.", "quotaExceeded": "Kuota e ruajtjes u tejkalua. Duke pastruar...", "offlineMode": "Jeni offline. Ndryshimet do të sinkronizohen kur të rikthehet lidhja."}, "success": {"saved": "Ndryshimet u ruajtën me sukses", "deleted": "Artikulli u fshi me sukses", "synced": "Të dhënat u sinkronizuan me sukses", "cacheCleared": "Cache u pastrua me sukses"}, "storage": {"online": "Online", "clearCache": "<PERSON><PERSON>"}, "receipts": {"title": "<PERSON><PERSON><PERSON>", "transactionHistory": "Historiku i Transaksioneve", "filtersAndSearch": "Filtrat dhe Kërkimi", "searchTransactions": "Kërko transaksione...", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "lastUpdated": "Përditësuar së fundmi", "loading": "<PERSON> ng<PERSON><PERSON><PERSON> trans<PERSON>t...", "noTransactions": "Nuk u gjetën transaksione", "type": "L<PERSON>ji", "table": "Tavolina", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON>", "amount": "<PERSON><PERSON>", "actions": "Veprimet", "view": "<PERSON><PERSON>", "reprint": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Fshi", "game": "Loj<PERSON>", "order": "<PERSON><PERSON><PERSON>", "allTypes": "Të Gjitha Llo<PERSON>", "allTables": "Të Gjit<PERSON>", "allUsers": "Të Gjithë Përdoruesit", "dateRange": "Intervali i Datës", "selectDate": "<PERSON><PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "clearFilters": "<PERSON><PERSON>", "exportData": "Eksporto Të Dhënat", "receiptDetails": "Detajet e Faturës", "receiptNumber": "Numri i Faturës", "printedBy": "<PERSON><PERSON>r nga", "printedAt": "<PERSON><PERSON>r nga", "duration": "Kohëzgjatja", "startTime": "<PERSON><PERSON> <PERSON>llimit", "endTime": "Koha e Përfundimit", "items": "Artikujt", "subtotal": "Nëntotali", "tax": "<PERSON><PERSON><PERSON>", "total": "Totali", "close": "<PERSON><PERSON>ll", "receiptPreview": "Pamja e Faturës", "date": "Data", "pickDateRange": "Zgjidh Intervalin e Datës", "desc": "Përshkrimi", "exportCSV": "Eksporto CSV", "selected": "të zg<PERSON>", "totalTransactions": "Totali i Transaksioneve", "pageAmount": "<PERSON><PERSON> e Faqes", "pageAvg": "Mesatarja e Faqes", "filterByType": "Filtro sipas Llojit", "filterByTable": "Filtro sipas Tavolinës", "filterByUser": "Filtro sipas Përdoruesit", "gameSessions": "Seancat e Biliardos", "barOrders": "Porositë e Barit", "sortBy": "Ren<PERSON> sipas", "asc": "<PERSON><PERSON><PERSON><PERSON>", "noPermission": "<PERSON>uk keni leje"}, "analytics": {"todaysRevenue": "Të Ardhurat e Sotme", "averageGameDuration": "Kohëzgjatja Mesatare e Lojës", "peakHours": "Orët e Pikut", "consistent": "I Qëndrueshëm", "barOrders": "Porositë e Barit", "orders": "Porositë", "tableUsageByHour": "Përdorimi i Tavolinave sipas Orës", "weeklyRevenue": "Të Ardhurat Javore", "games": "<PERSON><PERSON><PERSON>", "performanceMetrics": "Metrikat e Performancës", "dailyOverview": "📅 Përm<PERSON><PERSON>ja <PERSON>tore", "mostUsedTable": "Tavolina më e Përdorur", "tableNA": "Tavolina N/A", "topDrink": "Pija më e Shitur", "na": "N/A", "activeTables": "Tavolinat Aktive", "gameRoomAnalytics": "🎱 Analitika e Dhomës së Biliardos", "tableUtilization": "Shfrytëzimi i Tavolinave", "avgGameDuration": "Kohëzgjatja Mesatare e Seancës", "gamesToday": "Seancat e Biliardos Sot", "peakHour": "Ora e Pikut", "barPerformance": "🍺 Performanca e Barit", "barRevenueToday": "Të Ardhurat e Barit Sot", "ordersToday": "Porositë Sot", "avgOrderValue": "Vlera Mesatare e Porosisë", "bestseller": "Më i Shituri", "monthlyReport": "📈 <PERSON><PERSON><PERSON>", "totalRevenue": "Të Ardhurat Totale", "gamesRevenue": "🎱 Të Ardhurat nga Biliardo", "barRevenue": "🍺 Të Ardhurat nga Bari", "peakActivity": "Aktiviteti i Pikut", "performanceMetricsTitle": "📊 Metrikat e Performancës", "noDataAvailable": "Nuk ka të dhëna të disponueshme", "tableUsageDataWillAppear": "Të dhënat e përdorimit të tavolinave do të shfaqen këtu pasi të krijohen lojëra dhe porosi", "accessDenied": "Qasja u Refuzua", "noPermission": "Nuk keni leje për të parë analitikën", "loadError": "Dështoi ngarkimi i të dhënave të analitikës", "tables": "tavolina"}, "receipt": {"print": "<PERSON><PERSON>", "reprint": "<PERSON><PERSON><PERSON><PERSON>", "receipt": "<PERSON><PERSON>", "gameReceipt": "Fatura e Lojës", "orderReceipt": "Fatura e Porosisë", "receiptNumber": "Fatura #", "table": "Tavolina", "date": "Data", "time": "<PERSON><PERSON>", "duration": "Kohëzgjatja", "amount": "<PERSON><PERSON>", "total": "Totali", "thankYou": "Faleminderit!", "visitAgain": "Na vizitoni përsëri së shpejti"}, "profile": {"userProfile": "Profili i Përdoruesit", "profile": "Profili", "security": "Siguria", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profileInformation": "Informacioni i Profilit", "changePhoto": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "Emri i Plotë", "enterFullName": "Shkruani emrin tuaj të plotë", "username": "Emri i Përdoruesit", "enterUsername": "Shkruani emrin tuaj të përdoruesit", "cancel": "<PERSON><PERSON>", "saveChanges": "<PERSON><PERSON><PERSON>", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "currentPassword": "Fjalëkalimi Aktual", "enterCurrentPassword": "Shkruani fjalëkalimin tuaj aktual", "newPassword": "Fjalëkalimi i Ri", "enterNewPassword": "Shkruani fjalëkalimin tuaj të ri", "confirmNewPassword": "Konfirmo Fjalëkalimin e Ri", "enterConfirmNewPassword": "Konfirmoni fjalëkalimin tuaj të ri", "passwordFieldsNote": "Fjalëkalimi duhet të jetë të paktën 8 karaktere"}, "users": {"userManagement": "Menaxhimi i Përdoruesve", "usersTotal": "{{count}} p<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "addUser": "Shto Përdorues", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "gjit<PERSON><PERSON>", "active": "aktiv", "loadingUsers": "<PERSON> <PERSON><PERSON><PERSON><PERSON>...", "noUsersFound": "Nuk u gjetën përdorues", "clickAddUserToCreate": "Klik<PERSON> \"Shto Përdorues\" për të krijuar përdoruesin tuaj të parë", "createNewUser": "<PERSON><PERSON><PERSON> të Ri", "username": "Emri i Përdoruesit", "enterUsername": "Shkruaj emrin e përdoruesit", "fullName": "Emri i Plotë", "enterFullName": "Shkruaj em<PERSON> e <PERSON>", "role": "Roli", "waiter": "<PERSON><PERSON><PERSON>", "admin": "Administrator", "password": "Fjalëkalimi", "enterPassword": "Shk<PERSON>aj f<PERSON>", "activeUser": "Përdorues Aktiv", "cancel": "<PERSON><PERSON>", "creating": "<PERSON> krijuar...", "createUser": "<PERSON><PERSON><PERSON>", "usernameAndNameRequired": "Em<PERSON> i përdoruesit dhe emri i plotë janë të detyrueshëm", "passwordRequired": "Fjalëkalimi është i detyrueshëm", "userCreatedSuccessfully": "Përdoruesi u krijua me sukses", "failedToCreateUser": "Dështoi krijimi i përdoruesit", "userUpdatedSuccessfully": "Përdoruesi u përditësua me sukses", "failedToUpdateUser": "Dështoi përditësimi i përdoruesit", "confirmDeleteUser": "<PERSON>i i sigurt që doni të fshini pë<PERSON> \"{{username}}\"?", "userDeletedSuccessfully": "Përdoruesi u fshi me sukses", "failedToDeleteUser": "Dështoi fshirja e përdoruesit", "inactive": "Joaktiv", "created": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Zgjero", "collapse": "<PERSON><PERSON>ll", "delete": "Fshi", "editUserDetails": "Ndrysho Detajet e Përdoruesit", "saving": "Duke ruajtur...", "save": "<PERSON><PERSON><PERSON>", "newPasswordOptional": "Fjalëkalimi i Ri (opsional)", "enterNewPassword": "Shkruaj fjalëkalimin e ri", "userDetails": "Detajet e Përdoruesit", "edit": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statusi", "editUser": "<PERSON><PERSON><PERSON><PERSON>"}, "database": {"online": "Online", "offline": "Offline", "offlineMessage": "Baza e të dhënave nuk është e disponueshme aktualisht. Disa funksione mund të jenë të kufizuara."}, "hardcoded": {"player": "<PERSON><PERSON><PERSON><PERSON>", "now": "<PERSON><PERSON>", "recently": "Së fundmi", "timesUp": "<PERSON><PERSON>!", "left": "mbetur", "secondsLeft": "s mbetur", "minutesLeft": "m mbetur", "hoursLeft": "h mbetur", "albanianLek": "Leku <PERSON>ptar", "barBilardo": "Bar-Bilardo", "billiardClub": "KLUBI I BILIARDOS", "scanForMoreInfo": "Skanoni për më shumë informacion", "scanForContactInfo": "Skanoni për informacion kontakti", "systemAutoPrint": "Printim Automatik i Sistemit", "system": "<PERSON><PERSON><PERSON>", "courierNew": "Courier New", "libreBarcode": "Libre Barcode 39", "contentType": "Lloji i Përmbajtjes", "applicationJson": "aplikacion/json", "total": "TOTALI", "date": "Data"}}}