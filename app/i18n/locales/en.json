{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "clear": "Clear", "all": "All", "active": "Active", "inactive": "Inactive", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "navigation": {"dashboard": "Dashboard", "games": "Games", "bar": "Bar", "analytics": "Analytics", "settings": "Settings", "reports": "Reports"}, "games": {"title": "Games", "activeTables": "Active Tables", "liveControlMonitoring": "Live control & monitoring", "startGame": "Start Game", "stopGame": "Stop Game", "stop": "STOP", "table": "Table", "tableName": "Table Name", "tableNumber": "Table {{number}}", "tableShort": "T", "duration": "Duration", "player": "Player", "price": "Price", "cost": "Cost", "time": "Time", "end": "End", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "now": "Now", "recently": "Recently", "unknown": "Unknown", "defaultTimeLimit": "Default Time Limit", "soundOn": "Sound On", "soundOff": "Sound Off", "makeAllAvailable": "Make All Available", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "todaysSessions": "Today's Sessions", "timeLimitReached": "Time Limit Reached", "timeLimitReachedBody": "One or more billiard sessions have reached their time limit.", "noGamesToday": "No billiard sessions today", "gameSessionReceipt": "Game Session Receipt", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax ({{rate}}%)", "total": "Total", "unlimited": "Unlimited", "oneHour": "1 Hour", "twoHours": "2 Hours", "threeHours": "3 Hours", "fourHours": "4 Hours", "fiveHours": "5 Hours", "sixHours": "6 Hours", "tables": "tables", "playing": "playing", "gamesCount": "sessions", "readyToPlay": "Ready to Play", "tapToStart": "Tap to Start", "perHour": "/hr", "change": "Change", "offline": "Offline", "contactStaff": "Contact Staff", "timesUp": "Time's Up!", "secondsLeft": "{{seconds}}s left", "minutesLeft": "{{minutes}}m left", "hoursMinutesLeft": "{{hours}}h {{minutes}}m left", "default": "<PERSON><PERSON><PERSON>", "noLimit": "No limit"}, "bar": {"title": "Bar", "selectTable": "Select Table", "menu": "<PERSON><PERSON>", "addToOrder": "Add to Order", "placeOrder": "Place Order", "orderTotal": "Order Total", "onHold": "on hold", "clearAll": "Clear All", "pending": "Pending", "clear": "Clear", "selectTableToOrder": "Select a table to start ordering", "tablesOnHold": "tables on hold", "currentOrder": "Current Order", "submitOrderAndPrint": "Submit Order & Print", "todaysOrders": "Today's Orders", "noOrdersToday": "No orders today", "noOrdersCompletedToday": "No orders completed today", "smartBar": "Smart Bar", "activeTables": "active tables", "utilization": "utilization", "smartMenu": "Smart Menu", "items": "items", "reports": "Daily Reports", "monthlyReports": "Monthly Reports", "smartAnalytics": "Smart Analytics", "realTimeInsights": "Real-time insights", "lastUpdated": "Last updated", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "searchMenuItems": "Search menu items...", "all": "All", "quick": "Quick", "popular": "Popular", "recent": "Recent", "selectTableToStartOrdering": "Select a Table", "chooseTableToStartOrdering": "Choose a table to start ordering", "chooseFromAvailableTables": "Choose from {{count}} available tables above", "noItemsFound": "No items found", "noItemsMatch": "No items match \"{{query}}\"", "noItemsInCategory": "No items in this category", "clearSearch": "Clear search", "noMenuItems": "No menu items", "addProductsInSettings": "Add products in Settings", "addItemsFromMenu": "Add items from the menu", "tableReady": "Table {{number}} is ready", "each": "each", "avgPerItem": "Avg {{amount}}L/item", "table": "Table", "unknown": "Unknown", "item": "item", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "chooseWhatToInclude": "Choose what to include in the {{reportType}} report:", "gamesOnly": "Games Only", "ordersOnly": "Orders Only", "bothGamesAndBar": "Both Games and Bar", "cancel": "Cancel", "generateReport": "Generate Report", "barOrderReceipt": "Bar Order Receipt", "receipt": "Receipt", "date": "Date", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax", "total": "Total", "thankYouForOrder": "Thank you for your order!", "enjoyYourDrinks": "Enjoy your drinks!", "scanForMoreInfo": "Scan for more info", "scanForContactInfo": "Scan for contact info", "drinks": "Drinks", "snacks": "Snacks", "food": "Food", "albanianLek": "Albanian Lek", "completed": "completed", "active": "Active", "inactive": "Inactive"}, "settings": {"title": "Settings", "businessInfo": "Business Information", "currency": "<PERSON><PERSON><PERSON><PERSON>", "gameSettings": "Billiard Settings", "loginSettings": "<PERSON><PERSON>", "language": "Language", "selectLanguage": "Select Language", "english": "English", "albanian": "Albanian", "categories": "Categories", "tables": "Tables", "barMenu": "Bar Menu", "permissions": "Permissions", "barCategories": "Bar Categories", "organizeMenuItemsIntoCategories": "Organize your menu items into categories", "organizeMenuItems": "Organize menu items", "addCategory": "Add Category", "active": "Active", "inactive": "Inactive", "edit": "Edit", "businessName": "Business Name", "enterBusinessName": "Enter business name", "businessAddress": "Business Address", "enterBusinessAddress": "Enter business address", "businessPhone": "Business Phone", "enterBusinessPhone": "Enter business phone", "businessEmail": "Business Email", "enterBusinessEmail": "Enter business email", "currencyName": "Currency Name", "currencyUsedInBusiness": "The currency used in your business", "currencySymbol": "Currency Symbol", "symbolDisplayedWithPrices": "Symbol displayed with prices (e.g., L, €, $)", "taxRate": "Tax Rate (%)", "taxPercentageApplied": "Tax percentage applied to all sales", "showDecimals": "Show Decimals", "displayPricesWithDecimals": "Display prices with .00 (e.g., 50.00 L vs 50 L)", "defaultHourlyRate": "Default Hourly Rate (L)", "minimumGameTime": "Minimum Game Time (minutes)", "autoEndAfterHours": "Auto-end After (hours)", "autoPrintReceipts": "Auto-print receipts when games end", "saveSettings": "Save Settings", "saveUpdateAllTables": "Save & Update All Tables"}, "analytics": {"title": "Analytics", "dashboard": "Dashboard", "overview": "Overview", "revenue": "Revenue", "performance": "Performance", "insights": "Insights", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "monthlyRevenue": "Monthly Revenue", "revenueOverview": "Revenue Overview", "tableUsageByHour": "Table Usage by Hour", "performanceMetrics": "Performance Metrics", "noDataAvailable": "No data available"}, "errors": {"networkError": "Network connection error", "serverError": "Server error occurred", "validationError": "Validation error", "permissionDenied": "Permission denied", "notFound": "Not found", "quotaExceeded": "Storage quota exceeded", "offlineMode": "You are currently offline"}, "success": {"saved": "Successfully saved", "deleted": "Successfully deleted", "synced": "Successfully synchronized", "cacheCleared": "<PERSON><PERSON> cleared successfully"}}