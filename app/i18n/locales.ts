// Calendar locale configuration without date-fns dependency

// Calendar month names for React Day Picker
export const calendarLocales = {
  en: {
    months: [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ],
    weekdaysLong: [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ],
    weekdaysShort: ['<PERSON>', '<PERSON>', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    firstDayOfWeek: 1, // Monday
    weekStartsOn: 1
  },
  sq: {
    months: [
      '<PERSON>r', '<PERSON>hku<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'
    ],
    weekdaysLong: [
      '<PERSON>l', '<PERSON> Hën<PERSON>', '<PERSON> Mart<PERSON>', '<PERSON>ur<PERSON>', 
      '<PERSON> Enj<PERSON>', '<PERSON> Prem<PERSON>', '<PERSON>'
    ],
    weekdaysShort: ['<PERSON>', '<PERSON>ën', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Enj', 'Pre', 'Sht'],
    firstDayOfWeek: 1, // Monday
    weekStartsOn: 1
  }
}

// Get calendar locale configuration
export function getCalendarLocale(languageCode: string) {
  return calendarLocales[languageCode as keyof typeof calendarLocales] || calendarLocales.en
}
