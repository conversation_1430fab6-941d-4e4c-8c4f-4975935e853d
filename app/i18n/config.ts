import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translation files
import enTranslations from './locales/en.json'
import sqTranslations from './locales/sq.json'

const resources = {
  en: {
    translation: enTranslations
  },
  sq: {
    translation: sqTranslations
  }
}

// Only initialize on client side
if (typeof window !== 'undefined') {
  try {
    i18n
      .use(LanguageDetector)
      .use(initReactI18next)
      .init({
        resources,
        fallbackLng: 'sq',
        debug: false, // Disable debug to reduce console noise

        detection: {
          order: ['localStorage'],
          caches: ['localStorage'],
          lookupLocalStorage: 'i18nextLng'
        },

        interpolation: {
          escapeValue: false // React already does escaping
        },

        // Namespace configuration
        defaultNS: 'translation',
        ns: ['translation'],

        // React specific options
        react: {
          useSuspense: false
        }
      })
      .catch((error) => {
        console.warn('Failed to initialize i18n on client:', error)
      })
  } catch (error) {
    console.warn('Error setting up i18n on client:', error)
  }
} else {
  // Server-side fallback - just initialize with basic config
  try {
    i18n.init({
      resources,
      fallbackLng: 'sq',
      interpolation: {
        escapeValue: false
      },
      react: {
        useSuspense: false
      }
    })
  } catch (error) {
    console.warn('Error initializing i18n on server:', error)
  }
}

export default i18n
