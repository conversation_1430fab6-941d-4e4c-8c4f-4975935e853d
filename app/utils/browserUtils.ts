"use client"

import { clearAllStorage } from './storage'

// Simple browser utilities for clearing cache and storage
export const clearBrowserData = async () => {
  try {
    console.log('🧹 Starting browser data clear...')
    await clearAllStorage()
    console.log('✅ Browser data cleared successfully')
  } catch (error) {
    console.error('❌ Failed to clear browser data:', error)
    // Force reload anyway
    window.location.reload()
  }
}

// Clear only non-auth data (UI preferences and temporary data)
export const clearNonAuthData = async () => {
  try {
    console.log('🧹 Clearing non-auth data...')
    const { getStorageManager } = await import('./storage')

    // Clear memory storage (temporary data)
    const memoryStorage = getStorageManager('memory')
    await memoryStorage.clear()

    // Clear localStorage (UI preferences) but keep auth cookies
    const localStorage = getStorageManager('local')
    await localStorage.clear()

    console.log('✅ Non-auth data cleared successfully')
  } catch (error) {
    console.error('❌ Failed to clear non-auth data:', error)
  }
}

// Check if localStorage is accessible
export const isLocalStorageAvailable = () => {
  try {
    return storageManager.setItem('__test__', 'test') && storageManager.removeItem('__test__')
  } catch (error) {
    return false
  }
}

// Validate and fix storage
export const validateAndFixStorage = () => {
  return storageManager.validateAndFix()
}
