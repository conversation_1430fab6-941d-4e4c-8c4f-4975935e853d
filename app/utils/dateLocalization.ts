import { format, Locale } from 'date-fns'
import { enUS } from 'date-fns/locale'

// Albanian locale configuration for date-fns
const albanianLocale: Locale = {
  code: 'sq',
  formatDistance: () => '',
  formatRelative: () => '',
  localize: {
    ordinalNumber: (dirtyNumber: any) => String(dirtyNumber),
    era: () => '',
    quarter: () => '',
    month: (month: number) => {
      const months = [
        'Janar', 'Shkurt', 'Mars', 'Prill', 'Maj', 'Qershor',
        'Ko<PERSON><PERSON>', 'Gusht', 'Shtator', 'Tetor', 'Nëntor', 'Dhjetor'
      ]
      return months[month]
    },
    day: (day: number) => {
      const days = ['E Diel', 'E Hënë', 'E Martë', 'E Mërkurë', 'E Enj<PERSON>', 'E Premte', 'E Shtunë']
      return days[day]
    },
    dayPeriod: () => ''
  },
  formatLong: {
    date: () => 'dd/MM/yyyy',
    time: () => 'HH:mm',
    dateTime: () => 'dd/MM/yyyy HH:mm'
  },
  match: {
    ordinalNumber: () => ({ value: 0, rest: '' }),
    era: () => ({ value: 0, rest: '' }),
    quarter: () => ({ value: 0, rest: '' }),
    month: () => ({ value: 0, rest: '' }),
    day: () => ({ value: 0, rest: '' }),
    dayPeriod: () => ({ value: 0, rest: '' })
  },
  options: {
    weekStartsOn: 1,
    firstWeekContainsDate: 1
  }
}

// Get the appropriate locale based on language code
export function getDateFnsLocale(languageCode: string): Locale {
  switch (languageCode) {
    case 'sq':
      return albanianLocale
    default:
      return enUS
  }
}

// Format date with proper localization
export function formatLocalizedDate(date: Date, formatString: string, languageCode: string): string {
  const locale = getDateFnsLocale(languageCode)

  // For Albanian, use custom formatting to ensure proper month names
  if (languageCode === 'sq') {
    const months = ['Jan', 'Shk', 'Mar', 'Pri', 'Maj', 'Qer', 'Kor', 'Gus', 'Sht', 'Tet', 'Nën', 'Dhj']
    const monthsFull = ['Janar', 'Shkurt', 'Mars', 'Prill', 'Maj', 'Qershor', 'Korrik', 'Gusht', 'Shtator', 'Tetor', 'Nëntor', 'Dhjetor']
    const day = date.getDate()
    const month = months[date.getMonth()]
    const monthFull = monthsFull[date.getMonth()]
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    switch (formatString) {
      case 'LLL dd, y':
      case 'MMM dd, y':
        return `${day} ${month}, ${year}`
      case 'MMM dd, yyyy HH:mm':
        return `${day} ${month}, ${year} ${hours}:${minutes}`
      case 'MMMM yyyy':
        return `${monthFull} ${year}`
      case 'MMM yyyy':
        return `${month} ${year}`
      case 'dd/MM/yyyy':
        return `${day.toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${year}`
      case 'dd MMM yyyy':
        return `${day} ${month} ${year}`
      case 'EEEE, MMMM dd, yyyy':
        const days = ['E Diel', 'E Hënë', 'E Martë', 'E Mërkurë', 'E Enjte', 'E Premte', 'E Shtunë']
        return `${days[date.getDay()]}, ${monthFull} ${day}, ${year}`
      case 'dd MMM yyyy HH:mm':
        return `${day.toString().padStart(2, '0')} ${month} ${year} ${hours}:${minutes}`
      case 'MMM dd, HH:mm':
        return `${day} ${month}, ${hours}:${minutes}`
      case 'yyyy':
        return `${year}`
      default:
        return `${day} ${month}, ${year}`
    }
  }

  try {
    return format(date, formatString, { locale })
  } catch (error) {
    // Fallback to default formatting
    return format(date, formatString, { locale: enUS })
  }
}

// Format time with proper localization
export function formatLocalizedTime(date: Date, languageCode: string): string {
  if (languageCode === 'sq') {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }
  
  try {
    const locale = getDateFnsLocale(languageCode)
    return format(date, 'HH:mm', { locale })
  } catch (error) {
    return format(date, 'HH:mm', { locale: enUS })
  }
}
