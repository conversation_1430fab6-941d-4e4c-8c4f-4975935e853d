import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translation files
import translations from '../i18n/locales/translations.json'

let initializationPromise: Promise<void> | null = null

/**
 * Initialize i18next with proper error handling and fallbacks
 */
export async function initializeI18n(): Promise<void> {
  // Return existing promise if already initializing
  if (initializationPromise) {
    return initializationPromise
  }

  // If already initialized, return immediately
  if (i18n.isInitialized) {
    return Promise.resolve()
  }

  initializationPromise = new Promise(async (resolve, reject) => {
    try {
      await i18n
        .use(LanguageDetector)
        .use(initReactI18next)
        .init({
          resources: {
            en: { translation: translations.en },
            sq: { translation: translations.sq }
          },
          fallbackLng: 'sq',
          debug: false,

          detection: {
            order: ['localStorage'],
            caches: ['localStorage'],
            lookupLocalStorage: 'i18nextLng'
          },

          interpolation: {
            escapeValue: false
          },

          defaultNS: 'translation',
          ns: ['translation'],

          react: {
            useSuspense: false,
            bindI18n: 'languageChanged',
            bindI18nStore: '',
            transEmptyNodeValue: '',
            transSupportBasicHtmlNodes: true,
            transKeepBasicHtmlNodesFor: ['br', 'strong', 'i']
          }
        })

      console.log('✅ i18next initialized successfully')
      resolve()
    } catch (error) {
      console.error('❌ Failed to initialize i18next:', error)
      reject(error)
    }
  })

  return initializationPromise
}

/**
 * Check if i18next is ready to use
 */
export function isI18nReady(): boolean {
  return i18n.isInitialized
}

/**
 * Get safe translation function that works even if i18next is not initialized
 */
export function getSafeTranslation() {
  const fallbackTranslations: Record<string, string> = {
    // Games
    'games.timeLimitReached': 'Time Limit Reached',
    'games.timeLimitReachedBody': 'A game has reached its time limit',
    'games.gameSessionReceipt': 'Game Session Receipt',
    'games.receiptNumber': 'Receipt No',
    'games.table': 'Table',
    'games.startTime': 'Start Time',
    'games.endTime': 'End Time',
    'games.duration': 'Duration',
    'games.thankYouForPlaying': 'Thank you for playing!',
    'games.visitUsAgainSoon': 'Visit us again soon!',
    'games.scanForMoreInfo': 'Scan for more info',
    'games.scanForContactInfo': 'Scan for contact info',
    'games.defaultTimeLimit': 'Default Time Limit',
    'games.activeGames': 'Active Games',
    'games.todaysSessions': 'Today\'s Sessions',
    'games.startGame': 'Start Game',
    'games.endGame': 'End Game',
    'games.printReceipt': 'Print Receipt',
    'games.dailyReport': 'Daily Report',
    'games.monthlyReport': 'Monthly Report',
    'games.noActiveGames': 'No active games',
    'games.noSessionsToday': 'No sessions completed today',
    
    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.export': 'Export',
    'common.import': 'Import',
    'common.refresh': 'Refresh',
    'common.close': 'Close',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.ok': 'OK',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Info',

    // Navigation
    'navigation.dashboard': 'BBM',
    'navigation.games': 'Games',
    'navigation.bar': 'Bar',
    'navigation.analytics': 'Analytics',
    'navigation.settings': 'Settings',
    'navigation.profile': 'Profile',
    'navigation.logout': 'Logout',

    // Settings
    'settings.selectLanguage': 'Select Language',
    'settings.language': 'Language',
    'settings.title': 'Settings',

    // Bar
    'bar.title': 'Bar Management',
    'bar.selectTable': 'Select Table',
    'bar.menu': 'Menu',
    'bar.addToOrder': 'Add to Order',
    'bar.placeOrder': 'Place Order',
    'bar.orderTotal': 'Order Total',
    'bar.onHold': 'on hold',
    'bar.clearAll': 'Clear All',
    'bar.pending': 'Pending',
    'bar.clear': 'Clear',
    'bar.selectTableToOrder': 'Select a table to start ordering',
    'bar.tablesOnHold': 'tables on hold',
    'bar.currentOrder': 'Current Order',
    'bar.submitOrderAndPrint': 'Submit Order & Print',
    'bar.todaysOrders': 'Today\'s Orders',
    'bar.noOrdersToday': 'No orders today',
    'bar.noOrdersCompletedToday': 'No orders completed today',
    'bar.selectTableFirst': 'Select a table first',
    'bar.chooseTableToStartOrdering': 'Choose a table to start ordering',
    'bar.noMenuItems': 'No menu items available',
    'bar.addProductsInSettings': 'Add products in Settings',
    'bar.noItemsInOrder': 'No items in order',
    'bar.each': 'each',
    'bar.total': 'Total',
    'bar.orderDetails': 'Order Details',
    'bar.barOrderReceipt': 'Bar Order Receipt',
    'bar.receipt': 'Receipt',
    'bar.date': 'Date',
    'bar.subtotalExclTax': 'Subtotal (excl. tax)',
    'bar.tax': 'Tax',
    'bar.thankYouForOrder': 'Thank you for your order!',
    'bar.enjoyYourDrinks': 'Enjoy your drinks!',
    'bar.printReceipt': 'Print Receipt',
    'bar.dailyReport': 'Daily Report',
    'bar.monthlyReport': 'Monthly Report',
    'bar.table': 'Table',

    // Dashboard
    'dashboard.title': 'BBM',
    'dashboard.welcome': 'Welcome',
    'dashboard.overview': 'Overview',
    'dashboard.todaysOrders': 'Today\'s Orders',
    'dashboard.todaysSessions': 'Today\'s Sessions',
    'dashboard.activeTables': 'Active Tables',
    'dashboard.recentActivity': 'Recent Activity',
    'dashboard.quickStats': 'Quick Stats',
    'dashboard.totalRevenue': 'Total Revenue',
    'dashboard.activeGames': 'Active Games',
    'dashboard.completedOrders': 'Completed Orders',

    // Auth
    'auth.login': 'Login',
    'auth.logout': 'Logout',
    'auth.username': 'Username',
    'auth.password': 'Password',
    'auth.email': 'Email',
    'auth.loginButton': 'Login',
    'auth.logoutButton': 'Logout',
    'auth.loginError': 'Invalid username or password',
    'auth.sessionExpired': 'Session expired. Please login again.',

    // Storage
    'storage.online': 'Online',
    'storage.offline': 'Offline',
    'storage.clearCache': 'Clear Cache',

    // Database
    'database.online': 'Online',
    'database.offline': 'Offline',
    'database.offlineMessage': 'Database is currently unavailable. Some features may be limited.',

    // Hardcoded strings
    'hardcoded.player': 'Player',
    'hardcoded.now': 'Now',
    'hardcoded.recently': 'Recently',
    'hardcoded.timesUp': 'Time\'s Up!',
    'hardcoded.secondsLeft': '{{seconds}}s left',
    'hardcoded.albanianLek': 'Albanian Lek',
    'hardcoded.barBilardo': 'Bar-Bilardo',
    'hardcoded.scanForMoreInfo': 'Scan for more info',
    'hardcoded.scanForContactInfo': 'Scan for contact info',
    'hardcoded.systemAutoPrint': 'System Auto-Print',
    'hardcoded.system': 'System',
    'hardcoded.total': 'TOTAL',
    'hardcoded.date': 'Date',

    // Profile
    'profile.userProfile': 'User Profile',
    'profile.profile': 'Profile',
    'profile.security': 'Security',
    'profile.users': 'Users',
    'profile.profileInformation': 'Profile Information',
    'profile.changePhoto': 'Change Photo',
    'profile.fullName': 'Full Name',
    'profile.enterFullName': 'Enter your full name',
    'profile.username': 'Username',
    'profile.enterUsername': 'Enter your username',
    'profile.cancel': 'Cancel',
    'profile.saveChanges': 'Save Changes',
    'profile.changePassword': 'Change Password',
    'profile.currentPassword': 'Current Password',
    'profile.enterCurrentPassword': 'Enter your current password',
    'profile.newPassword': 'New Password',
    'profile.enterNewPassword': 'Enter your new password',
    'profile.confirmNewPassword': 'Confirm New Password',
    'profile.enterConfirmNewPassword': 'Confirm your new password',
    'profile.passwordFieldsNote': 'Password must be at least 8 characters long',

    // Users
    'users.userManagement': 'User Management',
    'users.usersTotal': '{{count}} users total',
    'users.addUser': 'Add User',

    // Receipts
    'receipts.title': 'Receipts',
    'receipts.transactionHistory': 'Transaction History',
    'receipts.filtersAndSearch': 'Filters & Search',
    'receipts.searchTransactions': 'Search transactions...',
    'receipts.refresh': 'Refresh',
    'receipts.lastUpdated': 'Last updated',
    'receipts.loading': 'Loading transactions...',
    'receipts.noTransactions': 'No transactions found',
    'receipts.type': 'Type',
    'receipts.table': 'Table',
    'receipts.user': 'User',
    'receipts.time': 'Time',
    'receipts.amount': 'Amount',
    'receipts.actions': 'Actions',
    'receipts.view': 'View',
    'receipts.reprint': 'Reprint',
    'receipts.delete': 'Delete',
    'receipts.game': 'Game',
    'receipts.order': 'Order',
    'receipts.allTypes': 'All Types',
    'receipts.allTables': 'All Tables',
    'receipts.allUsers': 'All Users',
    'receipts.dateRange': 'Date Range',
    'receipts.selectDate': 'Select date',
    'receipts.from': 'From',
    'receipts.to': 'To',
    'receipts.clearFilters': 'Clear Filters',
    'receipts.exportData': 'Export Data',
    'receipts.receiptDetails': 'Receipt Details',
    'receipts.receiptNumber': 'Receipt Number',
    'receipts.printedBy': 'Printed By',
    'receipts.printedAt': 'Printed At',
    'receipts.duration': 'Duration',
    'receipts.startTime': 'Start Time',
    'receipts.endTime': 'End Time',
    'receipts.items': 'Items',
    'receipts.subtotal': 'Subtotal',
    'receipts.tax': 'Tax',
    'receipts.total': 'Total',
    'receipts.close': 'Close',
    'receipts.receiptPreview': 'Receipt Preview',
    'receipts.date': 'Date',
    'receipts.desc': 'Description',
    'receipts.exportCSV': 'Export CSV',
    'receipts.selected': 'selected',
    'receipts.totalTransactions': 'Total Transactions',
    'receipts.pageAmount': 'Page Amount',
    'receipts.pageAvg': 'Page Avg',
    'receipts.filterByType': 'Filter by Type',
    'receipts.filterByTable': 'Filter by Table',
    'receipts.filterByUser': 'Filter by User',
    'receipts.gameSessions': 'Game Sessions',
    'receipts.barOrders': 'Bar Orders',
    'receipts.sortBy': 'Sort by',
    'receipts.order': 'Order',
    'receipts.asc': 'Ascending',
    'receipts.noPermission': 'No Permission',

    // Analytics
    'analytics.todaysRevenue': 'Today\'s Revenue',
    'analytics.averageGameDuration': 'Average Game Duration',
    'analytics.peakHours': 'Peak Hours',
    'analytics.consistent': 'Consistent',
    'analytics.barOrders': 'Bar Orders',
    'analytics.orders': 'Orders',
    'analytics.tableUsageByHour': 'Table Usage by Hour',
    'analytics.weeklyRevenue': 'Weekly Revenue',
    'analytics.games': 'Games',
    'analytics.performanceMetrics': 'Performance Metrics',
    'analytics.dailyOverview': 'Daily Overview',
    'analytics.mostUsedTable': 'Most Used Table',
    'analytics.tableNA': 'Table N/A',
    'analytics.topDrink': 'Top Drink',
    'analytics.na': 'N/A',
    'analytics.activeTables': 'Active Tables',
    'analytics.gameRoomAnalytics': 'Game Room Analytics',
    'analytics.tableUtilization': 'Table Utilization',
    'analytics.avgGameDuration': 'Avg Game Duration',
    'analytics.gamesToday': 'Games Today',
    'analytics.peakHour': 'Peak Hour',
    'analytics.barPerformance': 'Bar Performance',
    'analytics.barRevenueToday': 'Bar Revenue Today',
    'analytics.ordersToday': 'Orders Today',
    'analytics.avgOrderValue': 'Avg Order Value',
    'analytics.bestseller': 'Bestseller',
    'analytics.monthlyReport': 'Monthly Report',
    'analytics.totalRevenue': 'Total Revenue',
    'analytics.gamesRevenue': 'Games Revenue',
    'analytics.barRevenue': 'Bar Revenue',
    'analytics.peakActivity': 'Peak Activity',
    'analytics.performanceMetricsTitle': 'Performance Metrics',
    'analytics.noDataAvailable': 'No data available',
    'analytics.tableUsageDataWillAppear': 'Table usage data will appear here once games and orders are created',
    'analytics.accessDenied': 'Access Denied',
    'analytics.noPermission': 'You don\'t have permission to view analytics',
    'analytics.loading': 'Loading analytics...',
    'analytics.loadError': 'Failed to load analytics data',

    // Settings - recently added translations
    'settings.gameSettings': 'Game Settings',
    'settings.configureGameRulesAndPricing': 'Configure game rules and pricing',
    'settings.gameTables': 'Game Tables',
    'settings.manageBilliardTables': 'Manage billiard tables',
    'settings.fix': 'Fix',
    'settings.reset': 'Reset',
    'settings.searchTables': 'Search tables...',
    'settings.all': 'All',
    'settings.selected': 'selected',
    'settings.activate': 'Activate',
    'settings.deactivate': 'Deactivate',
    'settings.clear': 'Clear',
    'settings.noTablesMatchSearch': 'No tables match your search',
    'settings.noGameTablesFound': 'No game tables found',
    'settings.tryAdjustingSearch': 'Try adjusting your search',
    'settings.addFirstTableToStart': 'Add your first table to get started',
    'settings.selectAll': 'Select all',
    'settings.rate': 'Rate',
    'settings.type': 'Type',
    'settings.assigned': 'Assigned',
    'settings.shared': 'Shared',
    'settings.edit': 'Edit',
    'settings.category': 'Category',
    'settings.inactive': 'Inactive'
  }

  return (key: string, options?: any): string => {
    try {
      // Try to use i18next if it's available and initialized
      if (typeof i18n !== 'undefined' && isI18nReady() && i18n && i18n.isInitialized && typeof i18n.t === 'function') {
        try {
          const translation = i18n.t(key, options)
          if (translation && translation !== key) {
            return translation
          }
        } catch (translationError) {
          console.warn(`i18next translation error for key ${key}:`, translationError)
        }
      }
    } catch (error) {
      console.warn(`Error checking i18next for key ${key}:`, error)
    }

    // Use fallback translation
    const fallback = fallbackTranslations[key]
    if (fallback) {
      return fallback
    }

    // Last resort: return the key itself
    return key
  }
}

/**
 * Safe language change function that works even when i18next is not ready
 */
export async function changeLanguageSafely(languageCode: string): Promise<void> {
  try {
    // Store preference immediately
    if (typeof window !== 'undefined') {
      localStorage.setItem('i18nextLng', languageCode)
    }

    // Try to change language if i18next is ready
    if (i18n.isInitialized && i18n.changeLanguage) {
      await i18n.changeLanguage(languageCode)
      console.log(`✅ Language changed to ${languageCode}`)
    } else {
      console.warn('i18next not ready, language will be applied on next reload')
      // Optionally reload the page to apply the language change
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          window.location.reload()
        }, 100)
      }
    }
  } catch (error) {
    console.error('Failed to change language:', error)
    throw error
  }
}

/**
 * Initialize i18next on client side only
 */
if (typeof window !== 'undefined') {
  // Auto-initialize when this module is imported
  initializeI18n().catch(error => {
    console.warn('Auto-initialization of i18next failed:', error)
  })
}

export default i18n
