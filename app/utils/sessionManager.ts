// Database-based session management instead of localStorage
import pool from '@/lib/db'
import { v4 as uuidv4 } from 'uuid'

export interface SessionData {
  sessionId: string
  userId: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
  createdAt: Date
  expiresAt: Date
  isActive: boolean
}

// Create a new session in the database
export async function createSession(userId: number, username: string, role: 'admin' | 'waiter', fullName: string): Promise<string> {
  const sessionId = uuidv4()
  const createdAt = new Date()
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  await pool.query(
    `INSERT INTO user_sessions (session_id, user_id, username, role, full_name, created_at, expires_at, is_active)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
    [sessionId, userId, username, role, fullName, createdAt, expiresAt, true]
  )

  return sessionId
}

// Validate and get session from database
export async function getSession(sessionId: string): Promise<SessionData | null> {
  try {
    const result = await pool.query(
      `SELECT session_id, user_id, username, role, full_name, created_at, expires_at, is_active
       FROM user_sessions 
       WHERE session_id = $1 AND is_active = true AND expires_at > NOW()`,
      [sessionId]
    )

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      sessionId: row.session_id,
      userId: row.user_id,
      username: row.username,
      role: row.role,
      fullName: row.full_name,
      createdAt: row.created_at,
      expiresAt: row.expires_at,
      isActive: row.is_active
    }
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

// Invalidate session in database
export async function invalidateSession(sessionId: string): Promise<void> {
  try {
    await pool.query(
      'UPDATE user_sessions SET is_active = false WHERE session_id = $1',
      [sessionId]
    )
  } catch (error) {
    console.error('Error invalidating session:', error)
  }
}

// Clean up expired sessions (run periodically)
export async function cleanupExpiredSessions(): Promise<void> {
  try {
    await pool.query(
      'UPDATE user_sessions SET is_active = false WHERE expires_at < NOW()'
    )
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error)
  }
}

// Extend session expiration
export async function extendSession(sessionId: string): Promise<void> {
  try {
    const newExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // Extend by 24 hours
    await pool.query(
      'UPDATE user_sessions SET expires_at = $1 WHERE session_id = $2 AND is_active = true',
      [newExpiresAt, sessionId]
    )
  } catch (error) {
    console.error('Error extending session:', error)
  }
}

// Get all active sessions for a user (for security monitoring)
export async function getUserActiveSessions(userId: number): Promise<SessionData[]> {
  try {
    const result = await pool.query(
      `SELECT session_id, user_id, username, role, full_name, created_at, expires_at, is_active
       FROM user_sessions 
       WHERE user_id = $1 AND is_active = true AND expires_at > NOW()
       ORDER BY created_at DESC`,
      [userId]
    )

    return result.rows.map(row => ({
      sessionId: row.session_id,
      userId: row.user_id,
      username: row.username,
      role: row.role,
      fullName: row.full_name,
      createdAt: row.created_at,
      expiresAt: row.expires_at,
      isActive: row.is_active
    }))
  } catch (error) {
    console.error('Error getting user active sessions:', error)
    return []
  }
}
