/**
 * Unified Storage System
 * 
 * This module provides a unified interface for all storage operations
 * with automatic fallback and proper security enforcement.
 * 
 * Usage Examples:
 * 
 * // Get storage manager by type
 * const memoryStorage = getStorageManager('memory')
 * const localStorage = getStorageManager('local') 
 * const dbStorage = getStorageManager('database')
 * 
 * // Auto-select appropriate storage
 * const autoStorage = getAutoStorage()
 * 
 * // Use fallback chain (memory → local → database)
 * const result = await getWithFallback('user_preference')
 * 
 * // Set with automatic routing based on key patterns
 * await setWithRules('theme', 'dark') // → localStorage
 * await setWithRules('draft_order_1', orderData) // → memory
 * await setWithRules('user_setting', value) // → database (if authenticated)
 * 
 * Storage Rules:
 * - Auth/session data → HttpOnly cookies only (enforced)
 * - UI preferences → LocalStorage
 * - Temporary data → Memory
 * - Persistent state → Database (if authenticated)
 */

// Core interfaces and types
export type { IStorageManager, StorageType, StorageConfig, StorageResult } from './IStorageManager'

// Storage implementations
export { MemoryStorageManager } from './MemoryStorageManager'
export { LocalStorageManager } from './LocalStorageManager'
export { DatabaseStorageManager } from './DatabaseStorageManager'

// Factory and utilities
export {
  StorageManagerFactory,
  storageFactory,
  getStorageManager,
  getAutoStorage,
  getWithFallback,
  setWithRules,
  configureStorage
} from './StorageManagerFactory'

// Convenience functions for common patterns

/**
 * Store UI preference (localStorage with optional database sync)
 */
export async function setUIPreference(key: string, value: any, syncToDatabase: boolean = false): Promise<boolean> {
  const localStorage = getStorageManager('local')
  const success = await localStorage.setJSON(`ui_${key}`, value)

  // Optionally sync to database for cross-device preferences
  if (syncToDatabase && success) {
    try {
      const result = await setWithRules(`ui_sync_${key}`, JSON.stringify(value))
      if (!result.success) {
        console.warn(`Failed to sync UI preference ${key} to database`)
      }
    } catch (error) {
      console.warn(`Error syncing UI preference ${key} to database:`, error)
    }
  }

  return success
}

/**
 * Get UI preference with fallback and optional database sync
 */
export async function getUIPreference<T>(key: string, defaultValue?: T, checkDatabase: boolean = false): Promise<T | null> {
  // First try localStorage for fast access
  const localStorage = getStorageManager('local')
  const localValue = await localStorage.getJSON<T>(`ui_${key}`)

  if (localValue !== null) {
    return localValue
  }

  // If not found locally and database sync is enabled, check database
  if (checkDatabase) {
    try {
      const result = await getWithFallback(`ui_sync_${key}`)
      if (result.success && result.data) {
        const dbValue = JSON.parse(result.data) as T

        // Cache it locally for next time
        await localStorage.setJSON(`ui_${key}`, dbValue)

        return dbValue
      }
    } catch (error) {
      console.warn(`Error getting synced UI preference ${key} from database:`, error)
    }
  }

  return defaultValue || null
}

/**
 * Store temporary data (automatically uses memory)
 */
export async function setTempData(key: string, value: any): Promise<boolean> {
  const memoryStorage = getStorageManager('memory')
  return await memoryStorage.setJSON(`temp_${key}`, value)
}

/**
 * Get temporary data
 */
export async function getTempData<T>(key: string): Promise<T | null> {
  const memoryStorage = getStorageManager('memory')
  return await memoryStorage.getJSON<T>(`temp_${key}`)
}

/**
 * Store user setting (automatically uses database if authenticated, localStorage otherwise)
 */
export async function setUserSetting(key: string, value: any): Promise<boolean> {
  const result = await setWithRules(`setting_${key}`, JSON.stringify(value))
  return result.success
}

/**
 * Get user setting with fallback
 */
export async function getUserSetting<T>(key: string, defaultValue?: T): Promise<T | null> {
  const result = await getWithFallback(`setting_${key}`)
  if (result.success && result.data) {
    try {
      return JSON.parse(result.data) as T
    } catch {
      return result.data as T
    }
  }
  return defaultValue || null
}

/**
 * Clear all storage (useful for logout or reset)
 */
export async function clearAllStorage(): Promise<void> {
  await storageFactory.clearAll()
}

/**
 * Get storage statistics for debugging
 */
export async function getStorageStats() {
  return await storageFactory.getStorageStats()
}

// Analytics and monitoring
export { storageAnalytics } from './StorageAnalytics'
export type { StorageMetrics, StorageStats } from './StorageAnalytics'

// Advanced insights
export { storageInsights } from './StorageInsights'
export type { HistoricalMetrics, KeyUsageStats, GrowthTrend } from './StorageInsights'

// Security monitoring
export { securityMonitor } from './SecurityMonitor'
export type { SecurityEvent, TamperDetectionConfig } from './SecurityMonitor'

// Stress testing
export { stressTests } from './StressTests'
export type { TestResult, StressTestConfig } from './StressTests'

// Plugin system (temporarily disabled to fix loading issue)
// export { pluginSystem, IndexedDBPlugin } from './PluginSystem'
// export type { StoragePlugin, PluginCapabilities, PluginConfig } from './PluginSystem'

// Comprehensive test suite
export { storageTestSuite } from './StorageTestSuite'
export type { TestSuiteResult } from './StorageTestSuite'

// Development tools
export { storageDevTools } from './StorageDevTools'

// Offline management
export { offlineManager } from './OfflineManager'
export type { QueuedOperation } from './OfflineManager'

// Re-export debounce utilities
export { debounce, debounceAsync, throttle } from '../debounce'

// Import statements for factory functions
import { 
  getStorageManager, 
  getAutoStorage, 
  getWithFallback, 
  setWithRules, 
  storageFactory 
} from './StorageManagerFactory'
