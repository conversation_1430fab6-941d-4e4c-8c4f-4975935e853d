import { IStorageManager, StorageType, StorageConfig, StorageResult } from './IStorageManager'
import { MemoryStorageManager } from './MemoryStorageManager'
import { LocalStorageManager } from './LocalStorageManager'
import { DatabaseStorageManager } from './DatabaseStorageManager'
import { storageAnalytics } from './StorageAnalytics'

/**
 * Factory for creating and managing storage instances
 * Provides fallback logic and unified access to different storage types
 */
export class StorageManagerFactory {
  private static instance: StorageManagerFactory
  private managers: Map<StorageType, IStorageManager> = new Map()
  private config: StorageConfig = {}

  private constructor() {}

  public static getInstance(): StorageManagerFactory {
    if (!StorageManagerFactory.instance) {
      StorageManagerFactory.instance = new StorageManagerFactory()
    }
    return StorageManagerFactory.instance
  }

  /**
   * Configure the factory with global settings
   */
  configure(config: StorageConfig): void {
    this.config = { ...this.config, ...config }
    
    // Update existing managers with new config
    if (this.managers.has('database')) {
      const dbManager = this.managers.get('database') as DatabaseStorageManager
      if (config.authToken !== undefined) {
        dbManager.setAuthToken(config.authToken)
      }
    }
  }

  /**
   * Get a storage manager by type
   */
  getStorageManager(type: StorageType): IStorageManager {
    if (!this.managers.has(type)) {
      this.managers.set(type, this.createManager(type))
    }
    return this.managers.get(type)!
  }

  /**
   * Auto-select storage manager based on runtime environment and availability
   */
  getAutoStorageManager(): IStorageManager {
    // Server-side: use memory storage
    if (typeof window === 'undefined') {
      return this.getStorageManager('memory')
    }

    // Client-side: prefer localStorage, fallback to memory
    try {
      const localStorage = this.getStorageManager('local')
      // Test if localStorage is available
      if (localStorage) {
        return localStorage
      }
    } catch (error) {
      console.warn('LocalStorage not available, falling back to memory storage')
    }

    return this.getStorageManager('memory')
  }

  /**
   * Get storage manager with fallback chain for reading
   * Tries memory → localStorage → database in order
   */
  async getWithFallback(key: string): Promise<StorageResult<string>> {
    const managers: { type: StorageType; manager: IStorageManager }[] = [
      { type: 'memory', manager: this.getStorageManager('memory') },
      { type: 'local', manager: this.getStorageManager('local') },
      { type: 'database', manager: this.getStorageManager('database') }
    ]

    let lastAttemptedType: StorageType | null = null

    for (const { type, manager } of managers) {
      try {
        if (await manager.isAvailable()) {
          const value = await manager.get(key)
          if (value !== null) {
            // Record fallback if we didn't find it in the first storage
            if (lastAttemptedType) {
              storageAnalytics.recordFallback(lastAttemptedType, type, key)
            }

            // Sync upward if found in lower-tier storage
            await this.syncUpward(key, value, type)

            return {
              success: true,
              data: value,
              source: type
            }
          }
        }
        lastAttemptedType = type
      } catch (error) {
        console.warn(`Storage ${type} failed for key ${key}:`, error)
        lastAttemptedType = type
        continue
      }
    }

    return {
      success: false,
      error: 'Key not found in any storage'
    }
  }

  /**
   * Set value with automatic storage selection
   * Routes to appropriate storage based on data type and rules
   */
  async setWithRules(key: string, value: string, rules?: {
    preferredStorage?: StorageType
    syncToAll?: boolean
  }): Promise<StorageResult<boolean>> {
    const storageType = rules?.preferredStorage || this.determineStorageType(key)
    const manager = this.getStorageManager(storageType)

    try {
      const success = await manager.set(key, value)
      
      if (success && rules?.syncToAll) {
        // Sync to all available storages
        await this.syncToAll(key, value)
      }

      return {
        success,
        data: success,
        source: storageType
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: storageType
      }
    }
  }

  /**
   * Determine appropriate storage type based on key patterns
   */
  private determineStorageType(key: string): StorageType {
    // Auth/session data should not be stored (handled by cookies)
    if (key.includes('auth') || key.includes('token') || key.includes('session')) {
      throw new Error('Auth data should use HttpOnly cookies, not storage')
    }

    // UI preferences → localStorage
    if (key.includes('theme') || key.includes('preference') || key.includes('setting')) {
      return 'local'
    }

    // Temporary data → memory
    if (key.includes('draft') || key.includes('temp') || key.includes('cache')) {
      return 'memory'
    }

    // Persistent state → database (if authenticated)
    if (this.config.authToken) {
      return 'database'
    }

    // Default to localStorage
    return 'local'
  }

  /**
   * Sync value upward to higher-tier storages
   */
  private async syncUpward(key: string, value: string, sourceType: StorageType): Promise<void> {
    const hierarchy: StorageType[] = ['database', 'local', 'memory']
    const sourceIndex = hierarchy.indexOf(sourceType)

    // Sync to all higher-tier storages
    for (let i = 0; i < sourceIndex; i++) {
      const targetType = hierarchy[i]
      try {
        const manager = this.getStorageManager(targetType)
        if (await manager.isAvailable()) {
          await manager.set(key, value)
        }
      } catch (error) {
        console.warn(`Failed to sync ${key} to ${targetType}:`, error)
      }
    }
  }

  /**
   * Sync value to all available storages
   */
  private async syncToAll(key: string, value: string): Promise<void> {
    const types: StorageType[] = ['memory', 'local', 'database']
    
    for (const type of types) {
      try {
        const manager = this.getStorageManager(type)
        if (await manager.isAvailable()) {
          await manager.set(key, value)
        }
      } catch (error) {
        console.warn(`Failed to sync ${key} to ${type}:`, error)
      }
    }
  }

  /**
   * Create a storage manager instance
   */
  private createManager(type: StorageType): IStorageManager {
    switch (type) {
      case 'memory':
        return MemoryStorageManager.getInstance(this.config.cacheSize)
      
      case 'local':
        return LocalStorageManager.getInstance()
      
      case 'database':
        return DatabaseStorageManager.getInstance(this.config)
      
      default:
        throw new Error(`Unknown storage type: ${type}`)
    }
  }

  /**
   * Clear all storages
   */
  async clearAll(): Promise<void> {
    const types: StorageType[] = ['memory', 'local', 'database']
    
    for (const type of types) {
      try {
        const manager = this.getStorageManager(type)
        if (await manager.isAvailable()) {
          await manager.clear()
        }
      } catch (error) {
        console.warn(`Failed to clear ${type} storage:`, error)
      }
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    const analyticsStats = storageAnalytics.getStats()
    const stats: Record<string, any> = {}
    const types: StorageType[] = ['memory', 'local', 'database']

    for (const type of types) {
      try {
        const manager = this.getStorageManager(type)
        if (await manager.isAvailable()) {
          const keys = await manager.keys()

          // Get size information
          let size = 0
          if (type === 'memory' && manager instanceof MemoryStorageManager) {
            size = manager.getMemoryUsage()
          } else if (type === 'local' && manager instanceof LocalStorageManager) {
            const info = await manager.getStorageInfo()
            size = info?.totalSize || 0
          }

          stats[type] = {
            available: true,
            keys: keys.length,
            items: keys,
            size: size,
            // Include analytics metrics
            ...analyticsStats[type]
          }
        } else {
          stats[type] = {
            available: false,
            ...analyticsStats[type]
          }
        }
      } catch (error) {
        stats[type] = {
          available: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          ...analyticsStats[type]
        }
      }
    }

    // Add overall analytics
    stats.analytics = analyticsStats.overall

    return stats
  }
}

// Export singleton instance and convenience functions
export const storageFactory = StorageManagerFactory.getInstance()

// Convenience functions for common operations
export const getStorageManager = (type: StorageType) => storageFactory.getStorageManager(type)
export const getAutoStorage = () => storageFactory.getAutoStorageManager()
export const getWithFallback = (key: string) => storageFactory.getWithFallback(key)
export const setWithRules = (key: string, value: string, rules?: any) => storageFactory.setWithRules(key, value, rules)
export const configureStorage = (config: StorageConfig) => storageFactory.configure(config)
