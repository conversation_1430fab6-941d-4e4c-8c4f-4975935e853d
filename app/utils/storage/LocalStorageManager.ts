import { IStorageManager } from './IStorageManager'
import { storageAnalytics } from './StorageAnalytics'

/**
 * LocalStorage-based storage manager
 * Data persists across browser sessions
 * Ideal for UI preferences, settings, and non-sensitive data
 */
export class LocalStorageManager implements IStorageManager {
  private static instance: LocalStorageManager
  private isClient: boolean

  // Storage quotas
  private readonly MAX_ITEMS = 500
  private readonly MAX_SIZE_KB = 5 * 1024 // 5MB in KB

  constructor() {
    this.isClient = typeof window !== 'undefined'
  }

  public static getInstance(): LocalStorageManager {
    if (!LocalStorageManager.instance) {
      LocalStorageManager.instance = new LocalStorageManager()
    }
    return LocalStorageManager.instance
  }

  async get(key: string): Promise<string | null> {
    const startTime = performance.now()
    let result: string | null = null
    let success = false

    try {
      if (!this.isClient) return null
      result = localStorage.getItem(key)
      success = result !== null
      return result
    } catch (error) {
      console.error(`LocalStorage get error for key ${key}:`, error)
      storageAnalytics.recordError('localStorage', 'get', error as Error)
      return null
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordRead('localStorage', key, success, duration)
    }
  }

  async set(key: string, value: string): Promise<boolean> {
    const startTime = performance.now()
    let success = false

    try {
      if (!this.isClient) return false

      // Check quotas before setting
      const currentSize = this.getCurrentSizeKB()
      const newItemSize = (key.length + value.length) * 2 / 1024 // UTF-16 estimate in KB
      const currentItems = localStorage.length

      // Check item count quota
      if (currentItems >= this.MAX_ITEMS && !localStorage.getItem(key)) {
        console.warn(`LocalStorage item quota exceeded (${this.MAX_ITEMS} items). Attempting cleanup...`)
        await this.cleanup()
      }

      // Check size quota
      if (currentSize + newItemSize > this.MAX_SIZE_KB) {
        console.warn(`LocalStorage size quota exceeded (${this.MAX_SIZE_KB}KB). Attempting cleanup...`)
        await this.cleanup()
      }

      localStorage.setItem(key, value)
      success = true
      return true
    } catch (error) {
      console.error(`LocalStorage set error for key ${key}:`, error)

      // Handle quota exceeded error
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        console.warn('LocalStorage quota exceeded, attempting cleanup...')
        await this.cleanup()

        // Try again after cleanup
        try {
          localStorage.setItem(key, value)
          success = true
          return true
        } catch (retryError) {
          console.error('LocalStorage set failed even after cleanup:', retryError)
          storageAnalytics.recordError('localStorage', 'set', retryError as Error)
          return false
        }
      }

      storageAnalytics.recordError('localStorage', 'set', error as Error)
      return false
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordWrite('localStorage', key, success, duration)
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      if (!this.isClient) return false
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`LocalStorage delete error for key ${key}:`, error)
      return false
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      if (!value) return null
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`LocalStorage getJSON error for key ${key}:`, error)
      // Remove corrupted data
      await this.delete(key)
      return null
    }
  }

  async setJSON<T>(key: string, value: T): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString)
    } catch (error) {
      console.error(`LocalStorage setJSON error for key ${key}:`, error)
      return false
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      if (!this.isClient) return false
      return localStorage.getItem(key) !== null
    } catch (error) {
      console.error(`LocalStorage has error for key ${key}:`, error)
      return false
    }
  }

  async clear(): Promise<boolean> {
    try {
      if (!this.isClient) return false
      localStorage.clear()
      return true
    } catch (error) {
      console.error('LocalStorage clear error:', error)
      return false
    }
  }

  async keys(): Promise<string[]> {
    try {
      if (!this.isClient) return []
      return Object.keys(localStorage)
    } catch (error) {
      console.error('LocalStorage keys error:', error)
      return []
    }
  }

  getType(): string {
    return 'local'
  }

  async isAvailable(): Promise<boolean> {
    try {
      if (!this.isClient) return false
      
      const testKey = '__localStorage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Get current localStorage size in KB
   */
  private getCurrentSizeKB(): number {
    if (!this.isClient) return 0

    let totalSize = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key) || ''
        totalSize += (key.length + value.length) * 2 // UTF-16 estimate
      }
    }
    return totalSize / 1024 // Convert to KB
  }

  /**
   * Clean up old or corrupted entries
   */
  private async cleanup(): Promise<void> {
    try {
      if (!this.isClient) return

      const keys = Object.keys(localStorage)
      const corruptedKeys: string[] = []

      // Find corrupted JSON entries
      for (const key of keys) {
        try {
          const value = localStorage.getItem(key)
          if (value && (value.startsWith('{') || value.startsWith('['))) {
            JSON.parse(value) // This will throw if corrupted
          }
        } catch (error) {
          corruptedKeys.push(key)
        }
      }

      // Remove corrupted entries
      for (const key of corruptedKeys) {
        localStorage.removeItem(key)
        console.log(`Removed corrupted localStorage entry: ${key}`)
      }

      // Remove temporary entries first
      const tempKeys = keys.filter(key => key.startsWith('temp_'))
      tempKeys.forEach(key => localStorage.removeItem(key))
      if (tempKeys.length > 0) {
        console.log(`Removed ${tempKeys.length} temporary localStorage entries`)
      }

      // Check quotas after cleanup
      const currentItems = localStorage.length
      const currentSize = this.getCurrentSizeKB()

      // If still over quota, remove oldest non-essential entries
      if (currentItems > this.MAX_ITEMS || currentSize > this.MAX_SIZE_KB) {
        const nonEssentialKeys = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && !key.startsWith('auth_') && !key.startsWith('user_') && !key.startsWith('ui_')) {
            nonEssentialKeys.push(key)
          }
        }

        const toRemove = Math.min(
          Math.floor(nonEssentialKeys.length * 0.25),
          Math.max(currentItems - this.MAX_ITEMS + 10, 0)
        )

        for (let i = 0; i < toRemove && i < nonEssentialKeys.length; i++) {
          localStorage.removeItem(nonEssentialKeys[i])
        }

        if (toRemove > 0) {
          console.log(`Removed ${toRemove} additional localStorage entries due to quota limits`)
        }
      }
    } catch (error) {
      console.error('LocalStorage cleanup error:', error)
    }
  }

  /**
   * Get storage usage information
   */
  async getStorageInfo() {
    try {
      if (!this.isClient) return null

      let totalSize = 0
      const keys = Object.keys(localStorage)
      
      for (const key of keys) {
        const value = localStorage.getItem(key) || ''
        totalSize += key.length + value.length
      }

      return {
        keyCount: keys.length,
        totalSize: totalSize * 2, // Rough estimate in bytes (UTF-16)
        keys: keys
      }
    } catch (error) {
      console.error('LocalStorage getStorageInfo error:', error)
      return null
    }
  }
}
