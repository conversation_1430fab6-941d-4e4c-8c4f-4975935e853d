/**
 * Storage Plugin System
 * Extensible architecture for custom storage implementations
 */

import { IStorageManager, StorageConfig } from './IStorageManager'

export interface StoragePlugin {
  name: string
  version: string
  description: string
  priority: number // Higher priority = used first in fallback chain
  
  // Plugin lifecycle
  initialize(config?: any): Promise<boolean>
  destroy(): Promise<void>
  
  // Storage operations
  get(key: string): Promise<string | null>
  set(key: string, value: string): Promise<boolean>
  delete(key: string): Promise<boolean>
  keys(): Promise<string[]>
  clear(): Promise<boolean>
  
  // Plugin capabilities
  isAvailable(): Promise<boolean>
  getCapabilities(): PluginCapabilities
  
  // Hooks for integration
  onBeforeRead?(key: string): Promise<void>
  onAfterRead?(key: string, value: string | null): Promise<void>
  onBeforeWrite?(key: string, value: string): Promise<void>
  onAfterWrite?(key: string, value: string, success: boolean): Promise<void>
  onError?(operation: string, error: Error): Promise<void>
}

export interface PluginCapabilities {
  persistent: boolean // Data survives page refresh
  encrypted: boolean // Data is encrypted at rest
  compressed: boolean // Data is compressed
  synced: boolean // Data syncs across devices
  offline: boolean // Works offline
  maxSize?: number // Maximum storage size in bytes
  maxKeys?: number // Maximum number of keys
}

export interface PluginConfig {
  enabled: boolean
  priority: number
  config?: any
}

class PluginSystem {
  private static instance: PluginSystem
  private plugins: Map<string, StoragePlugin> = new Map()
  private pluginConfigs: Map<string, PluginConfig> = new Map()
  private isInitialized: boolean = false

  public static getInstance(): PluginSystem {
    if (!PluginSystem.instance) {
      PluginSystem.instance = new PluginSystem()
    }
    return PluginSystem.instance
  }

  /**
   * Register a storage plugin
   */
  async registerPlugin(plugin: StoragePlugin, config: PluginConfig = { enabled: true, priority: plugin.priority }): Promise<boolean> {
    try {
      // Validate plugin
      if (!this.validatePlugin(plugin)) {
        throw new Error(`Invalid plugin: ${plugin.name}`)
      }

      // Check for conflicts
      if (this.plugins.has(plugin.name)) {
        console.warn(`Plugin ${plugin.name} is already registered. Replacing...`)
      }

      // Initialize plugin
      const initialized = await plugin.initialize(config.config)
      if (!initialized) {
        throw new Error(`Failed to initialize plugin: ${plugin.name}`)
      }

      // Register plugin
      this.plugins.set(plugin.name, plugin)
      this.pluginConfigs.set(plugin.name, config)

      console.log(`✅ Registered storage plugin: ${plugin.name} v${plugin.version}`)
      return true
    } catch (error) {
      console.error(`❌ Failed to register plugin ${plugin.name}:`, error)
      return false
    }
  }

  /**
   * Unregister a storage plugin
   */
  async unregisterPlugin(name: string): Promise<boolean> {
    try {
      const plugin = this.plugins.get(name)
      if (!plugin) {
        console.warn(`Plugin ${name} is not registered`)
        return false
      }

      // Destroy plugin
      await plugin.destroy()

      // Remove from registry
      this.plugins.delete(name)
      this.pluginConfigs.delete(name)

      console.log(`🗑️ Unregistered storage plugin: ${name}`)
      return true
    } catch (error) {
      console.error(`❌ Failed to unregister plugin ${name}:`, error)
      return false
    }
  }

  /**
   * Get available plugins sorted by priority
   */
  getAvailablePlugins(): StoragePlugin[] {
    const enabledPlugins = Array.from(this.plugins.entries())
      .filter(([name, plugin]) => {
        const config = this.pluginConfigs.get(name)
        return config?.enabled !== false
      })
      .map(([name, plugin]) => plugin)
      .sort((a, b) => b.priority - a.priority)

    return enabledPlugins
  }

  /**
   * Get plugin by name
   */
  getPlugin(name: string): StoragePlugin | null {
    return this.plugins.get(name) || null
  }

  /**
   * Create a storage manager that uses plugins
   */
  createPluginStorageManager(pluginNames: string[]): IStorageManager {
    return new PluginStorageManager(this, pluginNames)
  }

  /**
   * Validate plugin implementation
   */
  private validatePlugin(plugin: StoragePlugin): boolean {
    const requiredMethods = ['get', 'set', 'delete', 'keys', 'clear', 'isAvailable', 'getCapabilities', 'initialize', 'destroy']
    
    for (const method of requiredMethods) {
      if (typeof plugin[method] !== 'function') {
        console.error(`Plugin ${plugin.name} missing required method: ${method}`)
        return false
      }
    }

    if (!plugin.name || !plugin.version) {
      console.error(`Plugin missing name or version`)
      return false
    }

    return true
  }

  /**
   * Get registered plugins
   */
  getRegisteredPlugins(): Array<{
    name: string
    version: string
    enabled: boolean
    capabilities: PluginCapabilities
  }> {
    return Array.from(this.plugins.entries()).map(([name, plugin]) => {
      const config = this.pluginConfigs.get(name)
      return {
        name: plugin.name,
        version: plugin.version,
        enabled: config?.enabled !== false,
        capabilities: plugin.getCapabilities()
      }
    })
  }

  /**
   * Get plugin statistics
   */
  getPluginStats(): {
    total: number
    enabled: number
    available: number
    plugins: Array<{
      name: string
      version: string
      enabled: boolean
      available: boolean
      capabilities: PluginCapabilities
    }>
  } {
    const pluginStats = Array.from(this.plugins.entries()).map(([name, plugin]) => {
      const config = this.pluginConfigs.get(name)
      return {
        name: plugin.name,
        version: plugin.version,
        enabled: config?.enabled !== false,
        available: false, // Will be checked async
        capabilities: plugin.getCapabilities()
      }
    })

    return {
      total: this.plugins.size,
      enabled: pluginStats.filter(p => p.enabled).length,
      available: 0, // Will be updated async
      plugins: pluginStats
    }
  }
}

/**
 * Storage manager that uses plugins
 */
class PluginStorageManager implements IStorageManager {
  private pluginSystem: PluginSystem
  private pluginNames: string[]

  constructor(pluginSystem: PluginSystem, pluginNames: string[]) {
    this.pluginSystem = pluginSystem
    this.pluginNames = pluginNames
  }

  async get(key: string): Promise<string | null> {
    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        if (await plugin.isAvailable()) {
          await plugin.onBeforeRead?.(key)
          const value = await plugin.get(key)
          await plugin.onAfterRead?.(key, value)
          
          if (value !== null) {
            return value
          }
        }
      } catch (error) {
        await plugin.onError?.('get', error as Error)
        console.warn(`Plugin ${pluginName} failed to get key ${key}:`, error)
      }
    }

    return null
  }

  async set(key: string, value: string): Promise<boolean> {
    let success = false

    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        if (await plugin.isAvailable()) {
          await plugin.onBeforeWrite?.(key, value)
          const result = await plugin.set(key, value)
          await plugin.onAfterWrite?.(key, value, result)
          
          if (result) {
            success = true
            // Continue to write to other plugins for redundancy
          }
        }
      } catch (error) {
        await plugin.onError?.('set', error as Error)
        console.warn(`Plugin ${pluginName} failed to set key ${key}:`, error)
      }
    }

    return success
  }

  async delete(key: string): Promise<boolean> {
    let success = false

    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        if (await plugin.isAvailable()) {
          const result = await plugin.delete(key)
          if (result) {
            success = true
          }
        }
      } catch (error) {
        await plugin.onError?.('delete', error as Error)
        console.warn(`Plugin ${pluginName} failed to delete key ${key}:`, error)
      }
    }

    return success
  }

  async keys(): Promise<string[]> {
    const allKeys = new Set<string>()

    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        if (await plugin.isAvailable()) {
          const keys = await plugin.keys()
          keys.forEach(key => allKeys.add(key))
        }
      } catch (error) {
        await plugin.onError?.('keys', error as Error)
        console.warn(`Plugin ${pluginName} failed to get keys:`, error)
      }
    }

    return Array.from(allKeys)
  }

  async clear(): Promise<boolean> {
    let success = false

    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        if (await plugin.isAvailable()) {
          const result = await plugin.clear()
          if (result) {
            success = true
          }
        }
      } catch (error) {
        await plugin.onError?.('clear', error as Error)
        console.warn(`Plugin ${pluginName} failed to clear:`, error)
      }
    }

    return success
  }

  async isAvailable(): Promise<boolean> {
    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (plugin && await plugin.isAvailable()) {
        return true
      }
    }
    return false
  }

  async getStorageInfo(): Promise<any> {
    const info: any = {
      plugins: []
    }

    for (const pluginName of this.pluginNames) {
      const plugin = this.pluginSystem.getPlugin(pluginName)
      if (!plugin) continue

      try {
        const available = await plugin.isAvailable()
        const capabilities = plugin.getCapabilities()
        
        info.plugins.push({
          name: plugin.name,
          version: plugin.version,
          available,
          capabilities
        })
      } catch (error) {
        console.warn(`Failed to get info for plugin ${pluginName}:`, error)
      }
    }

    return info
  }

  // Additional methods for compatibility
  async setJSON(key: string, value: any): Promise<boolean> {
    return this.set(key, JSON.stringify(value))
  }

  async getJSON<T>(key: string): Promise<T | null> {
    const value = await this.get(key)
    if (value === null) return null

    try {
      return JSON.parse(value) as T
    } catch {
      return null
    }
  }

  async has(key: string): Promise<boolean> {
    const value = await this.get(key)
    return value !== null
  }

  getType(): string {
    return 'plugin'
  }
}

// Lazy initialization to avoid timing issues
let _pluginSystemInstance: PluginSystem | null = null

export function getPluginSystem(): PluginSystem {
  if (!_pluginSystemInstance) {
    try {
      _pluginSystemInstance = PluginSystem.getInstance()
    } catch (error) {
      console.error('Failed to initialize plugin system:', error)
      throw error
    }
  }
  return _pluginSystemInstance
}

// Export as constant for backward compatibility
export const pluginSystem = (() => {
  try {
    return getPluginSystem()
  } catch (error) {
    console.error('Failed to export plugin system:', error)
    // Return a minimal object to prevent "not a function" errors
    return {
      registerPlugin: () => Promise.resolve(false),
      unregisterPlugin: () => Promise.resolve(false),
      getAvailablePlugins: () => [],
      getPlugin: () => null,
      createPluginStorageManager: () => null,
      getRegisteredPlugins: () => [],
      getPluginStats: () => ({ total: 0, enabled: 0, available: 0, plugins: [] })
    } as any
  }
})()

// Auto-register IndexedDB plugin if available (moved to end of file)

/**
 * Example IndexedDB Plugin
 */
export class IndexedDBPlugin implements StoragePlugin {
  name = 'indexeddb'
  version = '1.0.0'
  description = 'IndexedDB storage plugin with compression and encryption support'
  priority = 50

  private db: IDBDatabase | null = null
  private dbName = 'StoragePluginDB'
  private storeName = 'keyvalue'

  async initialize(config?: any): Promise<boolean> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return false
    }

    try {
      this.db = await this.openDatabase()
      return true
    } catch (error) {
      console.error('Failed to initialize IndexedDB plugin:', error)
      return false
    }
  }

  async destroy(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }

  private openDatabase(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 1)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName)
        }
      }
    })
  }

  async get(key: string): Promise<string | null> {
    if (!this.db) return null

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(key)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result || null)
    })
  }

  async set(key: string, value: string): Promise<boolean> {
    if (!this.db) return false

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.put(value, key)

      request.onerror = () => resolve(false)
      request.onsuccess = () => resolve(true)
    })
  }

  async delete(key: string): Promise<boolean> {
    if (!this.db) return false

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.delete(key)

      request.onerror = () => resolve(false)
      request.onsuccess = () => resolve(true)
    })
  }

  async keys(): Promise<string[]> {
    if (!this.db) return []

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.getAllKeys()

      request.onerror = () => resolve([])
      request.onsuccess = () => resolve(request.result as string[])
    })
  }

  async clear(): Promise<boolean> {
    if (!this.db) return false

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.clear()

      request.onerror = () => resolve(false)
      request.onsuccess = () => resolve(true)
    })
  }

  async isAvailable(): Promise<boolean> {
    return this.db !== null && typeof window !== 'undefined' && !!window.indexedDB
  }

  getCapabilities(): PluginCapabilities {
    return {
      persistent: true,
      encrypted: false,
      compressed: false,
      synced: false,
      offline: true,
      maxSize: 50 * 1024 * 1024, // ~50MB typical limit
      maxKeys: undefined
    }
  }
}

// Auto-register IndexedDB plugin if available
if (typeof window !== 'undefined') {
  setTimeout(async () => {
    try {
      const indexedDBPlugin = new IndexedDBPlugin()
      await getPluginSystem().registerPlugin(indexedDBPlugin)
    } catch (error) {
      console.warn('Failed to auto-register IndexedDB plugin:', error)
    }
  }, 100)
}

// Add to window for debugging (only in development)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  try {
    // Use the function to ensure proper initialization
    const system = getPluginSystem()
    if (system && typeof system === 'object' && typeof system.registerPlugin === 'function') {
      (window as any).pluginSystem = system
      (window as any).getPluginSystem = getPluginSystem
      (window as any).IndexedDBPlugin = IndexedDBPlugin
    }
  } catch (error) {
    console.warn('Failed to add plugin system to window:', error)
  }
}
