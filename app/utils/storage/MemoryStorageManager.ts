import { IStorageManager } from './IStorageManager'
import { storageAnalytics } from './StorageAnalytics'
import { storageInsights } from './StorageInsights'

/**
 * Memory-based storage manager using Map
 * Data is lost when the page is refreshed or closed
 * Ideal for temporary data like draft orders, form state, etc.
 */
export class MemoryStorageManager implements IStorageManager {
  private static instance: MemoryStorageManager
  private storage: Map<string, string> = new Map()
  private maxSize: number

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize
  }

  public static getInstance(maxSize?: number): MemoryStorageManager {
    if (!MemoryStorageManager.instance) {
      MemoryStorageManager.instance = new MemoryStorageManager(maxSize)
    }
    return MemoryStorageManager.instance
  }

  async get(key: string): Promise<string | null> {
    const startTime = performance.now()
    let result: string | null = null
    let success = false

    try {
      result = this.storage.get(key) || null
      success = result !== null
      return result
    } catch (error) {
      console.error(`MemoryStorage get error for key ${key}:`, error)
      storageAnalytics.recordError('memory', 'get', error as Error)
      return null
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordRead('memory', key, success, duration)
      storageInsights.trackKeyUsage(key, 'read', 'memory', result?.length || 0)
    }
  }

  async set(key: string, value: string): Promise<boolean> {
    const startTime = performance.now()
    let success = false

    try {
      // Check size limit
      if (this.storage.size >= this.maxSize && !this.storage.has(key)) {
        console.warn(`Memory storage quota exceeded (${this.maxSize} items). Removing oldest entry.`)
        // Remove oldest entry (first in Map)
        const firstKey = this.storage.keys().next().value
        if (firstKey) {
          this.storage.delete(firstKey)
        }
      }

      this.storage.set(key, value)
      success = true
      return true
    } catch (error) {
      console.error(`MemoryStorage set error for key ${key}:`, error)
      storageAnalytics.recordError('memory', 'set', error as Error)
      return false
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordWrite('memory', key, success, duration)
      if (success) {
        storageInsights.trackKeyUsage(key, 'write', 'memory', value.length)
      }
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      return this.storage.delete(key)
    } catch (error) {
      console.error(`MemoryStorage delete error for key ${key}:`, error)
      return false
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      if (!value) return null
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`MemoryStorage getJSON error for key ${key}:`, error)
      return null
    }
  }

  async setJSON<T>(key: string, value: T): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString)
    } catch (error) {
      console.error(`MemoryStorage setJSON error for key ${key}:`, error)
      return false
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      return this.storage.has(key)
    } catch (error) {
      console.error(`MemoryStorage has error for key ${key}:`, error)
      return false
    }
  }

  async clear(): Promise<boolean> {
    try {
      this.storage.clear()
      return true
    } catch (error) {
      console.error('MemoryStorage clear error:', error)
      return false
    }
  }

  async keys(): Promise<string[]> {
    try {
      return Array.from(this.storage.keys())
    } catch (error) {
      console.error('MemoryStorage keys error:', error)
      return []
    }
  }

  getType(): string {
    return 'memory'
  }

  async isAvailable(): Promise<boolean> {
    return true // Memory storage is always available
  }

  /**
   * Get current storage size
   */
  getSize(): number {
    return this.storage.size
  }

  /**
   * Get memory usage estimate in bytes
   */
  getMemoryUsage(): number {
    let totalSize = 0
    for (const [key, value] of this.storage) {
      totalSize += key.length + value.length
    }
    return totalSize * 2 // Rough estimate (UTF-16)
  }

  /**
   * Get storage statistics
   */
  getStats() {
    return {
      size: this.getSize(),
      maxSize: this.maxSize,
      memoryUsage: this.getMemoryUsage(),
      keys: Array.from(this.storage.keys())
    }
  }
}
