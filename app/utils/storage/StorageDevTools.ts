/**
 * Storage Development Tools
 * Utilities for inspecting and debugging storage state
 */

import { storageFactory } from './StorageManagerFactory'
import { storageAnalytics } from './StorageAnalytics'

export class StorageDevTools {
  private static instance: StorageDevTools

  public static getInstance(): StorageDevTools {
    if (!StorageDevTools.instance) {
      StorageDevTools.instance = new StorageDevTools()
    }
    return StorageDevTools.instance
  }

  /**
   * Log current storage state to console
   */
  async logStorageState(): Promise<void> {
    console.group('🔍 Storage State Inspector')
    
    try {
      const stats = await storageFactory.getStorageStats()
      
      console.log('📊 Overall Analytics:', stats.analytics)
      
      for (const [storageType, data] of Object.entries(stats)) {
        if (storageType === 'analytics') continue
        
        console.group(`💾 ${storageType.toUpperCase()} Storage`)
        console.log('Available:', data.available)
        console.log('Keys:', data.keys || 0)
        console.log('Size:', this.formatBytes(data.size || 0))
        
        if (data.items && data.items.length > 0) {
          console.log('Items:', data.items.slice(0, 10)) // Show first 10 items
          if (data.items.length > 10) {
            console.log(`... and ${data.items.length - 10} more`)
          }
        }
        
        // Analytics for this storage type
        if (data.reads !== undefined) {
          console.log('Performance:', {
            reads: data.reads,
            writes: data.writes,
            hits: data.hits,
            misses: data.misses,
            fallbacks: data.fallbackCount,
            errors: data.errorCount
          })
        }
        
        console.groupEnd()
      }
    } catch (error) {
      console.error('Failed to get storage state:', error)
    }
    
    console.groupEnd()
  }

  /**
   * Log storage contents for a specific storage type
   */
  async logStorageContents(storageType: 'memory' | 'local' | 'database'): Promise<void> {
    console.group(`📋 ${storageType.toUpperCase()} Storage Contents`)
    
    try {
      const manager = storageFactory.getStorageManager(storageType)
      const keys = await manager.keys()
      
      console.log(`Total keys: ${keys.length}`)
      
      for (const key of keys) {
        try {
          const value = await manager.get(key)
          console.log(`${key}:`, this.truncateValue(value))
        } catch (error) {
          console.error(`Error reading ${key}:`, error)
        }
      }
    } catch (error) {
      console.error(`Failed to read ${storageType} contents:`, error)
    }
    
    console.groupEnd()
  }

  /**
   * Search for keys matching a pattern
   */
  async searchKeys(pattern: string | RegExp): Promise<void> {
    console.group(`🔍 Search Results for: ${pattern}`)
    
    const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern
    const storageTypes = ['memory', 'local', 'database'] as const
    
    for (const storageType of storageTypes) {
      try {
        const manager = storageFactory.getStorageManager(storageType)
        const keys = await manager.keys()
        const matchingKeys = keys.filter(key => regex.test(key))
        
        if (matchingKeys.length > 0) {
          console.group(`📦 ${storageType.toUpperCase()} (${matchingKeys.length} matches)`)
          
          for (const key of matchingKeys) {
            try {
              const value = await manager.get(key)
              console.log(`${key}:`, this.truncateValue(value))
            } catch (error) {
              console.error(`Error reading ${key}:`, error)
            }
          }
          
          console.groupEnd()
        }
      } catch (error) {
        console.error(`Error searching ${storageType}:`, error)
      }
    }
    
    console.groupEnd()
  }

  /**
   * Monitor storage operations in real-time
   */
  startMonitoring(): () => void {
    console.log('🔍 Starting storage monitoring...')
    
    // Enable analytics if not already enabled
    storageAnalytics.setEnabled(true)
    
    // Log stats every 30 seconds
    const interval = setInterval(() => {
      storageAnalytics.logStats()
    }, 30000)
    
    // Return cleanup function
    return () => {
      console.log('🛑 Stopping storage monitoring...')
      clearInterval(interval)
    }
  }

  /**
   * Clear all storage (with confirmation)
   */
  async clearAllStorage(): Promise<void> {
    if (typeof window !== 'undefined') {
      const confirmed = window.confirm(
        'Are you sure you want to clear ALL storage? This cannot be undone.'
      )
      if (!confirmed) return
    }
    
    console.log('🧹 Clearing all storage...')
    await storageFactory.clearAll()
    console.log('✅ All storage cleared')
  }

  /**
   * Export storage data for debugging
   */
  async exportStorageData(): Promise<object> {
    const exportData: any = {
      timestamp: new Date().toISOString(),
      analytics: storageAnalytics.getStats(),
      storage: {}
    }
    
    const storageTypes = ['memory', 'local', 'database'] as const
    
    for (const storageType of storageTypes) {
      try {
        const manager = storageFactory.getStorageManager(storageType)
        const keys = await manager.keys()
        const data: Record<string, any> = {}
        
        for (const key of keys) {
          try {
            const value = await manager.get(key)
            data[key] = value
          } catch (error) {
            data[key] = `<ERROR: ${error.message}>`
          }
        }
        
        exportData.storage[storageType] = data
      } catch (error) {
        exportData.storage[storageType] = `<ERROR: ${error.message}>`
      }
    }
    
    console.log('📤 Storage data exported:', exportData)
    return exportData
  }

  /**
   * Test storage performance
   */
  async testPerformance(iterations: number = 100): Promise<void> {
    console.group('⚡ Storage Performance Test')
    
    const testKey = '__perf_test__'
    const testValue = JSON.stringify({ test: 'data', timestamp: Date.now() })
    const storageTypes = ['memory', 'local', 'database'] as const
    
    for (const storageType of storageTypes) {
      try {
        const manager = storageFactory.getStorageManager(storageType)
        
        // Write test
        const writeStart = performance.now()
        for (let i = 0; i < iterations; i++) {
          await manager.set(`${testKey}_${i}`, testValue)
        }
        const writeTime = performance.now() - writeStart
        
        // Read test
        const readStart = performance.now()
        for (let i = 0; i < iterations; i++) {
          await manager.get(`${testKey}_${i}`)
        }
        const readTime = performance.now() - readStart
        
        // Cleanup
        for (let i = 0; i < iterations; i++) {
          await manager.delete(`${testKey}_${i}`)
        }
        
        console.log(`${storageType.toUpperCase()}:`, {
          writeTime: `${writeTime.toFixed(2)}ms`,
          readTime: `${readTime.toFixed(2)}ms`,
          avgWrite: `${(writeTime / iterations).toFixed(2)}ms`,
          avgRead: `${(readTime / iterations).toFixed(2)}ms`
        })
        
      } catch (error) {
        console.error(`${storageType} performance test failed:`, error)
      }
    }
    
    console.groupEnd()
  }

  /**
   * Helper to format bytes
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * Helper to truncate long values for display
   */
  private truncateValue(value: string | null, maxLength: number = 100): string {
    if (!value) return '<null>'
    if (value.length <= maxLength) return value
    return value.substring(0, maxLength) + '...'
  }
}

// Export singleton instance
export const storageDevTools = StorageDevTools.getInstance()

// Add to window for browser console access
if (typeof window !== 'undefined') {
  (window as any).storageDevTools = storageDevTools
  console.log('🔧 Storage DevTools available at window.storageDevTools')
}
