import { IStorageManager, StorageConfig } from './IStorageManager'
import { debounceAsync, DebounceCancelledError } from '../debounce'
import { storageAnalytics } from './StorageAnalytics'
import { offlineManager } from './OfflineManager'

/**
 * Database-based storage manager with in-memory cache
 * Data persists in PostgreSQL database
 * Ideal for user preferences, persistent state, and cross-device sync
 */
export class DatabaseStorageManager implements IStorageManager {
  private static instance: DatabaseStorageManager
  private authToken: string | null = null
  private cache: Map<string, string> = new Map()
  private debouncedSet: (key: string, value: string) => Promise<boolean>
  private debouncedDelete: (key: string) => Promise<boolean>

  constructor(config: StorageConfig = {}) {
    this.authToken = config.authToken || null
    const debounceDelay = config.debounceDelay || 500

    // Debounce database operations to prevent excessive API calls
    this.debouncedSet = debounceAsync(this.setToDatabase.bind(this), debounceDelay)
    this.debouncedDelete = debounceAsync(this.deleteFromDatabase.bind(this), debounceDelay)
  }

  public static getInstance(config?: StorageConfig): DatabaseStorageManager {
    if (!DatabaseStorageManager.instance) {
      DatabaseStorageManager.instance = new DatabaseStorageManager(config)
    }
    return DatabaseStorageManager.instance
  }

  /**
   * Set authentication token for API calls
   */
  setAuthToken(token: string | null): void {
    this.authToken = token
  }

  async get(key: string): Promise<string | null> {
    const startTime = performance.now()
    let result: string | null = null
    let success = false

    try {
      // First check cache
      if (this.cache.has(key)) {
        result = this.cache.get(key) || null
        success = result !== null
        return result
      }

      // If not in cache, fetch from database
      result = await this.getFromDatabase(key)
      if (result !== null) {
        this.cache.set(key, result)
      }

      success = result !== null
      return result
    } catch (error) {
      console.error(`DatabaseStorage get error for key ${key}:`, error)
      storageAnalytics.recordError('database', 'get', error as Error)
      return null
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordRead('database', key, success, duration)
    }
  }

  async set(key: string, value: string): Promise<boolean> {
    const startTime = performance.now()
    let success = false

    try {
      // Update cache immediately for fast access
      this.cache.set(key, value)

      // Check if online before attempting database write
      if (offlineManager.isCurrentlyOnline()) {
        // Debounce database write
        try {
          success = await this.debouncedSet(key, value)
          return success
        } catch (error) {
          // Silently ignore cancelled debounce calls
          if (error instanceof DebounceCancelledError) {
            success = true // Consider it successful since the cache is updated
            return true
          }
          throw error // Re-throw other errors
        }
      } else {
        // Queue for later when back online
        offlineManager.queueOperation({
          type: 'set',
          key,
          value,
          maxRetries: 3
        })
        console.log(`📴 Offline: queued database write for key ${key}`)
        success = true // Consider it successful since we've cached it and queued the write
        return true
      }
    } catch (error) {
      console.error(`DatabaseStorage set error for key ${key}:`, error)

      // If it's a network error and we're offline, queue it
      if (!offlineManager.isCurrentlyOnline()) {
        offlineManager.queueOperation({
          type: 'set',
          key,
          value,
          maxRetries: 3
        })
        console.log(`📴 Network error while offline: queued database write for key ${key}`)
        success = true // Consider it successful since we've cached it and queued the write
        return true
      }

      storageAnalytics.recordError('database', 'set', error as Error)
      return false
    } finally {
      const duration = performance.now() - startTime
      storageAnalytics.recordWrite('database', key, success, duration)
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      // Remove from cache immediately
      this.cache.delete(key)

      // Check if online before attempting database delete
      if (offlineManager.isCurrentlyOnline()) {
        // Debounce database delete
        try {
          return await this.debouncedDelete(key)
        } catch (error) {
          // Silently ignore cancelled debounce calls
          if (error instanceof DebounceCancelledError) {
            return true // Consider it successful since the cache is updated
          }
          throw error // Re-throw other errors
        }
      } else {
        // Queue for later when back online
        offlineManager.queueOperation({
          type: 'delete',
          key,
          maxRetries: 3
        })
        console.log(`📴 Offline: queued database delete for key ${key}`)
        return true // Return true since we've removed from cache and queued the delete
      }
    } catch (error) {
      console.error(`DatabaseStorage delete error for key ${key}:`, error)

      // If it's a network error and we're offline, queue it
      if (!offlineManager.isCurrentlyOnline()) {
        offlineManager.queueOperation({
          type: 'delete',
          key,
          maxRetries: 3
        })
        console.log(`📴 Network error while offline: queued database delete for key ${key}`)
        return true // Return true since we've removed from cache and queued the delete
      }

      return false
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      if (!value) return null
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`DatabaseStorage getJSON error for key ${key}:`, error)
      return null
    }
  }

  async setJSON<T>(key: string, value: T): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString)
    } catch (error) {
      console.error(`DatabaseStorage setJSON error for key ${key}:`, error)
      return false
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      const value = await this.get(key)
      return value !== null
    } catch (error) {
      console.error(`DatabaseStorage has error for key ${key}:`, error)
      return false
    }
  }

  async clear(): Promise<boolean> {
    try {
      // Clear cache
      this.cache.clear()
      
      // Clear database
      return await this.clearDatabase()
    } catch (error) {
      console.error('DatabaseStorage clear error:', error)
      return false
    }
  }

  async keys(): Promise<string[]> {
    try {
      return await this.getKeysFromDatabase()
    } catch (error) {
      console.error('DatabaseStorage keys error:', error)
      return []
    }
  }

  getType(): string {
    return 'database'
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Test database connectivity using cookies
      const response = await fetch('/api/storage/test', {
        credentials: 'include' // Use cookies for authentication
      })

      return response.ok
    } catch (error) {
      return false
    }
  }

  /**
   * Initialize storage by loading data from database
   */
  async initialize(): Promise<void> {
    try {
      if (!this.authToken) return
      
      const data = await this.getAllFromDatabase()
      this.cache.clear()
      
      for (const [key, value] of Object.entries(data)) {
        this.cache.set(key, value)
      }
    } catch (error) {
      console.error('DatabaseStorage initialization error:', error)
    }
  }

  /**
   * Clear cache only (keep database data)
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  // Private methods for database operations

  private async getFromDatabase(key: string): Promise<string | null> {
    try {
      const response = await fetch(`/api/storage?key=${encodeURIComponent(key)}`, {
        credentials: 'include' // Use cookies for authentication
      })

      if (!response.ok) return null

      const data = await response.json()
      return data.value || null
    } catch (error) {
      console.error('Database get error:', error)
      return null
    }
  }

  private async setToDatabase(key: string, value: string): Promise<boolean> {
    try {
      const response = await fetch('/api/storage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({ key, value })
      })

      return response.ok
    } catch (error) {
      console.error('Database set error:', error)
      return false
    }
  }

  private async deleteFromDatabase(key: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/storage?key=${encodeURIComponent(key)}`, {
        method: 'DELETE',
        credentials: 'include' // Use cookies for authentication
      })

      return response.ok
    } catch (error) {
      console.error('Database delete error:', error)
      return false
    }
  }

  private async getAllFromDatabase(): Promise<Record<string, string>> {
    try {
      const response = await fetch('/api/storage/all', {
        credentials: 'include' // Use cookies for authentication
      })

      if (!response.ok) return {}

      return await response.json()
    } catch (error) {
      console.error('Database getAll error:', error)
      return {}
    }
  }

  private async clearDatabase(): Promise<boolean> {
    try {
      const response = await fetch('/api/storage/clear', {
        method: 'DELETE',
        credentials: 'include' // Use cookies for authentication
      })

      return response.ok
    } catch (error) {
      console.error('Database clear error:', error)
      return false
    }
  }

  private async getKeysFromDatabase(): Promise<string[]> {
    try {
      const response = await fetch('/api/storage/keys', {
        credentials: 'include' // Use cookies for authentication
      })

      if (!response.ok) return []

      const data = await response.json()
      return data.keys || []
    } catch (error) {
      console.error('Database getKeys error:', error)
      return []
    }
  }
}
