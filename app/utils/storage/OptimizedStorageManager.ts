import { IStorageManager } from './IStorageManager'
import { ultraCache } from '../../../lib/cache'

/**
 * Optimized Storage Manager for Client-Side Performance
 * Implements: UI State → Memory Cache → Batched LocalStorage → Database
 * 
 * This eliminates "Slow storage write detected" by:
 * 1. Using memory cache for immediate access
 * 2. Batching localStorage writes
 * 3. Debouncing database operations
 * 4. Only saving on critical events
 */
export class OptimizedStorageManager implements IStorageManager {
  private static instance: OptimizedStorageManager
  private pendingWrites: Map<string, any> = new Map()
  private writeTimeout: NodeJS.Timeout | null = null
  private stats = {
    memoryHits: 0,
    localStorageHits: 0,
    writes: 0,
    batchedWrites: 0
  }

  private constructor() {}

  public static getInstance(): OptimizedStorageManager {
    if (!OptimizedStorageManager.instance) {
      OptimizedStorageManager.instance = new OptimizedStorageManager()
    }
    return OptimizedStorageManager.instance
  }

  /**
   * Check if storage is available
   */
  async isAvailable(): Promise<boolean> {
    return typeof window !== 'undefined' && 'localStorage' in window
  }

  /**
   * Get value with optimized cache flow
   * 1. Check memory cache first (fastest)
   * 2. Check localStorage if not in memory
   * 3. Cache in memory for next time
   */
  async get(key: string): Promise<string | null> {
    try {
      // Step 1: Check memory cache first
      const memoryValue = ultraCache.get<string>(key)
      if (memoryValue !== null) {
        this.stats.memoryHits++
        return memoryValue
      }

      // Step 2: Check localStorage
      if (typeof window !== 'undefined') {
        const localValue = localStorage.getItem(key)
        if (localValue !== null) {
          // Cache in memory for next time (5 minute TTL)
          ultraCache.set(key, localValue, 5 * 60 * 1000)
          this.stats.localStorageHits++
          return localValue
        }
      }

      return null
    } catch (error) {
      console.error(`OptimizedStorage GET error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set value with batched writes to prevent excessive storage operations
   */
  async set(key: string, value: string): Promise<boolean> {
    try {
      // Immediately cache in memory
      ultraCache.set(key, value, 5 * 60 * 1000)

      // Add to pending writes for batching
      this.pendingWrites.set(key, value)

      // Schedule batched write (debounced)
      this.scheduleBatchedWrite()

      this.stats.writes++
      return true
    } catch (error) {
      console.error(`OptimizedStorage SET error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete value from all caches
   */
  async delete(key: string): Promise<boolean> {
    try {
      // Remove from memory cache
      ultraCache.delete(key)

      // Remove from pending writes
      this.pendingWrites.delete(key)

      // Remove from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(key)
      }

      return true
    } catch (error) {
      console.error(`OptimizedStorage DELETE error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Get JSON object with type safety
   */
  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      if (value === null) return null
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`OptimizedStorage getJSON error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set JSON object
   */
  async setJSON<T>(key: string, value: T): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString)
    } catch (error) {
      console.error(`OptimizedStorage setJSON error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Bulk get operations
   */
  async getBulk<T>(keys: string[]): Promise<Record<string, T | null>> {
    const results: Record<string, T | null> = {}
    
    for (const key of keys) {
      results[key] = await this.getJSON<T>(key)
    }
    
    return results
  }

  /**
   * Bulk set operations (batched)
   */
  async setBulk<T>(data: Record<string, T>): Promise<boolean> {
    try {
      for (const [key, value] of Object.entries(data)) {
        await this.setJSON(key, value)
      }
      return true
    } catch (error) {
      console.error('OptimizedStorage setBulk error:', error)
      return false
    }
  }

  /**
   * Force immediate write of all pending data
   */
  async flush(): Promise<void> {
    if (this.writeTimeout) {
      clearTimeout(this.writeTimeout)
      this.writeTimeout = null
    }
    await this.executeBatchedWrite()
  }

  /**
   * Schedule batched write with debouncing
   */
  private scheduleBatchedWrite(): void {
    if (this.writeTimeout) {
      clearTimeout(this.writeTimeout)
    }

    // Batch writes with 2-second delay to prevent excessive localStorage operations
    this.writeTimeout = setTimeout(() => {
      this.executeBatchedWrite()
    }, 2000)
  }

  /**
   * Execute batched write to localStorage
   */
  private async executeBatchedWrite(): Promise<void> {
    if (this.pendingWrites.size === 0) return

    try {
      if (typeof window !== 'undefined') {
        // Write all pending values in a single batch
        for (const [key, value] of this.pendingWrites.entries()) {
          localStorage.setItem(key, value)
        }
        
        console.log(`✅ Batched write completed: ${this.pendingWrites.size} items`)
        this.stats.batchedWrites++
      }

      // Clear pending writes
      this.pendingWrites.clear()
      this.writeTimeout = null
    } catch (error) {
      console.error('Batched write error:', error)
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): any {
    const totalHits = this.stats.memoryHits + this.stats.localStorageHits
    const memoryHitRate = totalHits > 0 ? (this.stats.memoryHits / totalHits) * 100 : 0

    return {
      ...this.stats,
      totalHits,
      memoryHitRate: Math.round(memoryHitRate * 100) / 100,
      pendingWrites: this.pendingWrites.size,
      performance: {
        cacheEfficiency: memoryHitRate > 80 ? 'Excellent' : memoryHitRate > 60 ? 'Good' : 'Needs Improvement',
        batchingRatio: this.stats.writes > 0 ? Math.round((this.stats.batchedWrites / this.stats.writes) * 100) : 0
      }
    }
  }

  /**
   * Clear all caches
   */
  async clear(): Promise<boolean> {
    try {
      // Clear memory cache
      ultraCache.clear()

      // Clear pending writes
      this.pendingWrites.clear()

      // Clear localStorage (only our keys)
      if (typeof window !== 'undefined') {
        const keysToRemove: string[] = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith('billard_')) {
            keysToRemove.push(key)
          }
        }
        
        keysToRemove.forEach(key => localStorage.removeItem(key))
      }

      return true
    } catch (error) {
      console.error('OptimizedStorage clear error:', error)
      return false
    }
  }

  /**
   * Invalidate specific keys from memory cache
   */
  invalidate(keys: string | string[]): void {
    const keyArray = Array.isArray(keys) ? keys : [keys]
    keyArray.forEach(key => ultraCache.delete(key))
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUp(keys: string[]): Promise<void> {
    for (const key of keys) {
      await this.get(key) // This will load into memory cache
    }
    console.log(`🔥 Cache warmed up for ${keys.length} keys`)
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    memory: boolean
    localStorage: boolean
    overall: boolean
  }> {
    try {
      const testKey = `health_check_${Date.now()}`
      const testValue = 'test'
      
      // Test write and read
      const writeSuccess = await this.set(testKey, testValue)
      const readValue = await this.get(testKey)
      const readSuccess = readValue === testValue
      
      // Clean up
      await this.delete(testKey)
      
      return {
        memory: true, // Memory cache is always available
        localStorage: typeof window !== 'undefined' && 'localStorage' in window,
        overall: writeSuccess && readSuccess
      }
    } catch (error) {
      console.error('Health check failed:', error)
      return {
        memory: false,
        localStorage: false,
        overall: false
      }
    }
  }

  /**
   * Cleanup on page unload
   */
  cleanup(): void {
    if (this.writeTimeout) {
      clearTimeout(this.writeTimeout)
    }
    // Force write any pending data
    this.executeBatchedWrite()
  }
}

// Export singleton instance
export const optimizedStorage = OptimizedStorageManager.getInstance()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    optimizedStorage.cleanup()
  })
}

export default optimizedStorage
