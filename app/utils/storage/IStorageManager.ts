/**
 * Unified async storage interface for all storage operations
 * Provides consistent API across memory, localStorage, and database storage
 */

export interface IStorageManager {
  /**
   * Get a value by key
   * @param key - Storage key
   * @returns Promise resolving to the value or null if not found
   */
  get(key: string): Promise<string | null>

  /**
   * Set a value by key
   * @param key - Storage key
   * @param value - Value to store
   * @returns Promise resolving to true if successful, false otherwise
   */
  set(key: string, value: string): Promise<boolean>

  /**
   * Delete a value by key
   * @param key - Storage key
   * @returns Promise resolving to true if successful, false otherwise
   */
  delete(key: string): Promise<boolean>

  /**
   * Get a JSON value by key
   * @param key - Storage key
   * @returns Promise resolving to the parsed object or null if not found
   */
  getJSON<T>(key: string): Promise<T | null>

  /**
   * Set a JSON value by key
   * @param key - Storage key
   * @param value - Object to store
   * @returns Promise resolving to true if successful, false otherwise
   */
  setJSON<T>(key: string, value: T): Promise<boolean>

  /**
   * Check if a key exists
   * @param key - Storage key
   * @returns Promise resolving to true if key exists, false otherwise
   */
  has(key: string): Promise<boolean>

  /**
   * Clear all storage
   * @returns Promise resolving to true if successful, false otherwise
   */
  clear(): Promise<boolean>

  /**
   * Get all keys
   * @returns Promise resolving to array of all keys
   */
  keys(): Promise<string[]>

  /**
   * Get storage type identifier
   * @returns String identifying the storage type
   */
  getType(): string

  /**
   * Check if storage is available
   * @returns Promise resolving to true if storage is available, false otherwise
   */
  isAvailable(): Promise<boolean>
}

/**
 * Storage manager types for factory
 */
export type StorageType = 'memory' | 'local' | 'database'

/**
 * Configuration for storage managers
 */
export interface StorageConfig {
  authToken?: string | null
  debounceDelay?: number
  cacheSize?: number
}

/**
 * Storage operation result
 */
export interface StorageResult<T = any> {
  success: boolean
  data?: T
  error?: string
  source?: StorageType
}
