/**
 * Offline Manager
 * Handles offline mode with queued writes and automatic sync when back online
 */

import { storageAnalytics } from './StorageAnalytics'

export interface QueuedOperation {
  id: string
  type: 'set' | 'delete' | 'setJSON'
  key: string
  value?: string
  timestamp: number
  retryCount: number
  maxRetries: number
}

export class OfflineManager {
  private static instance: OfflineManager
  private isOnline: boolean = navigator.onLine
  private queue: QueuedOperation[] = []
  private isProcessing: boolean = false
  private listeners: Set<(isOnline: boolean) => void> = new Set()
  private readonly QUEUE_STORAGE_KEY = '__offline_queue__'
  private readonly MAX_QUEUE_SIZE = 1000
  private readonly RETRY_DELAY = 5000 // 5 seconds

  public static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager()
    }
    return OfflineManager.instance
  }

  private constructor() {
    this.setupEventListeners()
    this.loadQueueFromStorage()
  }

  /**
   * Setup online/offline event listeners
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return

    window.addEventListener('online', () => {
      console.log('🌐 Back online - processing queued operations')
      this.isOnline = true
      this.notifyListeners(true)
      this.processQueue()
    })

    window.addEventListener('offline', () => {
      console.log('📴 Gone offline - queueing operations')
      this.isOnline = false
      this.notifyListeners(false)
    })

    // Also check periodically in case events are missed
    setInterval(() => {
      const currentOnlineStatus = navigator.onLine
      if (currentOnlineStatus !== this.isOnline) {
        this.isOnline = currentOnlineStatus
        this.notifyListeners(currentOnlineStatus)
        if (currentOnlineStatus) {
          this.processQueue()
        }
      }
    }, 10000) // Check every 10 seconds
  }

  /**
   * Add listener for online/offline status changes
   */
  addStatusListener(listener: (isOnline: boolean) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * Notify all listeners of status change
   */
  private notifyListeners(isOnline: boolean): void {
    this.listeners.forEach(listener => {
      try {
        listener(isOnline)
      } catch (error) {
        console.error('Error in offline status listener:', error)
      }
    })
  }

  /**
   * Check if currently online
   */
  isCurrentlyOnline(): boolean {
    return this.isOnline
  }

  /**
   * Queue an operation for later execution
   */
  queueOperation(operation: Omit<QueuedOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    if (this.queue.length >= this.MAX_QUEUE_SIZE) {
      console.warn('Offline queue is full, removing oldest operation')
      this.queue.shift()
    }

    const queuedOp: QueuedOperation = {
      ...operation,
      id: this.generateId(),
      timestamp: Date.now(),
      retryCount: 0
    }

    this.queue.push(queuedOp)
    this.saveQueueToStorage()

    console.log(`📝 Queued ${operation.type} operation for key: ${operation.key}`)
  }

  /**
   * Process the queue when back online
   */
  private async processQueue(): Promise<void> {
    if (!this.isOnline || this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true
    console.log(`🔄 Processing ${this.queue.length} queued operations`)

    const operations = [...this.queue]
    this.queue = []

    for (const operation of operations) {
      try {
        await this.executeOperation(operation)
        console.log(`✅ Successfully executed queued operation: ${operation.type} ${operation.key}`)
      } catch (error) {
        console.error(`❌ Failed to execute queued operation: ${operation.type} ${operation.key}`, error)
        
        // Retry logic
        if (operation.retryCount < operation.maxRetries) {
          operation.retryCount++
          this.queue.push(operation)
          console.log(`🔄 Retrying operation ${operation.id} (attempt ${operation.retryCount}/${operation.maxRetries})`)
        } else {
          console.error(`💀 Operation ${operation.id} failed after ${operation.maxRetries} retries`)
          storageAnalytics.recordError('offline', 'queue_execution', error as Error)
        }
      }
    }

    this.saveQueueToStorage()
    this.isProcessing = false

    // If there are still items in queue (retries), schedule another processing
    if (this.queue.length > 0) {
      setTimeout(() => this.processQueue(), this.RETRY_DELAY)
    }
  }

  /**
   * Execute a queued operation
   */
  private async executeOperation(operation: QueuedOperation): Promise<void> {
    // This would need to be integrated with the actual storage managers
    // For now, we'll simulate the operation
    const response = await fetch('/api/storage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        operation: operation.type,
        key: operation.key,
        value: operation.value
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }

  /**
   * Save queue to localStorage for persistence
   */
  private saveQueueToStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(this.QUEUE_STORAGE_KEY, JSON.stringify(this.queue))
      }
    } catch (error) {
      console.error('Failed to save offline queue to storage:', error)
    }
  }

  /**
   * Load queue from localStorage
   */
  private loadQueueFromStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem(this.QUEUE_STORAGE_KEY)
        if (stored) {
          this.queue = JSON.parse(stored)
          console.log(`📂 Loaded ${this.queue.length} operations from offline queue`)
          
          // Process queue if we're online
          if (this.isOnline) {
            setTimeout(() => this.processQueue(), 1000)
          }
        }
      }
    } catch (error) {
      console.error('Failed to load offline queue from storage:', error)
      this.queue = []
    }
  }

  /**
   * Clear the queue
   */
  clearQueue(): void {
    this.queue = []
    this.saveQueueToStorage()
    console.log('🗑️ Offline queue cleared')
  }

  /**
   * Get queue status
   */
  getQueueStatus(): {
    isOnline: boolean
    queueLength: number
    isProcessing: boolean
    oldestOperation?: QueuedOperation
  } {
    return {
      isOnline: this.isOnline,
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      oldestOperation: this.queue[0]
    }
  }

  /**
   * Generate unique ID for operations
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Force sync (for testing or manual trigger)
   */
  async forceSync(): Promise<void> {
    if (this.queue.length === 0) {
      console.log('📭 No operations to sync')
      return
    }

    console.log('🔄 Force syncing queued operations...')
    await this.processQueue()
  }

  /**
   * Get queue contents for debugging
   */
  getQueueContents(): QueuedOperation[] {
    return [...this.queue]
  }
}

export const offlineManager = OfflineManager.getInstance()

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).offlineManager = offlineManager
}
