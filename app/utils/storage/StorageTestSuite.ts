/**
 * Comprehensive Storage Test Suite
 * Advanced testing for all storage features including i18n, analytics, security, and performance
 */

import { storageFactory } from './StorageManagerFactory'
import { storageAnalytics } from './StorageAnalytics'
import { storageInsights } from './StorageInsights'
import { securityMonitor } from './SecurityMonitor'
import { stressTests } from './StressTests'

export interface TestSuiteResult {
  testName: string
  category: 'i18n' | 'analytics' | 'security' | 'performance' | 'integration'
  passed: boolean
  duration: number
  details: string
  score: number // 0-100
}

class StorageTestSuite {
  private static instance: StorageTestSuite
  private results: TestSuiteResult[] = []

  public static getInstance(): StorageTestSuite {
    if (!StorageTestSuite.instance) {
      StorageTestSuite.instance = new StorageTestSuite()
    }
    return StorageTestSuite.instance
  }

  /**
   * Run comprehensive test suite
   */
  async runFullTestSuite(): Promise<TestSuiteResult[]> {
    console.log('🧪 Starting comprehensive storage test suite...')
    this.results = []

    try {
      // Test 1: I18n Storage Integration
      await this.testI18nStorageIntegration()

      // Test 2: Analytics Data Persistence
      await this.testAnalyticsDataPersistence()

      // Test 3: Security Event Handling
      await this.testSecurityEventHandling()

      // Test 4: Cross-Storage Fallback
      await this.testCrossStorageFallback()

      // Test 5: Performance Under Load
      await this.testPerformanceUnderLoad()

      // Test 6: Data Integrity Across Sessions
      await this.testDataIntegrityAcrossSessions()

      // Test 7: Offline-Online Sync
      await this.testOfflineOnlineSync()

      // Test 8: Plugin System
      await this.testPluginSystem()

      // Test 9: Historical Metrics Accuracy
      await this.testHistoricalMetricsAccuracy()

      // Test 10: Security Encryption
      await this.testSecurityEncryption()

      console.log('✅ Comprehensive test suite completed')
      return this.results
    } catch (error) {
      console.error('❌ Test suite failed:', error)
      throw error
    }
  }

  /**
   * Test i18n storage integration
   */
  private async testI18nStorageIntegration(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Test language preference storage
      await storageFactory.setUIPreference('language', 'sq', true)
      const storedLang = await storageFactory.getUIPreference('language', 'en', true)
      
      // Test translation data caching
      const translationData = { 'common.save': 'Ruaj', 'common.cancel': 'Anulo' }
      await storageFactory.set('i18n_cache_sq', JSON.stringify(translationData))
      const cachedData = await storageFactory.get('i18n_cache_sq')
      
      passed = storedLang === 'sq' && cachedData === JSON.stringify(translationData)
      details = `Language preference: ${storedLang}, Translation cache: ${cachedData ? 'stored' : 'failed'}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'I18n Storage Integration',
      category: 'i18n',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test analytics data persistence
   */
  private async testAnalyticsDataPersistence(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Generate some analytics data
      await storageFactory.set('test_analytics_1', 'data1')
      await storageFactory.get('test_analytics_1')
      await storageFactory.set('test_analytics_2', 'data2')
      
      // Wait for analytics to process
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const stats = storageAnalytics.getStats()
      const insights = storageInsights.getInsightsSummary()
      
      passed = stats.overall.totalOperations > 0 && insights.topKeys.length > 0
      details = `Operations: ${stats.overall.totalOperations}, Top keys: ${insights.topKeys.length}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Analytics Data Persistence',
      category: 'analytics',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test security event handling
   */
  private async testSecurityEventHandling(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Clear previous events
      securityMonitor.clearEvents()
      
      // Trigger security events
      securityMonitor.recordValue('test_security_key', 'original_value')
      
      // Simulate tampering
      const isValid = securityMonitor.verifyIntegrity('test_security_key', 'tampered_value')
      
      // Check if events were recorded
      const events = securityMonitor.getEvents()
      const summary = securityMonitor.getSecuritySummary()
      
      passed = !isValid && events.length > 0 && summary.totalEvents > 0
      details = `Tamper detected: ${!isValid}, Events recorded: ${events.length}, Health score: ${summary.healthScore}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Security Event Handling',
      category: 'security',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test cross-storage fallback
   */
  private async testCrossStorageFallback(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Test fallback chain: memory -> localStorage -> database
      const testKey = 'fallback_test_key'
      const testValue = 'fallback_test_value'
      
      // Store in memory
      const memoryManager = storageFactory.getStorageManager('memory')
      await memoryManager.set(testKey, testValue)
      
      // Try to get with fallback
      const result1 = await storageFactory.getWithFallback(testKey)
      
      // Clear memory and test localStorage fallback
      await memoryManager.delete(testKey)
      const localManager = storageFactory.getStorageManager('local')
      await localManager.set(testKey, testValue)
      
      const result2 = await storageFactory.getWithFallback(testKey)
      
      passed = result1 === testValue && result2 === testValue
      details = `Memory fallback: ${result1 === testValue}, LocalStorage fallback: ${result2 === testValue}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Cross-Storage Fallback',
      category: 'integration',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test performance under load
   */
  private async testPerformanceUnderLoad(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      const operations = 100
      const promises = []
      
      // Concurrent read/write operations
      for (let i = 0; i < operations; i++) {
        promises.push(storageFactory.set(`perf_test_${i}`, `data_${i}`))
      }
      
      await Promise.all(promises)
      
      const readPromises = []
      for (let i = 0; i < operations; i++) {
        readPromises.push(storageFactory.get(`perf_test_${i}`))
      }
      
      const results = await Promise.all(readPromises)
      const successCount = results.filter(r => r !== null).length
      
      const duration = performance.now() - startTime
      const avgTimePerOp = duration / (operations * 2) // read + write
      
      passed = successCount >= operations * 0.95 && avgTimePerOp < 10 // 95% success, <10ms per op
      details = `${successCount}/${operations} operations succeeded, ${avgTimePerOp.toFixed(2)}ms avg per operation`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Performance Under Load',
      category: 'performance',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : Math.max(0, 100 - (performance.now() - startTime) / 10)
    })
  }

  /**
   * Test data integrity across sessions
   */
  private async testDataIntegrityAcrossSessions(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      const testData = {
        user: 'test_user',
        preferences: { theme: 'dark', language: 'sq' },
        timestamp: Date.now()
      }
      
      // Store in different storage types
      await storageFactory.setUserSetting('session_test', testData, true)
      await storageFactory.setUIPreference('session_ui_test', testData.preferences, true)
      
      // Retrieve and verify
      const userData = await storageFactory.getUserSetting('session_test', null, true)
      const uiData = await storageFactory.getUIPreference('session_ui_test', null, true)
      
      passed = userData?.user === testData.user && uiData?.theme === testData.preferences.theme
      details = `User data integrity: ${userData?.user === testData.user}, UI data integrity: ${uiData?.theme === testData.preferences.theme}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Data Integrity Across Sessions',
      category: 'integration',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test offline-online sync
   */
  private async testOfflineOnlineSync(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Simulate offline mode
      const originalOnline = navigator.onLine
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true })
      
      // Perform operations while offline
      await storageFactory.set('offline_test_1', 'offline_data_1')
      await storageFactory.set('offline_test_2', 'offline_data_2')
      
      // Restore online status
      Object.defineProperty(navigator, 'onLine', { value: originalOnline, writable: true })
      
      // Check if operations were queued and can be synced
      const { offlineManager } = await import('./OfflineManager')
      const queueStatus = offlineManager.getQueueStatus()
      
      passed = queueStatus.queueLength > 0
      details = `Offline operations queued: ${queueStatus.queueLength}, Oldest operation: ${queueStatus.oldestOperation ? 'exists' : 'none'}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Offline-Online Sync',
      category: 'integration',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test plugin system
   */
  private async testPluginSystem(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      const { pluginSystem } = await import('./PluginSystem')
      
      // Test plugin registration and usage
      const plugins = pluginSystem.getRegisteredPlugins()
      const hasIndexedDB = plugins.some(p => p.name === 'IndexedDB')
      
      passed = plugins.length > 0 && hasIndexedDB
      details = `Registered plugins: ${plugins.length}, IndexedDB plugin: ${hasIndexedDB ? 'available' : 'missing'}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Plugin System',
      category: 'integration',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test historical metrics accuracy
   */
  private async testHistoricalMetricsAccuracy(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      // Generate some operations for metrics
      for (let i = 0; i < 10; i++) {
        await storageFactory.set(`metrics_test_${i}`, `data_${i}`)
        await storageFactory.get(`metrics_test_${i}`)
      }
      
      // Get historical data
      const historical = storageInsights.getHistoricalMetrics(1) // Last day
      const trends = storageInsights.getGrowthTrends('daily')
      
      passed = historical.length > 0 && trends.data.length > 0
      details = `Historical entries: ${historical.length}, Trend data points: ${trends.data.length}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Historical Metrics Accuracy',
      category: 'analytics',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Test security encryption
   */
  private async testSecurityEncryption(): Promise<void> {
    const startTime = performance.now()
    let passed = false
    let details = ''

    try {
      const sensitiveData = 'secret_password_123'
      const sensitiveKey = 'user_password'
      
      // Test encryption
      const encrypted = securityMonitor.encryptValue(sensitiveData, sensitiveKey)
      const decrypted = securityMonitor.decryptValue(encrypted, sensitiveKey)
      
      // Test that sensitive data is actually encrypted
      const isEncrypted = encrypted !== sensitiveData
      const isDecryptedCorrectly = decrypted === sensitiveData
      
      passed = isEncrypted && isDecryptedCorrectly
      details = `Data encrypted: ${isEncrypted}, Decryption successful: ${isDecryptedCorrectly}`
    } catch (error) {
      details = `Test failed: ${error.message}`
    }

    this.results.push({
      testName: 'Security Encryption',
      category: 'security',
      passed,
      duration: performance.now() - startTime,
      details,
      score: passed ? 100 : 0
    })
  }

  /**
   * Get test results summary
   */
  getTestSummary(): {
    totalTests: number
    passedTests: number
    failedTests: number
    averageScore: number
    categoryScores: Record<string, number>
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor'
  } {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    const averageScore = this.results.reduce((sum, r) => sum + r.score, 0) / totalTests

    const categoryScores: Record<string, number> = {}
    const categories = ['i18n', 'analytics', 'security', 'performance', 'integration']
    
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category)
      if (categoryResults.length > 0) {
        categoryScores[category] = categoryResults.reduce((sum, r) => sum + r.score, 0) / categoryResults.length
      }
    })

    let overallHealth: 'excellent' | 'good' | 'fair' | 'poor'
    if (averageScore >= 90) overallHealth = 'excellent'
    else if (averageScore >= 75) overallHealth = 'good'
    else if (averageScore >= 60) overallHealth = 'fair'
    else overallHealth = 'poor'

    return {
      totalTests,
      passedTests,
      failedTests,
      averageScore,
      categoryScores,
      overallHealth
    }
  }

  /**
   * Export test results
   */
  exportResults(): object {
    return {
      results: this.results,
      summary: this.getTestSummary(),
      timestamp: new Date().toISOString()
    }
  }
}

export const storageTestSuite = StorageTestSuite.getInstance()

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).storageTestSuite = storageTestSuite
}
