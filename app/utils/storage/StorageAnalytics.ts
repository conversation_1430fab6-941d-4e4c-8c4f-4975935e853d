/**
 * Storage Analytics and Monitoring
 * Tracks read/write performance, hit/miss ratios, and usage patterns
 */

export interface StorageMetrics {
  reads: number
  writes: number
  hits: number
  misses: number
  totalReadTime: number
  totalWriteTime: number
  fallbackCount: number
  errorCount: number
  lastAccess: number
}

export interface StorageStats {
  memory: StorageMetrics & { size: number; keys: number; items: string[] }
  localStorage: StorageMetrics & { size: number; keys: number; items: string[] }
  database: StorageMetrics & { size: number; keys: number; items: string[] }
  overall: {
    totalOperations: number
    averageReadTime: number
    averageWriteTime: number
    hitRate: number
    fallbackRate: number
    errorRate: number
  }
}

class StorageAnalytics {
  private static instance: StorageAnalytics
  private metrics: Map<string, StorageMetrics> = new Map()
  private isEnabled: boolean = true

  public static getInstance(): StorageAnalytics {
    if (!StorageAnalytics.instance) {
      StorageAnalytics.instance = new StorageAnalytics()
    }
    return StorageAnalytics.instance
  }

  private constructor() {
    // Initialize metrics for each storage type
    this.metrics.set('memory', this.createEmptyMetrics())
    this.metrics.set('localStorage', this.createEmptyMetrics())
    this.metrics.set('database', this.createEmptyMetrics())
  }

  private createEmptyMetrics(): StorageMetrics {
    return {
      reads: 0,
      writes: 0,
      hits: 0,
      misses: 0,
      totalReadTime: 0,
      totalWriteTime: 0,
      fallbackCount: 0,
      errorCount: 0,
      lastAccess: Date.now()
    }
  }

  /**
   * Enable or disable analytics collection
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Record a read operation
   */
  recordRead(storageType: string, key: string, hit: boolean, duration: number): void {
    if (!this.isEnabled) return

    const metrics = this.metrics.get(storageType)
    if (!metrics) return

    metrics.reads++
    metrics.totalReadTime += duration
    metrics.lastAccess = Date.now()

    if (hit) {
      metrics.hits++
    } else {
      metrics.misses++
    }

    // Log slow reads
    if (duration > 100) {
      console.warn(`Slow storage read detected: ${storageType}/${key} took ${duration}ms`)
    }
  }

  /**
   * Record a write operation
   */
  recordWrite(storageType: string, key: string, success: boolean, duration: number): void {
    if (!this.isEnabled) return

    const metrics = this.metrics.get(storageType)
    if (!metrics) return

    metrics.writes++
    metrics.totalWriteTime += duration
    metrics.lastAccess = Date.now()

    if (!success) {
      metrics.errorCount++
    }

    // Log slow writes
    if (duration > 200) {
      console.warn(`Slow storage write detected: ${storageType}/${key} took ${duration}ms`)
    }
  }

  /**
   * Record a fallback operation
   */
  recordFallback(fromStorage: string, toStorage: string, key: string): void {
    if (!this.isEnabled) return

    const metrics = this.metrics.get(fromStorage)
    if (metrics) {
      metrics.fallbackCount++
    }

    console.log(`Storage fallback: ${fromStorage} → ${toStorage} for key: ${key}`)
  }

  /**
   * Record an error
   */
  recordError(storageType: string, operation: string, error: Error): void {
    if (!this.isEnabled) return

    const metrics = this.metrics.get(storageType)
    if (metrics) {
      metrics.errorCount++
    }

    console.error(`Storage error in ${storageType}.${operation}:`, error)
  }

  /**
   * Get comprehensive storage statistics
   */
  getStats(): StorageStats {
    const memory = this.metrics.get('memory')!
    const localStorage = this.metrics.get('localStorage')!
    const database = this.metrics.get('database')!

    const totalReads = memory.reads + localStorage.reads + database.reads
    const totalWrites = memory.writes + localStorage.writes + database.writes
    const totalHits = memory.hits + localStorage.hits + database.hits
    const totalMisses = memory.misses + localStorage.misses + database.misses
    const totalFallbacks = memory.fallbackCount + localStorage.fallbackCount + database.fallbackCount
    const totalErrors = memory.errorCount + localStorage.errorCount + database.errorCount
    const totalOperations = totalReads + totalWrites

    return {
      memory: {
        ...memory,
        size: 0, // Will be filled by storage manager
        keys: 0,
        items: []
      },
      localStorage: {
        ...localStorage,
        size: 0,
        keys: 0,
        items: []
      },
      database: {
        ...database,
        size: 0,
        keys: 0,
        items: []
      },
      overall: {
        totalOperations,
        averageReadTime: totalReads > 0 ? (memory.totalReadTime + localStorage.totalReadTime + database.totalReadTime) / totalReads : 0,
        averageWriteTime: totalWrites > 0 ? (memory.totalWriteTime + localStorage.totalWriteTime + database.totalWriteTime) / totalWrites : 0,
        hitRate: (totalHits + totalMisses) > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0,
        fallbackRate: totalOperations > 0 ? (totalFallbacks / totalOperations) * 100 : 0,
        errorRate: totalOperations > 0 ? (totalErrors / totalOperations) * 100 : 0
      }
    }
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics.clear()
    this.metrics.set('memory', this.createEmptyMetrics())
    this.metrics.set('localStorage', this.createEmptyMetrics())
    this.metrics.set('database', this.createEmptyMetrics())
  }

  /**
   * Get metrics for a specific storage type
   */
  getMetrics(storageType: string): StorageMetrics | null {
    return this.metrics.get(storageType) || null
  }

  /**
   * Log current statistics to console
   */
  logStats(): void {
    const stats = this.getStats()
    console.group('📊 Storage Analytics')
    console.log('Overall Performance:', {
      totalOperations: stats.overall.totalOperations,
      avgReadTime: `${stats.overall.averageReadTime.toFixed(2)}ms`,
      avgWriteTime: `${stats.overall.averageWriteTime.toFixed(2)}ms`,
      hitRate: `${stats.overall.hitRate.toFixed(1)}%`,
      fallbackRate: `${stats.overall.fallbackRate.toFixed(1)}%`,
      errorRate: `${stats.overall.errorRate.toFixed(1)}%`
    })
    console.log('Memory:', this.formatMetrics(stats.memory))
    console.log('LocalStorage:', this.formatMetrics(stats.localStorage))
    console.log('Database:', this.formatMetrics(stats.database))
    console.groupEnd()
  }

  private formatMetrics(metrics: StorageMetrics): object {
    return {
      reads: metrics.reads,
      writes: metrics.writes,
      hits: metrics.hits,
      misses: metrics.misses,
      avgReadTime: metrics.reads > 0 ? `${(metrics.totalReadTime / metrics.reads).toFixed(2)}ms` : '0ms',
      avgWriteTime: metrics.writes > 0 ? `${(metrics.totalWriteTime / metrics.writes).toFixed(2)}ms` : '0ms',
      fallbacks: metrics.fallbackCount,
      errors: metrics.errorCount
    }
  }
}

export const storageAnalytics = StorageAnalytics.getInstance()

// Note: Decorator functionality has been moved to manual performance measurement
// in each storage manager to avoid TypeScript decorator configuration issues
