import { cacheFlow, CacheOptions } from '../../../lib/cache-flow'
import { IStorageManager } from './IStorageManager'

/**
 * High-Performance Storage Manager
 * Implements the recommended cache flow: UI → Memory → Redis → PostgreSQL
 * 
 * Features:
 * - Automatic cache invalidation
 * - Bulk operations
 * - Performance monitoring
 * - Fallback strategies
 */
export class HighPerformanceStorageManager implements IStorageManager {
  private static instance: HighPerformanceStorageManager
  private initialized = false
  private stats = {
    hits: { memory: 0, redis: 0, database: 0 },
    misses: 0,
    sets: 0,
    errors: 0
  }

  private constructor() {}

  public static getInstance(): HighPerformanceStorageManager {
    if (!HighPerformanceStorageManager.instance) {
      HighPerformanceStorageManager.instance = new HighPerformanceStorageManager()
    }
    return HighPerformanceStorageManager.instance
  }

  /**
   * Initialize the high-performance storage system
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await cacheFlow.initialize()
      this.initialized = true
      console.log('🚀 High-Performance Storage Manager initialized')
    } catch (error) {
      console.error('Failed to initialize High-Performance Storage Manager:', error)
      throw error
    }
  }

  /**
   * Check if storage is available
   */
  async isAvailable(): Promise<boolean> {
    return this.initialized
  }

  /**
   * Get value with high-performance cache flow
   */
  async get(key: string): Promise<string | null> {
    try {
      const result = await cacheFlow.get<string>(key)
      
      // Update stats
      if (result.success && result.data !== null) {
        this.stats.hits[result.source as keyof typeof this.stats.hits]++
      } else {
        this.stats.misses++
      }

      return result.data
    } catch (error) {
      this.stats.errors++
      console.error(`HighPerformanceStorage GET error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set value with high-performance cache flow
   */
  async set(key: string, value: string): Promise<boolean> {
    try {
      const success = await cacheFlow.set(key, value)
      
      if (success) {
        this.stats.sets++
      } else {
        this.stats.errors++
      }

      return success
    } catch (error) {
      this.stats.errors++
      console.error(`HighPerformanceStorage SET error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete value from all cache layers
   */
  async delete(key: string): Promise<boolean> {
    try {
      return await cacheFlow.delete(key)
    } catch (error) {
      this.stats.errors++
      console.error(`HighPerformanceStorage DELETE error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Get JSON object with type safety
   */
  async getJSON<T>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const result = await cacheFlow.get<T>(key, options)
      
      // Update stats
      if (result.success && result.data !== null) {
        this.stats.hits[result.source as keyof typeof this.stats.hits]++
      } else {
        this.stats.misses++
      }

      return result.data
    } catch (error) {
      this.stats.errors++
      console.error(`HighPerformanceStorage getJSON error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set JSON object
   */
  async setJSON<T>(key: string, value: T, options?: CacheOptions): Promise<boolean> {
    try {
      const success = await cacheFlow.set(key, value, options)
      
      if (success) {
        this.stats.sets++
      } else {
        this.stats.errors++
      }

      return success
    } catch (error) {
      this.stats.errors++
      console.error(`HighPerformanceStorage setJSON error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Bulk get operations for better performance
   */
  async getBulk<T>(keys: string[], options?: CacheOptions): Promise<Record<string, T | null>> {
    try {
      const results = await cacheFlow.getBulk<T>(keys, options)
      const data: Record<string, T | null> = {}
      
      Object.entries(results).forEach(([key, result]) => {
        data[key] = result.data
        
        // Update stats
        if (result.success && result.data !== null) {
          this.stats.hits[result.source as keyof typeof this.stats.hits]++
        } else {
          this.stats.misses++
        }
      })
      
      return data
    } catch (error) {
      this.stats.errors++
      console.error('HighPerformanceStorage getBulk error:', error)
      return {}
    }
  }

  /**
   * Bulk set operations
   */
  async setBulk<T>(data: Record<string, T>, options?: CacheOptions): Promise<boolean> {
    try {
      const promises = Object.entries(data).map(([key, value]) =>
        this.setJSON(key, value, options)
      )
      
      const results = await Promise.all(promises)
      return results.every(result => result)
    } catch (error) {
      this.stats.errors++
      console.error('HighPerformanceStorage setBulk error:', error)
      return false
    }
  }

  /**
   * Invalidate cache for specific keys
   */
  async invalidate(keys: string | string[]): Promise<void> {
    try {
      const keyArray = Array.isArray(keys) ? keys : [keys]
      
      for (const key of keyArray) {
        await cacheFlow.invalidate(key)
      }
    } catch (error) {
      console.error('HighPerformanceStorage invalidate error:', error)
    }
  }

  /**
   * Warm up cache for frequently accessed data
   */
  async warmUp(keys: string[]): Promise<void> {
    try {
      await cacheFlow.warmUp(keys)
    } catch (error) {
      console.error('HighPerformanceStorage warmUp error:', error)
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): any {
    const totalHits = Object.values(this.stats.hits).reduce((sum, hits) => sum + hits, 0)
    const totalRequests = totalHits + this.stats.misses
    const hitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0

    return {
      ...this.stats,
      totalHits,
      totalRequests,
      hitRate: Math.round(hitRate * 100) / 100,
      performance: {
        cacheEfficiency: hitRate > 80 ? 'Excellent' : hitRate > 60 ? 'Good' : 'Needs Improvement'
      }
    }
  }

  /**
   * Get detailed cache flow statistics
   */
  async getDetailedStats(): Promise<any> {
    try {
      const cacheStats = await cacheFlow.getStats()
      const localStats = this.getStats()
      
      return {
        local: localStats,
        cacheFlow: cacheStats,
        recommendations: this.generateRecommendations(localStats)
      }
    } catch (error) {
      console.error('Error getting detailed stats:', error)
      return { error: 'Failed to get stats' }
    }
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(stats: any): string[] {
    const recommendations: string[] = []
    
    if (stats.hitRate < 60) {
      recommendations.push('Consider increasing cache TTL or warming up frequently accessed data')
    }
    
    if (stats.errors > stats.totalRequests * 0.1) {
      recommendations.push('High error rate detected - check Redis and database connectivity')
    }
    
    if (stats.hits.database > stats.hits.memory + stats.hits.redis) {
      recommendations.push('Most requests hitting database - consider cache warm-up strategy')
    }
    
    return recommendations
  }

  /**
   * Clear all caches (use with caution)
   */
  async clear(): Promise<boolean> {
    try {
      // This would need to be implemented in cacheFlow
      console.warn('Clear operation not implemented for safety reasons')
      return false
    } catch (error) {
      console.error('HighPerformanceStorage clear error:', error)
      return false
    }
  }

  /**
   * Health check for all cache layers
   */
  async healthCheck(): Promise<{
    memory: boolean
    redis: boolean
    database: boolean
    overall: boolean
  }> {
    try {
      const testKey = `health_check_${Date.now()}`
      const testValue = 'test'
      
      // Test write and read
      const writeSuccess = await this.set(testKey, testValue)
      const readValue = await this.get(testKey)
      const readSuccess = readValue === testValue
      
      // Clean up
      await this.delete(testKey)
      
      const stats = await cacheFlow.getStats()
      
      return {
        memory: true, // Memory cache is always available
        redis: stats?.redis?.connected || false,
        database: writeSuccess && readSuccess,
        overall: writeSuccess && readSuccess
      }
    } catch (error) {
      console.error('Health check failed:', error)
      return {
        memory: false,
        redis: false,
        database: false,
        overall: false
      }
    }
  }
}

// Export singleton instance
export const highPerformanceStorage = HighPerformanceStorageManager.getInstance()
export default highPerformanceStorage
