/**
 * Storage Stress Tests & Resilience Testing
 * Automated tests for quota enforcement, LRU behavior, and failure scenarios
 */

import { storageFactory } from './StorageManagerFactory'
import { storageAnalytics } from './StorageAnalytics'
import { offlineManager } from './OfflineManager'

export interface TestResult {
  testName: string
  passed: boolean
  duration: number
  details: string
  metrics?: any
}

export interface StressTestConfig {
  iterations: number
  concurrency: number
  dataSize: number
  timeout: number
}

class StressTests {
  private static instance: StressTests
  private isRunning: boolean = false
  private results: TestResult[] = []

  public static getInstance(): StressTests {
    if (!StressTests.instance) {
      StressTests.instance = new StressTests()
    }
    return StressTests.instance
  }

  /**
   * Run all stress tests
   */
  async runAllTests(config: Partial<StressTestConfig> = {}): Promise<TestResult[]> {
    if (this.isRunning) {
      throw new Error('Tests are already running')
    }

    this.isRunning = true
    this.results = []

    const defaultConfig: StressTestConfig = {
      iterations: 1000,
      concurrency: 10,
      dataSize: 1024, // 1KB
      timeout: 30000, // 30 seconds
      ...config
    }

    console.log('🧪 Starting storage stress tests...')

    try {
      // Test 1: Bulk write performance
      await this.testBulkWrites(defaultConfig)

      // Test 2: Memory quota enforcement
      await this.testMemoryQuotaEnforcement()

      // Test 3: LocalStorage quota enforcement
      await this.testLocalStorageQuotaEnforcement()

      // Test 4: LRU eviction behavior
      await this.testLRUEviction()

      // Test 5: Concurrent access
      await this.testConcurrentAccess(defaultConfig)

      // Test 6: Network failure simulation
      await this.testNetworkFailures()

      // Test 7: Offline mode resilience
      await this.testOfflineResilience()

      // Test 8: Recovery after failures
      await this.testRecoveryScenarios()

      console.log('✅ All stress tests completed')
      return this.results
    } catch (error) {
      console.error('❌ Stress tests failed:', error)
      throw error
    } finally {
      this.isRunning = false
    }
  }

  /**
   * Test bulk write performance
   */
  private async testBulkWrites(config: StressTestConfig): Promise<void> {
    const startTime = performance.now()
    const testData = 'x'.repeat(config.dataSize)
    let successCount = 0

    try {
      const promises = []
      for (let i = 0; i < config.iterations; i++) {
        const promise = storageFactory.set(`bulk_test_${i}`, testData)
          .then(success => {
            if (success) successCount++
          })
          .catch(() => {}) // Ignore individual failures
        promises.push(promise)
      }

      await Promise.all(promises)

      const duration = performance.now() - startTime
      const passed = successCount >= config.iterations * 0.95 // 95% success rate

      this.results.push({
        testName: 'Bulk Writes',
        passed,
        duration,
        details: `${successCount}/${config.iterations} writes succeeded in ${duration.toFixed(2)}ms`,
        metrics: {
          successRate: (successCount / config.iterations) * 100,
          avgTimePerWrite: duration / config.iterations
        }
      })

      // Cleanup
      for (let i = 0; i < config.iterations; i++) {
        await storageFactory.delete(`bulk_test_${i}`)
      }
    } catch (error) {
      this.results.push({
        testName: 'Bulk Writes',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test memory quota enforcement
   */
  private async testMemoryQuotaEnforcement(): Promise<void> {
    const startTime = performance.now()
    const memoryManager = storageFactory.getStorageManager('memory')
    const testData = 'x'.repeat(1000) // 1KB per item

    try {
      let itemsStored = 0
      
      // Try to store more than the quota (1000 items)
      for (let i = 0; i < 1200; i++) {
        const success = await memoryManager.set(`quota_test_${i}`, testData)
        if (success) itemsStored++
      }

      // Check that quota was enforced
      const keys = await memoryManager.keys()
      const quotaEnforced = keys.length <= 1000

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'Memory Quota Enforcement',
        passed: quotaEnforced,
        duration,
        details: `Stored ${itemsStored} items, final count: ${keys.length}/1000`,
        metrics: {
          itemsStored,
          finalCount: keys.length,
          quotaEnforced
        }
      })

      // Cleanup
      for (const key of keys) {
        if (key.startsWith('quota_test_')) {
          await memoryManager.delete(key)
        }
      }
    } catch (error) {
      this.results.push({
        testName: 'Memory Quota Enforcement',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test LocalStorage quota enforcement
   */
  private async testLocalStorageQuotaEnforcement(): Promise<void> {
    const startTime = performance.now()
    const localManager = storageFactory.getStorageManager('local')
    const testData = 'x'.repeat(10000) // 10KB per item

    try {
      let itemsStored = 0
      
      // Try to store more than the quota (500 items)
      for (let i = 0; i < 600; i++) {
        const success = await localManager.set(`ls_quota_test_${i}`, testData)
        if (success) itemsStored++
      }

      // Check that quota was enforced
      const keys = await localManager.keys()
      const testKeys = keys.filter(k => k.startsWith('ls_quota_test_'))
      const quotaEnforced = testKeys.length <= 500

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'LocalStorage Quota Enforcement',
        passed: quotaEnforced,
        duration,
        details: `Stored ${itemsStored} items, final count: ${testKeys.length}/500`,
        metrics: {
          itemsStored,
          finalCount: testKeys.length,
          quotaEnforced
        }
      })

      // Cleanup
      for (const key of testKeys) {
        await localManager.delete(key)
      }
    } catch (error) {
      this.results.push({
        testName: 'LocalStorage Quota Enforcement',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test LRU eviction behavior
   */
  private async testLRUEviction(): Promise<void> {
    const startTime = performance.now()
    const memoryManager = storageFactory.getStorageManager('memory')

    try {
      // Fill memory to capacity
      for (let i = 0; i < 1000; i++) {
        await memoryManager.set(`lru_test_${i}`, `data_${i}`)
      }

      // Add one more item to trigger eviction
      await memoryManager.set('lru_trigger', 'trigger_data')

      // Check that the oldest item was evicted
      const firstItem = await memoryManager.get('lru_test_0')
      const lastItem = await memoryManager.get('lru_test_999')
      const triggerItem = await memoryManager.get('lru_trigger')

      const lruWorking = firstItem === null && lastItem !== null && triggerItem !== null

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'LRU Eviction',
        passed: lruWorking,
        duration,
        details: `First item evicted: ${firstItem === null}, Last item preserved: ${lastItem !== null}`,
        metrics: {
          firstItemEvicted: firstItem === null,
          lastItemPreserved: lastItem !== null,
          triggerItemStored: triggerItem !== null
        }
      })

      // Cleanup
      const keys = await memoryManager.keys()
      for (const key of keys) {
        if (key.startsWith('lru_test_') || key === 'lru_trigger') {
          await memoryManager.delete(key)
        }
      }
    } catch (error) {
      this.results.push({
        testName: 'LRU Eviction',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test concurrent access
   */
  private async testConcurrentAccess(config: StressTestConfig): Promise<void> {
    const startTime = performance.now()
    let successCount = 0
    let errorCount = 0

    try {
      const promises = []
      
      for (let i = 0; i < config.concurrency; i++) {
        const promise = this.runConcurrentOperations(i, config.iterations / config.concurrency)
          .then(result => {
            successCount += result.success
            errorCount += result.errors
          })
          .catch(() => errorCount++)
        
        promises.push(promise)
      }

      await Promise.all(promises)

      const duration = performance.now() - startTime
      const totalOps = config.iterations
      const successRate = (successCount / totalOps) * 100
      const passed = successRate >= 90 // 90% success rate for concurrent access

      this.results.push({
        testName: 'Concurrent Access',
        passed,
        duration,
        details: `${successCount}/${totalOps} operations succeeded (${successRate.toFixed(1)}%)`,
        metrics: {
          successCount,
          errorCount,
          successRate,
          concurrency: config.concurrency
        }
      })
    } catch (error) {
      this.results.push({
        testName: 'Concurrent Access',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Run concurrent operations for a single thread
   */
  private async runConcurrentOperations(threadId: number, operations: number): Promise<{success: number, errors: number}> {
    let success = 0
    let errors = 0

    for (let i = 0; i < operations; i++) {
      try {
        const key = `concurrent_${threadId}_${i}`
        const value = `data_${threadId}_${i}`
        
        const writeSuccess = await storageFactory.set(key, value)
        if (writeSuccess) {
          const readValue = await storageFactory.get(key)
          if (readValue === value) {
            success++
          } else {
            errors++
          }
          await storageFactory.delete(key)
        } else {
          errors++
        }
      } catch (error) {
        errors++
      }
    }

    return { success, errors }
  }

  /**
   * Test network failure simulation
   */
  private async testNetworkFailures(): Promise<void> {
    const startTime = performance.now()

    try {
      // Simulate network failure by temporarily disabling online status
      const originalOnline = navigator.onLine
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true })

      // Try database operations while "offline"
      const dbManager = storageFactory.getStorageManager('database')
      const success = await dbManager.set('network_test', 'test_data')

      // Restore online status
      Object.defineProperty(navigator, 'onLine', { value: originalOnline, writable: true })

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'Network Failure Simulation',
        passed: success, // Should succeed due to offline queueing
        duration,
        details: `Database write while offline: ${success ? 'succeeded' : 'failed'}`,
        metrics: {
          offlineWriteSuccess: success
        }
      })
    } catch (error) {
      this.results.push({
        testName: 'Network Failure Simulation',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test offline mode resilience
   */
  private async testOfflineResilience(): Promise<void> {
    const startTime = performance.now()

    try {
      const initialQueueSize = offlineManager.getQueueStatus().queueLength

      // Simulate offline operations
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true })
      
      // Perform operations that should be queued
      await storageFactory.set('offline_test_1', 'data1')
      await storageFactory.set('offline_test_2', 'data2')
      await storageFactory.delete('offline_test_1')

      const queueStatus = offlineManager.getQueueStatus()
      const operationsQueued = queueStatus.queueLength > initialQueueSize

      // Restore online status
      Object.defineProperty(navigator, 'onLine', { value: true, writable: true })

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'Offline Resilience',
        passed: operationsQueued,
        duration,
        details: `Operations queued: ${operationsQueued}, Queue size: ${queueStatus.queueLength}`,
        metrics: {
          operationsQueued,
          queueSize: queueStatus.queueLength
        }
      })
    } catch (error) {
      this.results.push({
        testName: 'Offline Resilience',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Test recovery scenarios
   */
  private async testRecoveryScenarios(): Promise<void> {
    const startTime = performance.now()

    try {
      // Test recovery from corrupted localStorage
      const originalGetItem = localStorage.getItem
      localStorage.getItem = () => 'invalid_json{'

      const localManager = storageFactory.getStorageManager('local')
      const result = await localManager.get('recovery_test')

      // Restore localStorage
      localStorage.getItem = originalGetItem

      const recoveredGracefully = result === null // Should return null for corrupted data

      const duration = performance.now() - startTime

      this.results.push({
        testName: 'Recovery Scenarios',
        passed: recoveredGracefully,
        duration,
        details: `Graceful recovery from corrupted data: ${recoveredGracefully}`,
        metrics: {
          recoveredGracefully
        }
      })
    } catch (error) {
      this.results.push({
        testName: 'Recovery Scenarios',
        passed: false,
        duration: performance.now() - startTime,
        details: `Test failed: ${error.message}`
      })
    }
  }

  /**
   * Get test results
   */
  getResults(): TestResult[] {
    return [...this.results]
  }

  /**
   * Generate test report
   */
  generateReport(): string {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.passed).length
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)

    let report = `# Storage Stress Test Report\n\n`
    report += `**Summary:** ${passedTests}/${totalTests} tests passed\n`
    report += `**Total Duration:** ${totalDuration.toFixed(2)}ms\n\n`

    for (const result of this.results) {
      const status = result.passed ? '✅' : '❌'
      report += `${status} **${result.testName}** (${result.duration.toFixed(2)}ms)\n`
      report += `   ${result.details}\n\n`
    }

    return report
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.results = []
  }
}

export const stressTests = StressTests.getInstance()

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).stressTests = stressTests
}
