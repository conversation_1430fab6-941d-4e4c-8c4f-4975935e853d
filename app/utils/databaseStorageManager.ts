"use client"

// Database-based storage manager to replace localStorage
export class DatabaseStorageManager {
  private static instance: DatabaseStorageManager
  private authToken: string | null = null
  private cache: Map<string, any> = new Map()

  public static getInstance(): DatabaseStorageManager {
    if (!DatabaseStorageManager.instance) {
      DatabaseStorageManager.instance = new DatabaseStorageManager()
    }
    return DatabaseStorageManager.instance
  }

  // Set auth token for API calls
  public setAuthToken(token: string | null) {
    this.authToken = token
  }

  // Get user preferences from database
  public async getUserPreferences(): Promise<Record<string, string>> {
    try {
      if (!this.authToken) {
        console.warn('No auth token available for preferences')
        return {}
      }

      const response = await fetch('/api/user-preferences', {
        credentials: 'include' // Use cookies for authentication
      })

      if (!response.ok) {
        throw new Error('Failed to fetch preferences')
      }

      const preferences = await response.json()
      
      // Cache preferences
      Object.entries(preferences).forEach(([key, value]) => {
        this.cache.set(`pref_${key}`, value)
      })

      return preferences
    } catch (error) {
      console.error('Error fetching user preferences:', error)
      return {}
    }
  }

  // Set user preference in database
  public async setUserPreference(key: string, value: string): Promise<boolean> {
    try {
      if (!this.authToken) {
        console.warn('No auth token available for setting preference')
        return false
      }

      const response = await fetch('/api/user-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({ key, value })
      })

      if (response.ok) {
        // Update cache
        this.cache.set(`pref_${key}`, value)
        return true
      }

      return false
    } catch (error) {
      console.error('Error setting user preference:', error)
      return false
    }
  }

  // Get cached preference (for immediate access)
  public getCachedPreference(key: string): string | null {
    return this.cache.get(`pref_${key}`) || null
  }

  // Get draft orders from database
  public async getDraftOrders(): Promise<Record<number, any>> {
    try {
      if (!this.authToken) {
        console.warn('No auth token available for draft orders')
        return {}
      }

      const response = await fetch('/api/draft-orders', {
        credentials: 'include' // Use cookies for authentication
      })

      if (!response.ok) {
        throw new Error('Failed to fetch draft orders')
      }

      const draftOrders = await response.json()
      
      // Cache draft orders
      this.cache.set('draft_orders', draftOrders)

      return draftOrders
    } catch (error) {
      console.error('Error fetching draft orders:', error)
      return {}
    }
  }

  // Save draft order to database
  public async saveDraftOrder(tableNumber: number, orderData: any): Promise<boolean> {
    try {
      if (!this.authToken) {
        console.warn('No auth token available for saving draft order')
        return false
      }

      const response = await fetch('/api/draft-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({ tableNumber, orderData })
      })

      if (response.ok) {
        // Update cache
        const cachedOrders = this.cache.get('draft_orders') || {}
        cachedOrders[tableNumber] = orderData
        this.cache.set('draft_orders', cachedOrders)
        return true
      }

      return false
    } catch (error) {
      console.error('Error saving draft order:', error)
      return false
    }
  }

  // Delete draft order from database
  public async deleteDraftOrder(tableNumber: number): Promise<boolean> {
    try {
      if (!this.authToken) {
        console.warn('No auth token available for deleting draft order')
        return false
      }

      const response = await fetch(`/api/draft-orders?tableNumber=${tableNumber}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      })

      if (response.ok) {
        // Update cache
        const cachedOrders = this.cache.get('draft_orders') || {}
        delete cachedOrders[tableNumber]
        this.cache.set('draft_orders', cachedOrders)
        return true
      }

      return false
    } catch (error) {
      console.error('Error deleting draft order:', error)
      return false
    }
  }

  // Get cached draft orders (for immediate access)
  public getCachedDraftOrders(): Record<number, any> {
    return this.cache.get('draft_orders') || {}
  }

  // Clear all cached data
  public clearCache() {
    this.cache.clear()
  }

  // Initialize - load all data from database
  public async initialize(): Promise<void> {
    if (!this.authToken) return

    try {
      // Load preferences and draft orders in parallel
      await Promise.all([
        this.getUserPreferences(),
        this.getDraftOrders()
      ])
    } catch (error) {
      console.error('Error initializing database storage:', error)
    }
  }
}

// Export singleton instance
export const dbStorageManager = DatabaseStorageManager.getInstance()
