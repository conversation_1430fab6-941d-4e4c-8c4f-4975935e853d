import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production'

export interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
}

export function verifyToken(token: string): User | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    return {
      id: decoded.userId,
      username: decoded.username,
      role: decoded.role,
      fullName: decoded.fullName
    }
  } catch (error) {
    return null
  }
}

export function getAuthHeader(request: Request): string | null {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7) // Remove 'Bearer ' prefix
}

export function requireAuth(request: Request): User | null {
  const token = getAuthHeader(request)
  if (!token) {
    return null
  }
  return verifyToken(token)
}

export function requireAdmin(request: Request): User | null {
  const user = requireAuth(request)
  if (!user || user.role !== 'admin') {
    return null
  }
  return user
}
