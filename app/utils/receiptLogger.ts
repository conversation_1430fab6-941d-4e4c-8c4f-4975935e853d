export const logReceiptToHistory = async (receiptData: any) => {
  try {
    // Format the data based on receipt type to match API expectations
    const formattedData = {
      type: receiptData.type,
      data: {
        tableNumber: receiptData.data.tableNumber || receiptData.data.table_number,
        cost: receiptData.data.cost || receiptData.data.total,
        duration: receiptData.data.duration || null,
        items: receiptData.data.items || null,
        startTime: receiptData.data.startTime || receiptData.data.start_time || null,
        endTime: receiptData.data.endTime || receiptData.data.end_time || null,
        status: receiptData.data.status || "completed",
        created_at: receiptData.data.created_at || new Date()
      },
      printedBy: receiptData.printedBy || "System",
      printedAt: receiptData.printedAt || new Date()
    };

    // Validate required fields before sending
    if (!formattedData.type || !formattedData.data.tableNumber || !formattedData.data.cost) {
      console.error('Missing required fields for receipt logging:', {
        type: formattedData.type,
        tableNumber: formattedData.data.tableNumber,
        cost: formattedData.data.cost
      });
      throw new Error('Missing required fields: type, tableNumber, and cost are required');
    }

    console.log('Sending receipt data:', formattedData);

    const response = await fetch('/api/receipts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Use cookies for authentication
      body: JSON.stringify(formattedData),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (parseError) {
        errorData = { error: `HTTP ${response.status}: ${response.statusText}` };
      }
      console.error('Receipt API error:', {
        status: response.status,
        statusText: response.statusText,
        errorData,
        sentData: formattedData
      });
      throw new Error(`Failed to log receipt (${response.status}): ${errorData.error || errorData.details || 'Unknown error'}`);
    }

    const result = await response.json();
    console.log('Receipt logged successfully:', result);
    return result;
  } catch (error) {
    console.error("Error logging receipt to history:", {
      error,
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
