'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function MigrateTablesPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runMigration = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔄 Running bar tables migration...')
      
      const response = await fetch('/api/migrate-bar-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const migrationResult = await response.json()
        setResult({
          success: true,
          message: 'Migration completed successfully!',
          migrationResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Migration failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Fix Bar Tables Database Constraint</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will fix the database constraint to allow multiple tables with the same number assigned to different users.
          </p>
          
          <Button onClick={runMigration} disabled={loading} size="lg">
            {loading ? 'Running Migration...' : 'Run Migration'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Migration Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
