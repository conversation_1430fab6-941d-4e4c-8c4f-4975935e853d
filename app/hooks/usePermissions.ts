"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"

interface PermissionSet {
  games: {
    viewAllTables: boolean
    viewOwnTablesOnly: boolean
    createNewGames: boolean
    stopAnyGame: boolean
    stopOwnGamesOnly: boolean
    viewAllHistory: boolean
    viewOwnHistoryOnly: boolean
  }
  bar: {
    viewAllOrders: boolean
    viewOwnOrdersOnly: boolean
    createOrders: boolean
    editAnyOrder: boolean
    editOwnOrdersOnly: boolean
    viewAllHistory: boolean
    viewOwnHistoryOnly: boolean
  }
  analytics: {
    viewAnalytics: boolean
    viewOwnStats: boolean
  }
  receipts: {
    viewAllReceipts: boolean
    viewOwnReceiptsOnly: boolean
    reprintAnyReceipt: boolean
    reprintOwnReceiptsOnly: boolean
  }
  settings: {
    accessSettings: boolean
    manageUsers: boolean
    managePermissions: boolean
  }
}

interface Permissions {
  waiter: PermissionSet
  admin: PermissionSet
}

// Default permissions - admins get everything, waiters get restricted access
const defaultPermissions: Permissions = {
  waiter: {
    games: {
      viewAllTables: false,
      viewOwnTablesOnly: true,
      createNewGames: true,
      stopAnyGame: false,
      stopOwnGamesOnly: true,
      viewAllHistory: false,
      viewOwnHistoryOnly: true
    },
    bar: {
      viewAllOrders: false,
      viewOwnOrdersOnly: true,
      createOrders: true,
      editAnyOrder: false,
      editOwnOrdersOnly: true,
      viewAllHistory: false,
      viewOwnHistoryOnly: true
    },
    analytics: {
      viewAnalytics: false,
      viewOwnStats: true
    },
    receipts: {
      viewAllReceipts: false,
      viewOwnReceiptsOnly: true,
      reprintAnyReceipt: false,
      reprintOwnReceiptsOnly: true
    },
    settings: {
      accessSettings: false,
      manageUsers: false,
      managePermissions: false
    }
  },
  admin: {
    games: {
      viewAllTables: true,
      viewOwnTablesOnly: true,
      createNewGames: true,
      stopAnyGame: true,
      stopOwnGamesOnly: true,
      viewAllHistory: true,
      viewOwnHistoryOnly: true
    },
    bar: {
      viewAllOrders: true,
      viewOwnOrdersOnly: true,
      createOrders: true,
      editAnyOrder: true,
      editOwnOrdersOnly: true,
      viewAllHistory: true,
      viewOwnHistoryOnly: true
    },
    analytics: {
      viewAnalytics: true,
      viewOwnStats: true
    },
    receipts: {
      viewAllReceipts: true,
      viewOwnReceiptsOnly: true,
      reprintAnyReceipt: true,
      reprintOwnReceiptsOnly: true
    },
    settings: {
      accessSettings: true,
      manageUsers: true,
      managePermissions: true
    }
  }
}

export function usePermissions() {
  const { user } = useAuth()
  const [permissions, setPermissions] = useState<PermissionSet | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadPermissions()
    }
  }, [user])

  const loadPermissions = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/permissions', {
        credentials: 'include' // Include cookies for authentication
      })

      if (response.ok) {
        const data = await response.json()
        const userRole = user?.role as 'admin' | 'waiter'
        setPermissions(data[userRole] || defaultPermissions[userRole])
      } else {
        // Use default permissions if API fails
        const userRole = user?.role as 'admin' | 'waiter'
        setPermissions(defaultPermissions[userRole])
      }
    } catch (error) {
      console.error('Failed to load permissions:', error)
      // Use default permissions if API fails
      const userRole = user?.role as 'admin' | 'waiter'
      setPermissions(defaultPermissions[userRole])
    } finally {
      setLoading(false)
    }
  }

  // Helper functions to check specific permissions
  const canViewAllTables = () => permissions?.games.viewAllTables ?? false
  const canViewOwnTablesOnly = () => permissions?.games.viewOwnTablesOnly ?? true
  const canCreateNewGames = () => permissions?.games.createNewGames ?? true
  const canStopAnyGame = () => permissions?.games.stopAnyGame ?? false
  const canStopOwnGamesOnly = () => permissions?.games.stopOwnGamesOnly ?? true
  const canViewAllGameHistory = () => permissions?.games.viewAllHistory ?? false
  const canViewOwnGameHistory = () => permissions?.games.viewOwnHistoryOnly ?? true

  const canViewAllOrders = () => permissions?.bar.viewAllOrders ?? false
  const canViewOwnOrdersOnly = () => permissions?.bar.viewOwnOrdersOnly ?? true
  const canCreateOrders = () => permissions?.bar.createOrders ?? true
  const canEditAnyOrder = () => permissions?.bar.editAnyOrder ?? false
  const canEditOwnOrdersOnly = () => permissions?.bar.editOwnOrdersOnly ?? true
  const canViewAllBarHistory = () => permissions?.bar.viewAllHistory ?? false
  const canViewOwnBarHistory = () => permissions?.bar.viewOwnHistoryOnly ?? true

  const canViewAnalytics = () => permissions?.analytics.viewAnalytics ?? false
  const canViewOwnStats = () => permissions?.analytics.viewOwnStats ?? true

  const canViewAllReceipts = () => permissions?.receipts.viewAllReceipts ?? false
  const canViewOwnReceiptsOnly = () => permissions?.receipts.viewOwnReceiptsOnly ?? true
  const canReprintAnyReceipt = () => permissions?.receipts.reprintAnyReceipt ?? false
  const canReprintOwnReceiptsOnly = () => permissions?.receipts.reprintOwnReceiptsOnly ?? true

  const canAccessSettings = () => permissions?.settings.accessSettings ?? false
  const canManageUsers = () => permissions?.settings.manageUsers ?? false
  const canManagePermissions = () => permissions?.settings.managePermissions ?? false

  // Data filtering helpers
  const filterGameData = (data: any[]) => {
    if (!user || !permissions) return data

    if (canViewAllTables()) {
      return data // Show all data
    } else if (canViewOwnTablesOnly()) {
      // For games: show ALL active games (for table availability) + only own completed games
      // This matches the API behavior and ensures waiters can see table availability correctly
      return data.filter(item =>
        item.status === 'active' || // Show all active games regardless of creator
        item.user_id === user.id || item.userId === user.id || item.created_by === user.id // Show own completed games
      )
    }
    return [] // No access
  }

  const filterBarData = (data: any[]) => {
    if (!user || !permissions) return data

    if (canViewAllOrders()) {
      return data // Show all data
    } else if (canViewOwnOrdersOnly()) {
      return data.filter(item => item.user_id === user.id || item.userId === user.id || item.created_by === user.id)
    }
    return [] // No access
  }

  const filterReceiptData = (data: any[]) => {
    if (!user || !permissions) return data

    if (canViewAllReceipts()) {
      return data // Show all data
    } else if (canViewOwnReceiptsOnly()) {
      return data.filter(item => item.user_id === user.id || item.userId === user.id || item.created_by === user.id)
    }
    return [] // No access
  }

  return {
    permissions,
    loading,

    // Game permissions
    canViewAllTables,
    canViewOwnTablesOnly,
    canCreateNewGames,
    canStopAnyGame,
    canStopOwnGamesOnly,
    canViewAllGameHistory,
    canViewOwnGameHistory,

    // Bar permissions
    canViewAllOrders,
    canViewOwnOrdersOnly,
    canCreateOrders,
    canEditAnyOrder,
    canEditOwnOrdersOnly,
    canViewAllBarHistory,
    canViewOwnBarHistory,

    // Analytics permissions
    canViewAnalytics,
    canViewOwnStats,

    // Receipt permissions
    canViewAllReceipts,
    canViewOwnReceiptsOnly,
    canReprintAnyReceipt,
    canReprintOwnReceiptsOnly,

    // Settings permissions
    canAccessSettings,
    canManageUsers,
    canManagePermissions,

    // Data filtering helpers
    filterGameData,
    filterBarData,
    filterReceiptData,
  }
}
