import { useTranslation } from 'react-i18next'
import { useState, useEffect } from 'react'
import { isI18nReady, getSafeTranslation } from '../utils/i18nInit'

/**
 * Safe translation hook that provides fallbacks when i18next is not initialized
 */
export function useSafeTranslation() {
  const [isReady, setIsReady] = useState(false)

  // Try to get the translation hook, but handle errors gracefully
  let translationHook: any = null
  let hasError = false

  try {
    translationHook = useTranslation()
  } catch (error) {
    console.warn('i18next not initialized, using fallback translations:', error)
    hasError = true
  }

  useEffect(() => {
    // Check if i18next is ready
    if (isI18nReady() && translationHook && translationHook.ready) {
      setIsReady(true)
    } else if (!hasError) {
      // Wait a bit and check again
      const timer = setTimeout(() => {
        setIsReady(true)
      }, 100)
      return () => clearTimeout(timer)
    } else {
      setIsReady(true) // Use fallbacks
    }
  }, [translationHook, hasError])



  // Safe translation function
  const t = (key: string, options?: any): string => {
    // Always use fallback first to ensure we never return undefined
    try {
      if (!hasError && translationHook && translationHook.t) {
        try {
          const translation = translationHook.t(key, options)
          // If translation equals the key, it means translation is missing
          if (translation !== key) {
            return translation
          }
        } catch (error) {
          console.warn(`Translation error for key ${key}:`, error)
        }
      }

      // Use the safe translation utility as fallback
      const fallbackTranslation = getSafeTranslation()(key, options)
      return fallbackTranslation || key
    } catch (error) {
      console.error(`Critical translation error for key ${key}:`, error)
      return key
    }
  }

  // Safe i18n object with proper changeLanguage function
  const i18n = translationHook?.i18n || {
    language: typeof window !== 'undefined' ? localStorage.getItem('i18nextLng') || 'sq' : 'sq',
    changeLanguage: async (lng: string) => {
      try {
        // Store preference even if i18next is not ready
        if (typeof window !== 'undefined') {
          localStorage.setItem('i18nextLng', lng)
        }

        // Try to use real i18next if available
        if (translationHook?.i18n?.changeLanguage) {
          await translationHook.i18n.changeLanguage(lng)
          return
        }

        console.warn('i18next not initialized, language preference stored for next reload')

        // Force reload to apply language change
        if (typeof window !== 'undefined') {
          window.location.reload()
        }

        return Promise.resolve()
      } catch (error) {
        console.error('Failed to change language:', error)
        return Promise.reject(error)
      }
    }
  }

  return {
    t,
    i18n,
    ready: isReady,
    isLoading: !isReady
  }
}

export default useSafeTranslation
