import { useTranslation as useI18nTranslation } from 'react-i18next'

/**
 * Enhanced translation hook with additional utilities
 */
export function useTranslation(namespace?: string) {
  const { t, i18n, ready } = useI18nTranslation(namespace)

  /**
   * Translate with fallback to key if translation is missing
   */
  const translate = (key: string, options?: any) => {
    const translation = t(key, options)
    
    // If translation equals the key, it means translation is missing
    if (translation === key && process.env.NODE_ENV === 'development') {
      console.warn(`Missing translation for key: ${key} in language: ${i18n.language}`)
    }
    
    return translation
  }

  /**
   * Translate with pluralization support
   */
  const translatePlural = (key: string, count: number, options?: any) => {
    return t(key, { count, ...options })
  }

  /**
   * Get translation for common UI elements
   */
  const common = {
    loading: () => translate('common.loading'),
    save: () => translate('common.save'),
    cancel: () => translate('common.cancel'),
    delete: () => translate('common.delete'),
    edit: () => translate('common.edit'),
    add: () => translate('common.add'),
    search: () => translate('common.search'),
    filter: () => translate('common.filter'),
    export: () => translate('common.export'),
    import: () => translate('common.import'),
    refresh: () => translate('common.refresh'),
    close: () => translate('common.close'),
    confirm: () => translate('common.confirm'),
    yes: () => translate('common.yes'),
    no: () => translate('common.no'),
    ok: () => translate('common.ok'),
    error: () => translate('common.error'),
    success: () => translate('common.success'),
    warning: () => translate('common.warning'),
    info: () => translate('common.info')
  }

  /**
   * Get navigation translations
   */
  const nav = {
    dashboard: () => translate('navigation.dashboard'),
    games: () => translate('navigation.games'),
    bar: () => translate('navigation.bar'),
    analytics: () => translate('navigation.analytics'),
    settings: () => translate('navigation.settings'),
    logout: () => translate('navigation.logout')
  }

  /**
   * Get error message translations
   */
  const errors = {
    network: () => translate('errors.networkError'),
    server: () => translate('errors.serverError'),
    validation: () => translate('errors.validationError'),
    permission: () => translate('errors.permissionDenied'),
    notFound: () => translate('errors.notFound'),
    quotaExceeded: () => translate('errors.quotaExceeded'),
    offline: () => translate('errors.offlineMode')
  }

  /**
   * Get success message translations
   */
  const success = {
    saved: () => translate('success.saved'),
    deleted: () => translate('success.deleted'),
    synced: () => translate('success.synced'),
    cacheCleared: () => translate('success.cacheCleared')
  }

  /**
   * Format currency with localization
   */
  const formatCurrency = (amount: number, currency: string = 'EUR') => {
    try {
      return new Intl.NumberFormat(i18n.language, {
        style: 'currency',
        currency: currency
      }).format(amount)
    } catch (error) {
      // Fallback to simple formatting
      return `${amount.toFixed(2)} ${currency}`
    }
  }

  /**
   * Format date with localization
   */
  const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date

    try {
      // Use Albanian locale for Albanian language
      const locale = i18n.language === 'sq' ? 'sq-AL' : i18n.language
      return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        ...options
      }).format(dateObj)
    } catch (error) {
      // Fallback to manual formatting for Albanian
      if (i18n.language === 'sq') {
        const months = ['Jan', 'Shk', 'Mar', 'Pri', 'Maj', 'Qer', 'Kor', 'Gus', 'Sht', 'Tet', 'Nën', 'Dhj']
        return `${dateObj.getDate()} ${months[dateObj.getMonth()]} ${dateObj.getFullYear()}`
      }
      return dateObj.toLocaleDateString()
    }
  }

  /**
   * Format time with localization
   */
  const formatTime = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date

    try {
      // Use Albanian locale for Albanian language
      const locale = i18n.language === 'sq' ? 'sq-AL' : i18n.language
      return new Intl.DateTimeFormat(locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format for Albanian
        ...options
      }).format(dateObj)
    } catch (error) {
      // Fallback to manual formatting
      const hours = dateObj.getHours().toString().padStart(2, '0')
      const minutes = dateObj.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
  }

  /**
   * Format numbers with localization
   */
  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    try {
      return new Intl.NumberFormat(i18n.language, options).format(number)
    } catch (error) {
      return number.toString()
    }
  }

  return {
    t: translate,
    tPlural: translatePlural,
    i18n,
    ready,
    common,
    nav,
    errors,
    success,
    formatCurrency,
    formatDate,
    formatTime,
    formatNumber,
    currentLanguage: i18n.language,
    isLoading: !ready
  }
}
