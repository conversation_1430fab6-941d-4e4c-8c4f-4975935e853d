'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function CreateAdminPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const createAdmin = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/create-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Create Admin User</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This will create an admin user with username "admin" and password "admin123".
        </p>
        
        <Button onClick={createAdmin} disabled={loading}>
          {loading ? 'Creating Admin...' : 'Create Admin User'}
        </Button>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">Result:</h3>
            <pre className="mt-2 text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
