'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function RemoveReservationSystemPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const removeReservationSystem = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🗑️ Removing table reservation system...')
      
      const response = await fetch('/api/remove-reservation-system', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const removalResult = await response.json()
        setResult({
          success: true,
          message: 'Table reservation system removed successfully!',
          removalResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Removal failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>🗑️ Remove Table Reservation System</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will completely remove the table reservation system from the database and application.
          </p>
          
          <div className="p-4 bg-red-50 rounded-lg border border-red-200">
            <h3 className="font-semibold text-red-800 mb-2">⚠️ What will be removed:</h3>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• table_sessions table</li>
              <li>• Reservation columns from gametables (current_user_id, current_username, locked_at, session_expires_at)</li>
              <li>• Reservation-related database functions</li>
              <li>• Reservation-related database views</li>
              <li>• Reservation-related indexes</li>
              <li>• Reservation UI components and logic</li>
            </ul>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="font-semibold text-green-800 mb-2">✅ What will remain:</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• User assignment system (assigned_user_id, assigned_username)</li>
              <li>• Multi-user table isolation</li>
              <li>• Duplicate table numbers for different users</li>
              <li>• All game and bar functionality</li>
              <li>• Settings and management features</li>
            </ul>
          </div>
          
          <Button onClick={removeReservationSystem} disabled={loading} size="lg" variant="destructive">
            {loading ? 'Removing Reservation System...' : 'Remove Reservation System'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Removal Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>After removal:</strong></p>
            <p>• Tables will work without reservation system</p>
            <p>• Users can directly start games on available tables</p>
            <p>• Multi-user isolation will still work perfectly</p>
            <p>• No more table locking or session management</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
