'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestUserIsolationPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testIsolation = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔍 Testing user table isolation...')
      
      const response = await fetch('/api/test-user-isolation')
      
      if (response.ok) {
        const testResult = await response.json()
        setResult(testResult)
      } else {
        const error = await response.text()
        setResult({
          error: 'Test failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>🔍 Test User Table Isolation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will test that each user sees only their assigned tables and no cross-user interference exists.
          </p>
          
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">🎯 Expected Results:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Each user sees only their assigned tables</li>
              <li>• Admin's Table 1 is separate from Gesti's Table 1</li>
              <li>• No unassigned tables visible to users</li>
              <li>• Complete data isolation between users</li>
              <li>• Multiple users can have same table numbers</li>
            </ul>
          </div>
          
          <Button onClick={testIsolation} disabled={loading} size="lg">
            {loading ? 'Testing Isolation...' : 'Test User Isolation'}
          </Button>
          
          {result && (
            <div className="mt-4 space-y-4">
              {result.success && (
                <>
                  <div className={`p-4 rounded-lg border ${
                    result.data.summary.perfectIsolation 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-yellow-50 border-yellow-200'
                  }`}>
                    <h3 className={`font-semibold mb-2 ${
                      result.data.summary.perfectIsolation 
                        ? 'text-green-800' 
                        : 'text-yellow-800'
                    }`}>
                      {result.data.summary.perfectIsolation ? '✅ Perfect Isolation!' : '⚠️ Isolation Issues'}
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>Total Users: {result.data.summary.totalUsers}</p>
                      <p>Total Tables: {result.data.summary.totalTables}</p>
                      <p>Assigned Tables: {result.data.summary.assignedTables}</p>
                      <p>Unassigned Tables: {result.data.summary.unassignedTables}</p>
                      <p>Users with Tables: {result.data.summary.usersWithTables}</p>
                    </div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-semibold mb-2">👥 Tables by User:</h3>
                    <div className="space-y-2">
                      {result.data.users.map((userData: any) => (
                        <div key={userData.user.id} className="p-2 bg-white rounded border">
                          <div className="font-medium">{userData.user.username} ({userData.user.fullName})</div>
                          <div className="text-sm text-gray-600">
                            {userData.tables.length === 0 ? (
                              <span className="text-red-600">No tables assigned</span>
                            ) : (
                              userData.tables.map((table: any) => (
                                <span key={table.id} className="inline-block mr-2 px-2 py-1 bg-blue-100 rounded text-xs">
                                  Table {table.number} - {table.name}
                                </span>
                              ))
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {result.data.tableConflicts.length > 0 && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h3 className="font-semibold text-blue-800 mb-2">📊 Table Number Sharing (OK):</h3>
                      <div className="space-y-1 text-sm">
                        {result.data.tableConflicts.map((conflict: any) => (
                          <div key={conflict.tableNumber}>
                            Table {conflict.tableNumber}: Used by {conflict.userCount} users ({conflict.users.join(', ')})
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.data.unassignedTables.length > 0 && (
                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Unassigned Tables:</h3>
                      <div className="space-y-1 text-sm">
                        {result.data.unassignedTables.map((table: any) => (
                          <div key={table.id}>
                            Table {table.number} - {table.name} (ID: {table.id})
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-yellow-700 mt-2">
                        Assign these tables to users for complete isolation.
                      </p>
                    </div>
                  )}
                </>
              )}
              
              <details className="mt-4">
                <summary className="cursor-pointer font-medium">Raw Test Data</summary>
                <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap bg-gray-100 p-4 rounded">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
