import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ClientProviders } from "./components/ClientProviders"
import MainApp from "./main"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "BBM",
  description: "Modern dashboard for billiard club management with game timing and bar orders",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClientProviders>
          <MainApp />
        </ClientProviders>
      </body>
    </html>
  )
}
