// Hardcoded game table configuration
// This replaces database calls for table management

export interface GameTable {
  id: string
  number: number
  name: string
  isActive: boolean
  hourlyRate: number
  tableType: string
  assignedUserId?: number
  assignedUsername?: string
  customSoundUrl?: string
}

// Hardcoded table configuration - modify this to change tables
export const GAME_TABLES: GameTable[] = [
  {
    id: "1",
    number: 1,
    name: "Table 1",
    isActive: true,
    hourlyRate: 400,
    tableType: "billiard"
  },
  {
    id: "2",
    number: 2,
    name: "Table 2",
    isActive: true,
    hourlyRate: 400,
    tableType: "billiard"
  },
  {
    id: "3",
    number: 3,
    name: "Table 3",
    isActive: true,
    hourlyRate: 500,
    tableType: "pool"
  },
  {
    id: "4",
    number: 4,
    name: "Table 4",
    isActive: true,
    hourlyRate: 500,
    tableType: "pool"
  },
  {
    id: "5",
    number: 5,
    name: "Table 5",
    isActive: true,
    hourlyRate: 600,
    tableType: "vip"
  },
  {
    id: "6",
    number: 6,
    name: "Table 6",
    isActive: true,
    hourlyRate: 350,
    tableType: "standard"
  },
  {
    id: "7",
    number: 7,
    name: "Table 7",
    isActive: false,
    hourlyRate: 550,
    tableType: "premium"
  },
  {
    id: "8",
    number: 8,
    name: "Table 8",
    isActive: true,
    hourlyRate: 700,
    tableType: "tournament"
  }
]

// Helper functions for table management
export const getTableById = (id: string): GameTable | undefined => {
  return GAME_TABLES.find(table => table.id === id)
}

export const getTableByNumber = (number: number): GameTable | undefined => {
  return GAME_TABLES.find(table => table.number === number)
}

export const getActiveTables = (): GameTable[] => {
  return GAME_TABLES.filter(table => table.isActive)
}

export const getAllTables = (): GameTable[] => {
  return [...GAME_TABLES]
}

export const getTablesByType = (type: string): GameTable[] => {
  return GAME_TABLES.filter(table => table.tableType === type)
}
