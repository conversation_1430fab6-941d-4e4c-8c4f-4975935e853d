// Shared type definitions for the Billiard Dashboard

export interface OrderItem {
  id: number
  name: string
  price: number
  quantity: number
  category?: string
  isActive?: boolean
}

export interface Order {
  id: string
  table_number: number
  items: OrderItem[]
  total: number
  status: "pending" | "completed"
  created_at: Date
  created_by_username?: string
  created_by_name?: string
  created_by_role?: string
}

export interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  created_by?: number
  created_by_name?: string
  created_by_role?: string
}

export interface DrinkItem {
  id: number
  name: string
  price: number
  category: string
  isActive: boolean
  description?: string
}

export interface Table {
  id: string
  number: number
  name: string
  isActive: boolean
  hourlyRate: number
}

export interface TableOrder {
  table_number: number
  items: OrderItem[]
  total: number
  lastUpdated: Date
}

export interface CurrencySettings {
  id: number
  currency_type: string
  decimal_places: number
  tax_included: boolean
  tax_rate: number
  tax_enabled: boolean
  qr_code_enabled: boolean
  qr_code_url: string
}

export interface BusinessInfo {
  id: number
  name: string
  address: string
  phone: string
  email: string
  vat_number: string
}
