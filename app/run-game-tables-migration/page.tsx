'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function RunGameTablesMigrationPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runMigration = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔄 Running game tables migration...')
      
      const response = await fetch('/api/migrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const migrationResult = await response.json()
        setResult({
          success: true,
          message: 'Game tables migration completed successfully!',
          migrationResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Migration failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const checkMigrationStatus = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/migrate', {
        method: 'GET'
      })
      
      if (response.ok) {
        const statusResult = await response.json()
        setResult({
          success: true,
          message: 'Migration status checked',
          statusResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Status check failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Game Tables Migration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will ensure game tables support user assignment and duplicate table numbers for different users.
          </p>
          
          <div className="flex gap-4">
            <Button onClick={checkMigrationStatus} disabled={loading} variant="outline">
              {loading ? 'Checking...' : 'Check Migration Status'}
            </Button>
            
            <Button onClick={runMigration} disabled={loading}>
              {loading ? 'Running Migration...' : 'Run Migration'}
            </Button>
          </div>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>What this migration does:</strong></p>
            <p>• Adds assigned_user_id and assigned_username columns to gametables</p>
            <p>• Adds user assignment columns to tables (bar tables)</p>
            <p>• Creates indexes for better performance</p>
            <p>• Enables duplicate table numbers for different users</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
