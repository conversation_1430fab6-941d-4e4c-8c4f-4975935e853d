"use client"

import React, { createContext, useContext, useState, useCallback } from 'react'

interface DataRefreshContextType {
  refreshTrigger: number
  refreshGames: () => void
  refreshOrders: () => void
  refreshAll: () => void
  refreshAnalytics: () => void
  refreshReceipts: () => void
}

const DataRefreshContext = createContext<DataRefreshContextType | undefined>(undefined)

export function DataRefreshProvider({ children }: { children: React.ReactNode }) {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const refreshGames = useCallback(() => {
    console.log('Triggering games data refresh')
    setRefreshTrigger(prev => prev + 1)
    // Dispatch custom event for games refresh
    window.dispatchEvent(new CustomEvent('refreshGames'))
  }, [])

  const refreshOrders = useCallback(() => {
    console.log('Triggering orders data refresh')
    setRefreshTrigger(prev => prev + 1)
    // Dispatch custom event for orders refresh
    window.dispatchEvent(new CustomEvent('refreshOrders'))
  }, [])

  const refreshAll = useCallback(() => {
    console.log('Triggering all data refresh')
    setRefreshTrigger(prev => prev + 1)
    // Dispatch custom events for all data types
    window.dispatchEvent(new CustomEvent('refreshGames'))
    window.dispatchEvent(new CustomEvent('refreshOrders'))
    window.dispatchEvent(new CustomEvent('refreshAnalytics'))
    window.dispatchEvent(new CustomEvent('refreshReceipts'))
  }, [])

  const refreshAnalytics = useCallback(() => {
    console.log('Triggering analytics data refresh')
    setRefreshTrigger(prev => prev + 1)
    // Dispatch custom event for analytics refresh
    window.dispatchEvent(new CustomEvent('refreshAnalytics'))
  }, [])

  const refreshReceipts = useCallback(() => {
    console.log('Triggering receipts data refresh')
    setRefreshTrigger(prev => prev + 1)
    // Dispatch custom event for receipts refresh
    window.dispatchEvent(new CustomEvent('refreshReceipts'))
  }, [])

  const value = {
    refreshTrigger,
    refreshGames,
    refreshOrders,
    refreshAll,
    refreshAnalytics,
    refreshReceipts
  }

  return (
    <DataRefreshContext.Provider value={value}>
      {children}
    </DataRefreshContext.Provider>
  )
}

export function useDataRefresh() {
  const context = useContext(DataRefreshContext)
  if (context === undefined) {
    throw new Error('useDataRefresh must be used within a DataRefreshProvider')
  }
  return context
}
