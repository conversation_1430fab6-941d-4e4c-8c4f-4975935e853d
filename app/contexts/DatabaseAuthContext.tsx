"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { configureStorage, getStorageManager } from '../utils/storage/StorageManagerFactory'

interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
  profilePicture?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: User) => void
  isLoading: boolean
}

const DatabaseAuthContext = createContext<AuthContextType | undefined>(undefined)

export function DatabaseAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check authentication using HttpOnly cookies
    // The cookie will be automatically sent with the request
    const validateAuth = async () => {
      try {
        const response = await fetch('/api/auth/validate', {
          method: 'POST',
          credentials: 'include', // Include cookies
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          if (data.valid && data.user) {
            setUser(data.user)
            setToken('authenticated') // We don't store the actual token client-side

            // Configure storage with auth token (for database storage)
            // Note: We'll need to get the token from the response or use a different approach
            configureStorage({ authToken: 'authenticated' })

            // Initialize database storage
            const dbStorage = getStorageManager('database')
            if (dbStorage && 'initialize' in dbStorage) {
              await (dbStorage as any).initialize()
            }
          }
        }
      } catch (error) {
        console.error('Auth validation error:', error)
      } finally {
        setIsLoading(false)
      }
    }

    validateAuth()
  }, [])

  const login = async (userData: User, authToken: string) => {
    setUser(userData)
    setToken(authToken)

    // Configure storage with auth token
    configureStorage({ authToken })

    // Initialize database storage
    const dbStorage = getStorageManager('database')
    if (dbStorage && 'initialize' in dbStorage) {
      await (dbStorage as any).initialize()
    }
  }

  const updateUser = (userData: User) => {
    setUser(userData)
    // User data is now managed server-side via cookies
    // No need to store in client-side storage
  }

  const logout = async () => {
    try {
      // Call logout API (will clear HttpOnly cookie)
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies
      })
    } catch (error) {
      console.error('Logout API error:', error)
    }

    // Clear local state
    setUser(null)
    setToken(null)

    // Clear storage configuration
    configureStorage({ authToken: null })

    // Clear database storage cache
    const dbStorage = getStorageManager('database')
    if (dbStorage && 'clearCache' in dbStorage) {
      (dbStorage as any).clearCache()
    }
  }

  return (
    <DatabaseAuthContext.Provider value={{ user, token, login, logout, updateUser, isLoading }}>
      {children}
    </DatabaseAuthContext.Provider>
  )
}

export function useDatabaseAuth() {
  const context = useContext(DatabaseAuthContext)
  if (context === undefined) {
    throw new Error('useDatabaseAuth must be used within a DatabaseAuthProvider')
  }
  return context
}
