"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
  profilePicture?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: User) => void
  isLoading: boolean
}

const NoStorageAuthContext = createContext<AuthContextType | undefined>(undefined)

export function NoStorageAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false) // No loading since no storage to check

  const login = (userData: User, authToken: string) => {
    setUser(userData)
    setToken(authToken)
    console.log('✅ User logged in (no storage)')
  }

  const updateUser = (userData: User) => {
    setUser(userData)
    console.log('✅ User updated (no storage)')
  }

  const logout = async () => {
    try {
      // Call logout API
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    } catch (error) {
      console.error('Logout API error:', error)
    }

    // Clear local state only
    setUser(null)
    setToken(null)
    console.log('✅ User logged out (no storage)')
  }

  return (
    <NoStorageAuthContext.Provider value={{ user, token, login, logout, updateUser, isLoading }}>
      {children}
    </NoStorageAuthContext.Provider>
  )
}

export function useNoStorageAuth() {
  const context = useContext(NoStorageAuthContext)
  if (context === undefined) {
    throw new Error('useNoStorageAuth must be used within a NoStorageAuthProvider')
  }
  return context
}
