"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { setUIPreference, getUIPreference } from '../utils/storage'

interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
  profilePicture?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: User) => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  console.log('🔧 AuthProvider component rendered')
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    console.log('🔐 AuthProvider useEffect triggered')

    // Simple timeout to ensure loading never gets stuck
    const maxLoadingTime = setTimeout(() => {
      console.log('🚨 Maximum loading time reached, setting isLoading to false')
      setIsLoading(false)
    }, 5000) // 5 second maximum

    // Check authentication using HttpOnly cookies
    const validateAuth = async () => {
      console.log('🔐 Starting authentication validation...')
      try {
        console.log('📡 Making auth validation request...')
        const response = await fetch('/api/auth/validate', {
          method: 'POST',
          credentials: 'include', // Include cookies
          headers: {
            'Content-Type': 'application/json'
          }
        })

        console.log('📡 Auth validation response:', response.status)

        if (response.ok) {
          const data = await response.json()
          console.log('✅ Auth validation successful:', data)
          if (data.valid && data.user) {
            setUser(data.user)
            setToken('authenticated') // We don't store the actual token client-side
          }
        } else {
          console.log('❌ Auth validation failed:', response.status)
        }
      } catch (error) {
        console.error('❌ Auth validation error:', error)
      } finally {
        console.log('🏁 Auth validation complete, setting isLoading to false')
        clearTimeout(maxLoadingTime)
        setIsLoading(false)
      }
    }

    validateAuth()

    // Cleanup timeout on unmount
    return () => {
      clearTimeout(maxLoadingTime)
    }
  }, [])

  const login = (userData: User, authToken: string) => {
    setUser(userData)
    setToken(authToken)
    // Cookies are set automatically by the server
    // No need to store auth data client-side
  }

  const updateUser = (userData: User) => {
    setUser(userData)
    // User data is now managed server-side via cookies
    // No need to store in client-side storage
  }

  const logout = async () => {
    try {
      // Call logout API (will clear HttpOnly cookie)
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies
      })
    } catch (error) {
      console.error('Logout API error:', error)
    }

    // Clear local state
    setUser(null)
    setToken(null)
  }

  return (
    <AuthContext.Provider value={{ user, token, login, logout, updateUser, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
