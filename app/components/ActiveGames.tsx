"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, DollarSign } from "lucide-react"
import { ReceiptPrinter } from "./ReceiptPrinter"
import { GAME_TABLES, type GameTable } from "../constants/gameTables"

interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  created_by_username?: string
  created_by_name?: string
  created_by_role?: string
}

interface ActiveGamesProps {
  games: Game[]
}

export function ActiveGames({ games }: ActiveGamesProps) {
  const { t } = useSafeTranslation()
  const [currentTimes, setCurrentTimes] = useState<{ [key: string]: number }>({})
  const [gameTables, setGameTables] = useState<GameTable[]>([])

  // Load game tables from database only once
  useEffect(() => {
    const fetchGameTables = async () => {
      try {
        const response = await fetch('/api/gametables/user', {
          credentials: 'include' // Use cookies for authentication
        })
        if (response.ok) {
          const tables = await response.json()
          setGameTables(tables.map((t: any) => ({
            id: t.id.toString(),
            number: t.number,
            name: t.name,
            isActive: t.is_active,
            hourlyRate: t.hourly_rate
          })))
          console.log('✅ Game tables loaded for ActiveGames component')
        }
      } catch (error) {
        console.warn('Failed to load game tables for ActiveGames:', error)
      }
    }

    // Only fetch if we don't have tables yet
    if (gameTables.length === 0) {
      fetchGameTables()
    }
  }, [gameTables.length])

  useEffect(() => {
    const interval = setInterval(() => {
      const newTimes: { [key: string]: number } = {}
      games.forEach((game) => {
        if (game.status === "active") {
          const elapsed = Math.floor((Date.now() - game.startTime.getTime()) / 1000 / 60)
          newTimes[game.id] = elapsed
        }
      })
      setCurrentTimes(newTimes)
    }, 1000)

    return () => clearInterval(interval)
  }, [games])

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`
  }

  // Calculate cost based on actual time passed (precise minute-based billing)
  const calculateCostFromMinutes = (minutes: number, hourlyRate: number = 400) => {
    // Minimum billing is 1 minute
    const billableMinutes = Math.max(1, minutes)
    // Calculate cost per minute based on hourly rate
    const costPerMinute = hourlyRate / 60
    return Math.round(billableMinutes * costPerMinute)
  }

  // Real-time cost calculation for display (precise)
  const calculateCurrentCost = (game: Game) => {
    // Get the table's hourly rate
    const gameTable = gameTables.find(t => t.number === game.tableNumber)
    const hourlyRate = gameTable?.hourlyRate || 400

    // Calculate precise cost based on actual elapsed time
    const preciseSeconds = (Date.now() - game.startTime.getTime()) / 1000
    const preciseMinutes = preciseSeconds / 60
    return calculateCostFromMinutes(preciseMinutes, hourlyRate)
  }

  const activeGames = games.filter((game) => game.status === "active")
  const completedGames = games.filter((game) => game.status === "completed").slice(0, 2)

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      <div className="border-b border-gray-200 p-3">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
            <Clock className="h-3 w-3 text-green-600" />
          </div>
          <div>
            <div className="text-sm font-bold text-gray-900">{t('activeGames.title')}</div>
            <div className="text-xs text-gray-500">{activeGames.length} {t('activeGames.active')}</div>
          </div>
        </div>
      </div>
      <div className="p-3">
        {activeGames.length === 0 && completedGames.length === 0 ? (
          <div className="text-center py-6">
            <div className="text-sm text-gray-500">{t('activeGames.noGamesYet')}</div>
            <div className="text-xs text-gray-400 mt-1">{t('activeGames.startGameToSeeActivity')}</div>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Active Games Table */}
            {activeGames.map((game) => {
              const currentTime = currentTimes[game.id] || 0
              const currentCost = calculateCurrentCost(game)

              return (
                <div key={game.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-600 rounded text-white text-xs font-bold flex items-center justify-center">
                      {game.tableNumber}
                    </div>
                    <div>
                      <div className="text-sm font-bold text-gray-900">{formatTime(currentTime)}</div>
                      <div className="text-xs text-gray-500">
                        {game.startTime.toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-green-600">{currentCost} L</div>
                    {game.created_by_name && (
                      <div className="text-xs text-gray-500">{game.created_by_name.split(' ')[0]}</div>
                    )}
                  </div>
                </div>
              )
            })}

            {/* Section Divider for Completed Games */}
            {completedGames.length > 0 && activeGames.length > 0 && (
              <div className="border-t border-gray-200 pt-2 mt-2">
                <div className="text-xs text-gray-500 mb-2">{t('activeGames.recentCompleted')}</div>
              </div>
            )}

            {/* Recent Completed Games - Table Format */}
            {completedGames.map((game) => (
              <div key={game.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center gap-2">
                  <div className="min-w-6 h-6 px-1 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center whitespace-nowrap" title={gameTables.find(t => t.number === game.tableNumber)?.name || `Table ${game.tableNumber}`}>
                    {gameTables.find(t => t.number === game.tableNumber)?.name || `Table ${game.tableNumber}`}
                  </div>
                  <div>
                    <div className="text-sm font-bold text-gray-900">{formatTime(game.duration)}</div>
                    <div className="text-xs text-gray-500">
                      {game.endTime ? new Date(game.endTime).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      }) : t('activeGames.recently')}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <div className="text-sm font-bold text-green-600">{game.cost} L</div>
                    {game.created_by_name && (
                      <div className="text-xs text-gray-500">{game.created_by_name.split(' ')[0]}</div>
                    )}
                  </div>
                  <ReceiptPrinter
                    type="game"
                    data={game}
                    onPrint={() => console.log(`Printed receipt for game ${game.id}`)}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
