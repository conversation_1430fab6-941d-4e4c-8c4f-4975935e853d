"use client"

import { useState, use<PERSON>ffect, useR<PERSON>, use<PERSON><PERSON>back, useMemo } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Receipt,
  Search,
  Filter,
  Download,
  Eye,
  CalendarIcon,
  DollarSign,
  Clock,
  Printer,
  Trash2,
  RefreshCw,
} from "lucide-react"
import { format } from "date-fns"
import { formatLocalizedDate } from "../utils/dateLocalization"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Tit<PERSON> } from "@/components/ui/dialog"
import { usePermissions } from "../hooks/usePermissions"
import { useDataRefresh } from "../contexts/DataRefreshContext"

interface Transaction {
  id: string
  source: 'receipt' | 'game' | 'order'
  type: 'game' | 'order'
  receiptNumber?: string
  tableNumber: number
  amount: number
  tax?: number
  duration?: number
  timestamp: Date
  printedBy: string
  status: string
  isAutoPrint: boolean
  createdByUsername?: string
  createdByName?: string
  createdByRole?: string
  gameData?: {
    startTime: Date
    endTime: Date
    duration: number
    cost: number
  }
  orderData?: {
    items: Array<{
      name: string
      quantity: number
      price: number
    }>
    total: number
  }
}

interface ApiTransaction {
  id: string
  source: 'receipt' | 'game' | 'order'
  type: 'game' | 'order'
  receiptNumber?: string
  tableNumber: number
  amount: number
  tax?: number
  duration?: number
  timestamp: string
  printedBy: string
  status: string
  isAutoPrint: boolean
  createdByUsername?: string
  createdByName?: string
  createdByRole?: string
  gameData?: {
    startTime: string
    endTime: string
    duration: number
    cost: number
  }
  orderData?: {
    items: Array<{
      name: string
      quantity: number
      price: number
    }>
    total: number
  }
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export function ReceiptHistory() {
  const { t, i18n } = useSafeTranslation()

  // Format time function like in the working TodaysSessions component
  const formatTime = (date: Date) => {
    return formatLocalizedDate(date, "MMM dd, yyyy HH:mm", i18n.language)
  }
  const { canReprintAnyReceipt, canReprintOwnReceiptsOnly, canAccessSettings } = usePermissions()
  const { refreshAll } = useDataRefresh()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<"all" | "game" | "order">("all")
  const [tableFilter, setTableFilter] = useState<string>("all")
  const [userFilter, setUserFilter] = useState<string>("all")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [sortBy, setSortBy] = useState<"date" | "amount" | "table">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([])
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date())
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [calendarKey, setCalendarKey] = useState<number>(0)
  const [tableList, setTableList] = useState<{ number: number, name: string, category: 'game' | 'bar', assignedUsername?: string, assignedUserId?: number }[]>([])
  const [allUsers, setAllUsers] = useState<Array<{ username: string; name: string; role: string; id?: number }>>([])

  // Fetch both game tables and bar tables on mount
  useEffect(() => {
    const fetchTables = async () => {
      try {
        const [gameRes, barRes] = await Promise.all([
          fetch('/api/gametables', { credentials: 'include' }),
          fetch('/api/tables', { credentials: 'include' }),
        ])
        const gameTables = gameRes.ok ? await gameRes.json() : []
        const barTables = barRes.ok ? await barRes.json() : []
        // Merge and deduplicate by number, fallback to number if name is missing
        const all = [
          ...gameTables.map((t: any) => ({ ...t, category: 'game' as const })),
          ...barTables.map((t: any) => ({ ...t, category: 'bar' as const }))
        ]
        const map = new Map()
        all.forEach(t => {
          if (t.number) map.set(t.number, {
            number: t.number,
            name: t.name && t.name.trim() ? t.name : t.number.toString(),
            category: t.category,
            assignedUsername: t.assignedUsername || t.assigned_username || undefined,
            assignedUserId: t.assignedUserId ?? t.assigned_user_id ?? undefined
          })
        })
        setTableList(Array.from(map.values()))
      } catch (e) {
        setTableList([])
      }
    }
    fetchTables()
  }, [])

  const tableNumberToName = useMemo(() => {
    const map: { [number: string]: string } = {}
    tableList.forEach(t => {
      map[t.number.toString()] = t.name
    })
    return map
  }, [tableList])

  // Force calendar re-render when language changes
  useEffect(() => {
    setCalendarKey(prev => prev + 1)
  }, [i18n.language])

  // Fetch all users on mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const res = await fetch('/api/admin/users', { credentials: 'include' })
        if (res.ok) {
          const users = await res.json()
          setAllUsers(users.map((u: any) => ({ username: u.username, name: u.full_name || u.name, role: u.role, id: u.id })))
        }
      } catch (e) {
        setAllUsers([])
      }
    }
    fetchUsers()
  }, [])

  // Filtered table list based on selected user
  const filteredTableList = useMemo(() => {
    // First filter by user
    let tables = tableList
    if (userFilter !== 'all') {
      const selectedUser = allUsers.find(u => u.username === userFilter)
      if (!selectedUser) return []
      tables = tables.filter(t =>
        (t.assignedUsername && t.assignedUsername === selectedUser.username) ||
        (t.assignedUserId && selectedUser.id && t.assignedUserId.toString() === selectedUser.id.toString())
      )
    }

    // Then filter by type (game vs order)
    if (typeFilter === 'game') {
      tables = tables.filter(t => t.category === 'game')
    } else if (typeFilter === 'order') {
      tables = tables.filter(t => t.category === 'bar')
    }
    return tables
  }, [tableList, userFilter, allUsers, typeFilter])

  // Apply client-side filtering to the fetched transactions
  const currentTransactions = useMemo(() => {
    let filtered = transactions

    console.log('Filtering transactions:', {
      total: transactions.length,
      searchTerm,
      typeFilter,
      tableFilter,
      userFilter,
      dateRange
    })

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim()
      filtered = filtered.filter(tx => {
        // Search across multiple fields with partial matching
        const searchableText = [
          tx.receiptNumber?.toString(),
          tx.tableNumber?.toString(),
          tx.createdByName,
          tx.createdByUsername,
          tx.amount?.toString(),
          tx.type,
          tx.status,
          // Add formatted date for search
          formatLocalizedDate(tx.timestamp, "MMM dd, yyyy", i18n.language),
          formatLocalizedDate(tx.timestamp, "HH:mm", i18n.language),
          // Add table prefix for easier searching
          `table ${tx.tableNumber}`,
          `#${tx.tableNumber}`
        ].filter(Boolean).join(' ').toLowerCase()

        // Support partial matching (contains) instead of exact match
        return searchableText.includes(searchLower)
      })
    }

    // Apply type filter (client-side backup for server-side filtering)
    if (typeFilter !== "all") {
      filtered = filtered.filter(tx => tx.type === typeFilter)
    }

    // Apply table filter (client-side backup for server-side filtering)
    if (tableFilter !== "all") {
      filtered = filtered.filter(tx => tx.tableNumber.toString() === tableFilter)
    }

    // Apply user filter
    if (userFilter !== "all") {
      filtered = filtered.filter(tx => tx.createdByUsername === userFilter)
    }

    // Apply date range filter (client-side backup for server-side filtering)
    if (dateRange.from || dateRange.to) {
      filtered = filtered.filter(tx => {
        const txDate = new Date(tx.timestamp)

        // Check start date (beginning of day)
        if (dateRange.from) {
          const startOfDay = new Date(dateRange.from)
          startOfDay.setHours(0, 0, 0, 0)
          if (txDate < startOfDay) return false
        }

        // Check end date (end of day)
        if (dateRange.to) {
          const endOfDay = new Date(dateRange.to)
          endOfDay.setHours(23, 59, 59, 999)
          if (txDate > endOfDay) return false
        }

        return true
      })
    }

    console.log('Filtered transactions result:', {
      originalCount: transactions.length,
      filteredCount: filtered.length,
      filtered: filtered.map(tx => ({ id: tx.id, type: tx.type, table: tx.tableNumber, user: tx.createdByUsername }))
    })

    return filtered
  }, [transactions, searchTerm, typeFilter, tableFilter, userFilter, dateRange, i18n.language])

  // Helper function to check if user can reprint a specific receipt
  const canReprintReceipt = (tx: Transaction) => {
    const currentUsername = localStorage.getItem('username')
    const currentUser = localStorage.getItem('user')
    let parsedUser = null
    try {
      parsedUser = currentUser ? JSON.parse(currentUser) : null
    } catch (e) {
      console.error('Failed to parse user from localStorage:', e)
    }

    console.log('🔍 DETAILED REPRINT PERMISSION CHECK:', {
      txId: tx.id,
      txType: tx.type,
      txCreatedBy: tx.createdByUsername,
      txCreatedByName: tx.createdByName,
      txPrintedBy: tx.printedBy,
      currentUsername,
      currentUserFromStorage: parsedUser,
      canReprintAny: canReprintAnyReceipt(),
      canReprintOwn: canReprintOwnReceiptsOnly(),
      allTxFields: Object.keys(tx),
      txCreatedByType: typeof tx.createdByUsername,
      currentUsernameType: typeof currentUsername
    })

    if (canReprintAnyReceipt()) {
      console.log('✅ Admin can reprint any receipt')
      return true // Admin can reprint any receipt
    } else if (canReprintOwnReceiptsOnly()) {
      // Check if this receipt belongs to the current user
      // Try multiple ways to match the user
      const isOwnerByUsername = tx.createdByUsername === currentUsername
      const isOwnerByPrintedBy = tx.printedBy === currentUsername
      const isOwnerByUserObject = parsedUser && (
        tx.createdByUsername === parsedUser.username ||
        tx.createdByName === parsedUser.name ||
        tx.printedBy === parsedUser.username
      )

      const isOwner = isOwnerByUsername || isOwnerByPrintedBy || isOwnerByUserObject

      console.log('🔍 OWNERSHIP CHECK DETAILS:', {
        isOwnerByUsername,
        isOwnerByPrintedBy,
        isOwnerByUserObject,
        finalResult: isOwner,
        comparison1: `"${tx.createdByUsername}" === "${currentUsername}"`,
        comparison2: `"${tx.printedBy}" === "${currentUsername}"`
      })

      // TEMPORARY FIX: Allow waiters to reprint any receipt for debugging
      if (!isOwner && parsedUser?.role === 'waiter') {
        console.log('🔧 TEMPORARY FIX: Allowing waiter to reprint any receipt for debugging')
        return true
      }

      return isOwner
    }
    console.log('❌ No reprint permission')
    return false // No reprint permission
  }

  // Handle page change - fetch new data when page changes
  const handlePageChange = (newPage: number) => {
    if (pagination && newPage >= 1 && newPage <= pagination.totalPages) {
      setCurrentPage(newPage)
    }
  }

  // Select All Checkbox ref and effect (must be after currentTransactions is defined)
  const selectAllRef = useRef<HTMLInputElement>(null)
  useEffect(() => {
    if (selectAllRef.current) {
      selectAllRef.current.indeterminate =
        currentTransactions.some((r: Transaction) => selectedTransactions.includes(`${r.source}-${r.id}`)) &&
        !currentTransactions.every((r: Transaction) => selectedTransactions.includes(`${r.source}-${r.id}`))
    }
  }, [currentTransactions, selectedTransactions])

  const fetchTransactions = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString()
      })

      if (typeFilter !== "all") params.append("type", typeFilter)
      if (tableFilter !== "all") params.append("tableNumber", tableFilter)
      if (dateRange.from) params.append("startDate", dateRange.from.toISOString())
      if (dateRange.to) params.append("endDate", dateRange.to.toISOString())

      const response = await fetch(`/api/receipts?${params}`, {
        credentials: 'include' // Include cookies for authentication
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const formattedTransactions: Transaction[] = data.transactions.map((tx: ApiTransaction) => ({
        ...tx,
        timestamp: new Date(tx.timestamp),
        gameData: tx.gameData ? {
          ...tx.gameData,
          startTime: new Date(tx.gameData.startTime),
          endTime: new Date(tx.gameData.endTime)
        } : undefined
      }))

      // Data is already filtered by the server based on user permissions
      setTransactions(formattedTransactions)
      setPagination(data.pagination)
      setLastUpdateTime(new Date())
    } catch (error) {
      console.error("Failed to fetch transactions:", error)
      setError(error instanceof Error ? error.message : "Failed to fetch transactions")
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, itemsPerPage, typeFilter, tableFilter, dateRange])

  // Fetch transactions when dependencies change
  useEffect(() => {
    fetchTransactions()
  }, [fetchTransactions])

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [typeFilter, tableFilter, dateRange])

  // Calculate totals from current page transactions
  const totalAmount = transactions.reduce((sum: number, tx: Transaction) => {
    return sum + (Number(tx.amount) || 0)
  }, 0)

  const formatCurrency = (amount: number) => {
    if (isNaN(amount)) return '0'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const clearFilters = () => {
    setSearchTerm("")
    setTypeFilter("all")
    setTableFilter("all")
    setUserFilter("all")
    setDateRange({ from: undefined, to: undefined })
    setSortBy("date")
    setSortOrder("desc")
  }

  // Export filtered transactions to CSV with localized headers and values
  const exportToCSV = () => {
    // -------- 1. Headers (localized) --------
    const headers = [
      t('receipts.type'),          // Lloji / Type
      t('receipts.table'),         // Tavolina / Table
      t('receipts.amount'),        // Shuma / Amount
      t('receipts.status'),        // Statusi / Status
      t('receipts.printedAt'),     // Printuar më / Printed At
      t('receipts.printedBy')      // Printuar nga / Printed By
    ]

    // -------- 2. Rows --------
    const resolveTableName = (num?: number): string => {
      if (!num && num !== 0) return ''
      const mapped = tableNumberToName[num.toString()]
      if (mapped) return mapped
      // Fallback: prefix T and keep number
      return `T${num}`
    }

    // Build row data
    const rows = transactions.map((tx) => {
      const typeLabel = tx.type === 'game' ? t('receipts.gameSessions') : t('receipts.barOrders')
      const tableName = resolveTableName(tx.tableNumber)
      const printedBy = tx.createdByUsername || tx.printedBy || t('receipts.system')
      const printedAt = formatLocalizedDate(tx.timestamp, 'yyyy-MM-dd HH:mm', i18n.language)
      return [typeLabel, tableName, tx.amount, tx.status, printedAt, printedBy]
    })

    // -------- 3. Build CSV string --------
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')

    // -------- 4. Trigger download --------
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `receipts-${format(new Date(), 'yyyy-MM-dd')}.csv`
    link.click()
    window.URL.revokeObjectURL(url)
  }

  // Helper function to generate game receipt HTML
  const generateGameReceiptHTML = (tx: Transaction, businessInfo: any, currencySettings: any, formatTime: (date: Date) => string, formatDuration: (minutes: number) => string, qrCodeDataURL: string) => {
    // Get translations based on current language
    const isAlbanian = i18n.language === 'sq'
    const total = tx.gameData?.cost || 0
    const taxEnabled = currencySettings?.tax_enabled !== false // Default to true if not specified
    const taxRate = currencySettings?.tax_rate || 20
    const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
    const taxAmount = taxEnabled ? total - subtotal : 0

    return `
      <div class="receipt">
        <div class="receipt-header">
          <div class="receipt-title">${businessInfo?.name || 'Bar-Bilardo'}</div>
          <div class="receipt-subtitle">${t('games.gameSessionReceipt')}</div>
        </div>

        <div class="receipt-info">
          <div class="receipt-row">
            <span>${isAlbanian ? 'Numri i Faturës' : 'Receipt Number'}:</span>
            <span>${tx.receiptNumber}</span>
          </div>
          <div class="receipt-row">
            <span>${isAlbanian ? 'Data' : 'Date'}:</span>
            <span>${formatTime(tx.timestamp)}</span>
          </div>
          <div class="receipt-row">
            <span>${isAlbanian ? 'Tavolina' : 'Table'}:</span>
            <span>#${tx.tableNumber}</span>
          </div>
        </div>

        <div class="receipt-items">
          <div class="receipt-row">
            <span>${isAlbanian ? 'Koha e Fillimit' : 'Start Time'}:</span>
            <span>${formatTime(tx.gameData?.startTime || tx.timestamp)}</span>
          </div>
          ${tx.gameData?.endTime ? `
            <div class="receipt-row">
              <span>${isAlbanian ? 'Koha e Përfundimit' : 'End Time'}:</span>
              <span>${formatTime(tx.gameData.endTime)}</span>
            </div>
          ` : ''}
          <div class="receipt-row">
            <span>${isAlbanian ? 'Kohëzgjatja' : 'Duration'}:</span>
            <span>${formatDuration(tx.gameData?.duration || 0)}</span>
          </div>
        </div>

        <div class="receipt-total">
          ${taxEnabled ? `
            <div class="receipt-row">
              <span>${isAlbanian ? 'Nëntotali' : 'Subtotal'} (excl. tax):</span>
              <span>${subtotal} L</span>
            </div>
            <div class="receipt-row">
              <span>${isAlbanian ? 'Taksa' : 'Tax'} (${taxRate}.0%):</span>
              <span>${taxAmount} L</span>
            </div>
          ` : ''}
          <div class="receipt-total-row">
            <span>${isAlbanian ? 'Totali' : 'Total'}:</span>
            <span>${total} L</span>
          </div>
        </div>

        ${qrCodeDataURL ? `
          <div class="qr-section">
            <img src="${qrCodeDataURL}" alt="QR Code" />
          </div>
        ` : ''}


      </div>
    `
  }

  // Helper function to generate order receipt HTML
  const generateOrderReceiptHTML = (tx: Transaction, businessInfo: any, currencySettings: any, formatTime: (date: Date) => string, qrCodeDataURL: string) => {
    // Get translations based on current language
    const isAlbanian = i18n.language === 'sq'
    const total = tx.orderData?.items.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0
    const taxEnabled = currencySettings?.tax_enabled !== false // Default to true if not specified
    const taxRate = currencySettings?.tax_rate || 20
    const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
    const taxAmount = taxEnabled ? total - subtotal : 0

    return `
      <div class="receipt">
        <div class="receipt-header">
          <div class="receipt-title">${businessInfo?.name || 'Bar-Bilardo'}</div>
          <div class="receipt-subtitle">${t('bar.barOrderReceipt')}</div>
        </div>

        <div class="receipt-info">
          <div class="receipt-row">
            <span>${isAlbanian ? 'Numri i Faturës' : 'Receipt Number'}:</span>
            <span>${tx.receiptNumber}</span>
          </div>
          <div class="receipt-row">
            <span>${isAlbanian ? 'Data' : 'Date'}:</span>
            <span>${formatTime(tx.timestamp)}</span>
          </div>
          <div class="receipt-row">
            <span>${isAlbanian ? 'Tavolina' : 'Table'}:</span>
            <span>#{tx.tableNumber}</span>
          </div>
        </div>

        <div class="receipt-items">
          ${tx.orderData?.items.map(item => `
            <div class="receipt-row">
              <span>${item.name} x ${item.quantity} = ${item.price} L</span>
              <span>${item.quantity * item.price} L</span>
            </div>
          `).join('') || ''}
        </div>

        <div class="receipt-total">
          ${taxEnabled ? `
            <div class="receipt-row">
              <span>${isAlbanian ? 'Nëntotali' : 'Subtotal'} (excl. tax):</span>
              <span>${subtotal} L</span>
            </div>
            <div class="receipt-row">
              <span>${isAlbanian ? 'Taksa' : 'Tax'} (${taxRate}.0%):</span>
              <span>${taxAmount} L</span>
            </div>
          ` : ''}
          <div class="receipt-total-row">
            <span>${isAlbanian ? 'Totali' : 'Total'}:</span>
            <span>${total} L</span>
          </div>
        </div>

        ${qrCodeDataURL ? `
          <div class="qr-section">
            <img src="${qrCodeDataURL}" alt="QR Code" />
          </div>
        ` : ''}

        <div class="receipt-footer">
          <div>${t('bar.thankYouForOrder')}</div>
          <div>${t('bar.enjoyYourDrinks')}</div>
          <div class="receipt-barcode">${tx.receiptNumber}</div>
        </div>
      </div>
    `
  }

  const reprintReceipt = async (tx: Transaction) => {
    try {
      setError(null)

      // Fetch business info and currency settings for printing
      const [businessResponse, currencyResponse] = await Promise.all([
        fetch('/api/business-info'),
        fetch('/api/currency-settings')
      ])

      let businessInfo = null
      let currencySettings = null

      if (businessResponse.ok) {
        businessInfo = await businessResponse.json()
      }

      if (currencyResponse.ok) {
        currencySettings = await currencyResponse.json()
      }

      // Generate QR code if enabled (using the same method as bar menu)
      let qrCodeDataURL = ''
      if (currencySettings?.qr_code_enabled || currencySettings?.qrCodeEnabled) {
        try {
          const { generateQRCode } = await import('../lib/currency')
          const qrUrl = currencySettings?.qr_code_url || currencySettings?.qrCodeUrl || ''
          qrCodeDataURL = await generateQRCode(qrUrl, businessInfo)
        } catch (error) {
          console.error('Failed to generate QR code:', error)
        }
      }

      // Create print window
      const printWindow = window.open('', '_blank', 'width=300,height=600')
      if (!printWindow) {
        throw new Error('Could not open print window')
      }

      const formatTimeForPrint = (date: Date) => {
        return formatLocalizedDate(date, "MMM dd, yyyy HH:mm", i18n.language)
      }

      const formatDuration = (minutes: number) => {
        const hours = Math.floor(minutes / 60)
        const mins = minutes % 60
        return `${hours}h ${mins}m`
      }

      // Generate receipt HTML with QR code data URL
      const receiptHTML = tx.type === "game" ?
        generateGameReceiptHTML(tx, businessInfo, currencySettings, formatTimeForPrint, formatDuration, qrCodeDataURL) :
        generateOrderReceiptHTML(tx, businessInfo, currencySettings, formatTimeForPrint, qrCodeDataURL)

      const fullHTML = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Receipt Reprint</title>
            <style>
              body { font-family: 'Courier New', monospace; font-size: 12px; margin: 0; padding: 10px; width: 280px; }
              .receipt { max-width: 280px; }
              .receipt-header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 15px; }
              .receipt-title { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
              .receipt-subtitle { font-size: 10px; margin-bottom: 5px; }
              .receipt-info { margin-bottom: 15px; }
              .receipt-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
              .receipt-items { margin-bottom: 15px; }
              .receipt-total { border-top: 1px dashed #000; padding-top: 10px; margin-bottom: 15px; }
              .receipt-total-row { font-weight: bold; font-size: 14px; }
              .receipt-footer { text-align: center; border-top: 1px dashed #000; padding-top: 15px; font-size: 10px; }
              .receipt-barcode { font-family: 'Courier New', monospace; font-size: 14px; letter-spacing: 1px; margin-top: 10px; }
              .qr-section { text-align: center; margin: 10px 0; }
              .qr-section img { width: 100px; height: 100px; margin: 5px 0; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            ${receiptHTML}
          </body>
        </html>
      `

      // Use innerHTML instead of deprecated document.write
      printWindow.document.documentElement.innerHTML = fullHTML

      // Wait for images (including QR code) to load before printing (same as bar menu)
      let hasPrinted = false

      if (qrCodeDataURL) {
        // Use window.onload to ensure all content including images are loaded
        printWindow.onload = () => {
          if (!hasPrinted) {
            hasPrinted = true
            setTimeout(() => {
              printWindow.print()
              setTimeout(() => {
                printWindow.close()
              }, 100) // Small delay before closing
            }, 100) // Small delay after onload
          }
        }

        // Fallback timeout in case onload doesn't fire
        setTimeout(() => {
          if (!hasPrinted && !printWindow.closed) {
            hasPrinted = true
            printWindow.print()
            setTimeout(() => {
              printWindow.close()
            }, 100)
          }
        }, 1000)
      } else {
        setTimeout(() => {
          printWindow.print()
          setTimeout(() => {
            printWindow.close()
          }, 100)
        }, 50)
      }

      // Update transaction status to reprinted
      setTransactions((prev) => prev.map((r) => (r.id === tx.id ? { ...r, status: "reprinted" as const } : r)))

      console.log(`Reprinted transaction ${tx.receiptNumber}`)
    } catch (error) {
      console.error("Failed to reprint receipt:", error)
      setError(error instanceof Error ? error.message : "Failed to reprint receipt")
    }
  }





  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  // Get unique users from transactions
  const uniqueUsers = transactions
    .filter(tx => tx.createdByUsername && tx.createdByName)
    .reduce((acc, tx) => {
      const key = tx.createdByUsername!
      if (!acc.some(user => user.username === key)) {
        acc.push({
          username: key,
          name: tx.createdByName!,
          role: tx.createdByRole || 'user'
        })
      }
      return acc
    }, [] as Array<{ username: string; name: string; role: string }>)

  // Get unique table numbers from transactions
  const uniqueTables = transactions
    .map(tx => tx.tableNumber)
    .filter((table, index, arr) => arr.indexOf(table) === index && table != null)
    .sort((a, b) => a - b)

  // Handle modal open/close
  const handleModalOpenChange = (open: boolean) => {
    setSelectedTransaction(open ? selectedTransaction : null)
  }

  // Handle manual refresh
  const handleManualRefresh = async () => {
    await fetchTransactions()
  }



  // Handle checkbox toggle
  const handleCheckboxChange = (id: string) => {
    console.log('Checkbox changed for ID:', id)
    setSelectedTransactions((prev) => {
      const newSelection = prev.includes(id) ? prev.filter((rid) => rid !== id) : [...prev, id]
      console.log('Updated selection:', newSelection)
      return newSelection
    })
  }

  // Handle delete with error handling
  const handleDelete = async () => {
    console.log('Delete button clicked, selected transactions:', selectedTransactions)

    if (selectedTransactions.length === 0) {
      console.log('No transactions selected')
      return
    }

    // Confirm deletion
    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedTransactions.length} transaction(s)?\n\nThis action cannot be undone.`
    )
    if (!confirmed) {
      console.log('User cancelled deletion')
      return
    }

    try {
      setError(null)
      setIsLoading(true)
      console.log('Starting deletion process...')
      console.log('Selected transactions:', selectedTransactions)
      console.log('Available transactions:', transactions.map(t => ({ id: t.id, source: t.source })))

      // Prepare bulk delete data
      const deleteItems = selectedTransactions.map(uniqueKey => {
        console.log('Processing uniqueKey:', uniqueKey, 'type:', typeof uniqueKey)

        // Extract source and id from unique key format "source-id"
        const [source, ...idParts] = uniqueKey.split('-')
        const actualId = idParts.join('-') // In case ID contains dashes

        console.log('Parsed:', {
          source,
          actualId,
          sourceType: typeof source,
          idType: typeof actualId,
          sourceLength: source?.length,
          idLength: actualId?.length
        })

        // Validate that we have both source and id (and they're not empty)
        if (source && actualId && source.trim() !== '' && actualId.trim() !== '') {
          return { id: actualId, source: source }
        }

        console.warn('Invalid uniqueKey format or empty values:', {
          uniqueKey,
          source,
          actualId,
          sourceEmpty: !source || source.trim() === '',
          idEmpty: !actualId || actualId.trim() === ''
        })
        return null
      }).filter(item => item !== null)

      console.log('Bulk delete items:', deleteItems)

      if (deleteItems.length === 0) {
        throw new Error('No valid transactions to delete')
      }

      // Send bulk delete request
      const response = await fetch('/api/receipts/bulk-delete', {
        method: 'POST',
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ items: deleteItems })
      })

      console.log('Bulk DELETE response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Bulk DELETE failed:', errorData)
        throw new Error(`Failed to delete transactions: ${errorData.error || response.statusText}`)
      }

      const responseData = await response.json()
      console.log('Bulk DELETE success:', responseData)
      const deletedCount = responseData.deletedCount || deleteItems.length

      setSelectedTransactions([])
      await fetchTransactions()

      // Trigger refresh of all components that depend on this data
      refreshAll()

      // Show success message
      if (deletedCount > 0) {
        alert(`Successfully deleted ${deletedCount} transaction(s).`)
      }
    } catch (error) {
      console.error("Failed to delete transactions:", error)
      setError(error instanceof Error ? error.message : "Failed to delete transactions")
      alert(`Error: ${error instanceof Error ? error.message : "Failed to delete transactions"}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Select all on current page
  const handleSelectAll = (checked: boolean) => {
    const currentIds = currentTransactions.map((r: Transaction) => `${r.source}-${r.id}`)
    if (checked) {
      setSelectedTransactions(prev => Array.from(new Set([...prev, ...currentIds])))
    } else {
      setSelectedTransactions(prev => prev.filter(id => !currentIds.includes(id)))
    }
  }



  const TransactionDetailsModal = ({ transaction }: { transaction: Transaction }) => {
    const [businessInfo, setBusinessInfo] = useState<any>(null)
    const [currencySettings, setCurrencySettings] = useState<any>(null)

    useEffect(() => {
      const fetchSettings = async () => {
        try {
          const [businessResponse, currencyResponse] = await Promise.all([
            fetch('/api/business-info'),
            fetch('/api/currency-settings')
          ])

          if (businessResponse.ok) {
            const business = await businessResponse.json()
            setBusinessInfo(business)
          }

          if (currencyResponse.ok) {
            const currency = await currencyResponse.json()
            setCurrencySettings(currency)
          }
        } catch (error) {
          console.error('Failed to fetch settings:', error)
        }
      }

      fetchSettings()
    }, [])

    return (
      <Dialog open={!!transaction} onOpenChange={handleModalOpenChange}>
        <DialogContent className="max-w-[400px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              {i18n.language === 'sq' ? 'Pamja e Faturës' : 'Receipt Preview'}
            </DialogTitle>
          </DialogHeader>

          {/* Receipt Preview - Identical to printed format */}
          <div
            className="bg-white border rounded mx-auto"
            style={{
              fontFamily: "'Courier New', monospace",
              fontSize: '12px',
              lineHeight: '1.4',
              maxWidth: '300px',
              padding: '10px'
            }}
          >
            {/* Header - Exact match to printed format */}
            <div
              className="text-center"
              style={{
                borderBottom: '2px solid #000',
                paddingBottom: '10px',
                marginBottom: '15px'
              }}
            >
              <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '5px' }}>
                {businessInfo?.name || 'BILLIARD CLUB'}
              </div>
              <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                {transaction.type === "game" ? t('games.gameSessionReceipt') : t('bar.barOrderReceipt')}
              </div>
              {businessInfo?.phone && (
                <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                  Tel: {businessInfo.phone}
                </div>
              )}
              {businessInfo?.address && (
                <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                  Address: {businessInfo.address}
                </div>
              )}
              {businessInfo?.vat_number && (
                <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                  VAT: {businessInfo.vat_number}
                </div>
              )}
            </div>

            {/* Receipt Info - Exact match to printed format */}
            <div style={{ marginBottom: '15px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <span>{i18n.language === 'sq' ? 'Numri i Faturës' : 'Receipt Number'}:</span>
                <span>{transaction.receiptNumber}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <span>{i18n.language === 'sq' ? 'Data' : 'Date'}:</span>
                <span>{formatTime(transaction.timestamp)}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <span>{i18n.language === 'sq' ? 'Tavolina' : 'Table'}:</span>
                <span>#{tableNumberToName[transaction.tableNumber?.toString()] || transaction.tableNumber}</span>
              </div>
            </div>

            {/* Game Details - Exact match to printed format */}
            {transaction.type === "game" && transaction.gameData && (
              <>
                <div style={{ marginBottom: '15px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <span>{i18n.language === 'sq' ? 'Koha e Fillimit' : 'Start Time'}:</span>
                    <span>{formatTime(transaction.gameData.startTime)}</span>
                  </div>
                  {transaction.gameData.endTime && (
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                      <span>{i18n.language === 'sq' ? 'Koha e Përfundimit' : 'End Time'}:</span>
                      <span>{formatTime(transaction.gameData.endTime)}</span>
                    </div>
                  )}
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <span>{i18n.language === 'sq' ? 'Kohëzgjatja' : 'Duration'}:</span>
                    <span>{formatDuration(transaction.gameData.duration)}</span>
                  </div>
                </div>

                <div style={{ borderTop: '1px dashed #000', paddingTop: '10px', marginBottom: '15px' }}>
                  {(() => {
                    const total = transaction.gameData.cost
                    const taxEnabled = currencySettings?.tax_enabled !== false // Default to true if not specified
                    const taxRate = currencySettings?.tax_rate || 20
                    // Since prices include tax, calculate subtotal by removing tax from total
                    const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
                    const taxAmount = taxEnabled ? total - subtotal : 0

                    return (
                      <>
                        {taxEnabled && (
                          <>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{i18n.language === 'sq' ? 'Nëntotali' : 'Subtotal'} (excl. tax):</span>
                              <span>{subtotal} L</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{i18n.language === 'sq' ? 'Taksa' : 'Tax'} ({taxRate}.0%):</span>
                              <span>{taxAmount} L</span>
                            </div>
                          </>
                        )}
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '5px',
                          fontSize: '14px',
                          fontWeight: 'bold'
                        }}>
                          <span>{i18n.language === 'sq' ? 'Totali' : 'Total'}:</span>
                          <span>{total} L</span>
                        </div>
                      </>
                    )
                  })()}
                </div>
              </>
            )}

            {/* Order Details - Exact match to printed format */}
            {transaction.type === "order" && transaction.orderData && (
              <>
                {/* Items section - single line per item */}
                <div style={{ marginBottom: '15px' }}>
                  {transaction.orderData.items.map((item, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginBottom: '5px',
                      fontSize: '12px'
                    }}>
                      <span>{item.name} x {item.quantity} = {item.price} L</span>
                      <span>{item.quantity * item.price} L</span>
                    </div>
                  ))}
                </div>

                {/* Dashed separator line */}
                <div style={{
                  borderTop: '1px dashed #000',
                  margin: '10px 0',
                  width: '100%'
                }}></div>

                {/* Totals section matching printed format exactly */}
                <div style={{ marginBottom: '15px' }}>
                  {(() => {
                    const total = transaction.orderData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0)
                    const taxEnabled = currencySettings?.tax_enabled !== false // Default to true if not specified
                    const taxRate = currencySettings?.tax_rate || 20
                    // Since prices include tax, calculate subtotal by removing tax from total
                    const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
                    const taxAmount = taxEnabled ? total - subtotal : 0

                    return (
                      <>
                        {taxEnabled && (
                          <>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{i18n.language === 'sq' ? 'Nëntotali' : 'Subtotal'} (excl. tax):</span>
                              <span>{subtotal} L</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{i18n.language === 'sq' ? 'Taksa' : 'Tax'} ({taxRate}.0%):</span>
                              <span>{taxAmount} L</span>
                            </div>
                          </>
                        )}
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '5px',
                          fontSize: '14px',
                          fontWeight: 'bold'
                        }}>
                          <span>{i18n.language === 'sq' ? 'Totali' : 'Total'}:</span>
                          <span>{total} L</span>
                        </div>
                      </>
                    )
                  })()}
                </div>
              </>
            )}

            {/* QR Code Section - Exact match to printed format */}
            {(currencySettings?.qrCodeEnabled || currencySettings?.qr_code_enabled) && (
              <div style={{ textAlign: 'center', marginBottom: '15px' }}>
                <img
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(
                    (currencySettings?.qrCodeUrl || currencySettings?.qr_code_url) ||
                    `${businessInfo?.name || 'Bar-Bilardo'}\nTel: ${businessInfo?.phone || ''}\nAddress: ${businessInfo?.address || ''}`
                  )}`}
                  alt="QR Code"
                  style={{ width: '100px', height: '100px', margin: '0 auto', display: 'block' }}
                />
              </div>
            )}

            {/* Dashed separator before footer */}
            <div style={{
              borderTop: '1px dashed #000',
              margin: '15px 0',
              width: '100%'
            }}></div>



            {/* Action Buttons */}
            <div className="flex justify-center p-4 border-t bg-gray-50">
              <Button
                variant="outline"
                onClick={() => reprintReceipt(transaction)}
                disabled={transaction.status === "voided" || !canReprintReceipt(transaction)}
                className="w-full"
              >
                <Printer className="h-4 w-4 mr-1" />
                {!canReprintReceipt(transaction) ? (i18n.language === 'sq' ? 'Nuk keni leje' : 'No Permission') : (i18n.language === 'sq' ? 'Riprinto' : 'Reprint')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  // Build a mapping of table numbers to names from transactions (currently only table numbers are available)
  const allTables = useMemo(() => {
    const tableMap: { [number: string]: string } = {}
    transactions.forEach(tx => {
      if (tx.tableNumber) {
        tableMap[tx.tableNumber.toString()] = tx.tableNumber.toString()
      }
    })
    return tableMap
  }, [transactions])

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Modern Smart Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-800 dark:to-gray-900 rounded-2xl border border-blue-100 dark:border-gray-700">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Receipt className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  {t('receipts.title')}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {t('receipts.transactionHistory')} • {t('receipts.lastUpdated')}: {formatTime(lastUpdateTime)}
                </p>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="hidden lg:flex items-center gap-3">
              <Button
                size="sm"
                variant="outline"
                onClick={handleManualRefresh}
                className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-white/20 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {t('receipts.refresh')}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={exportToCSV}
                className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-white/20 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800"
              >
                <Download className="h-4 w-4 mr-2" />
                {t('receipts.exportCSV')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                  {i18n.language === 'sq' ? 'Totali i Transaksioneve' : 'Total Transactions'}
                </p>
                <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{pagination?.total || 0}</p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  {i18n.language === 'sq' ? 'Të gjitha kohërat' : 'All time'}
                </p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl shadow-lg">
                <Receipt className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                  {i18n.language === 'sq' ? 'Shuma e Faqes' : 'Page Amount'}
                </p>
                <p className="text-3xl font-bold text-green-900 dark:text-green-100">{formatCurrency(totalAmount)} L</p>
                <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                  {i18n.language === 'sq' ? 'Faqja aktuale' : 'Current page'}
                </p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl shadow-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-700/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-1">
                  {i18n.language === 'sq' ? 'Mesatarja e Faqes' : 'Page Average'}
                </p>
                <p className="text-3xl font-bold text-orange-900 dark:text-orange-100">
                  {transactions.length > 0 ? formatCurrency(Math.round(totalAmount / transactions.length)) : 0} L
                </p>
                <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                  {i18n.language === 'sq' ? 'Për transaksion' : 'Per transaction'}
                </p>
              </div>
              <div className="p-3 bg-orange-500 rounded-xl shadow-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Smart Filters */}
      <Card className="relative overflow-hidden bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
          <CardTitle className="flex items-center gap-3 dark:text-gray-100">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Filter className="h-5 w-5 text-white" />
            </div>
            <div>
              <span className="text-lg font-semibold">{t('receipts.filtersAndSearch')}</span>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-normal">
                {i18n.language === 'sq' ? 'Filtro dhe kërko transaksionet' : 'Filter and search transactions'}
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* Active Filters Indicator */}
          {(searchTerm || typeFilter !== "all" || tableFilter !== "all" || userFilter !== "all" || dateRange.from || dateRange.to) && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
                <Filter className="h-4 w-4" />
                <span className="font-medium">
                  {i18n.language === 'sq' ? 'Filtrat aktiv:' : 'Active filters:'}
                </span>
                <div className="flex flex-wrap gap-1">
                  {searchTerm && (
                    <Badge variant="secondary" className="text-xs">
                      {i18n.language === 'sq' ? 'Kërkim' : 'Search'}: "{searchTerm}"
                    </Badge>
                  )}
                  {typeFilter !== "all" && (
                    <Badge variant="secondary" className="text-xs">
                      {i18n.language === 'sq' ? 'Lloji' : 'Type'}: {typeFilter === 'game' ? '🎱' : '🍺'} {typeFilter}
                    </Badge>
                  )}
                  {tableFilter !== "all" && (
                    <Badge variant="secondary" className="text-xs">
                      {i18n.language === 'sq' ? 'Tavolina' : 'Table'}: #{tableFilter}
                    </Badge>
                  )}
                  {userFilter !== "all" && (
                    <Badge variant="secondary" className="text-xs">
                      {i18n.language === 'sq' ? 'Përdoruesi' : 'User'}: {uniqueUsers.find(u => u.username === userFilter)?.name || userFilter}
                    </Badge>
                  )}
                  {(dateRange.from || dateRange.to) && (
                    <Badge variant="secondary" className="text-xs">
                      {i18n.language === 'sq' ? 'Data' : 'Date'}: {dateRange.from ? formatLocalizedDate(dateRange.from, "MMM dd", i18n.language) : '...'} - {dateRange.to ? formatLocalizedDate(dateRange.to, "MMM dd", i18n.language) : '...'}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Smart Search */}
            <div className="relative lg:col-span-2">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500" />
              <Input
                placeholder={t('receipts.searchTransactions')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 h-12 bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-xl shadow-sm"
              />
              {searchTerm && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Badge variant="secondary" className="text-xs">
                    {currentTransactions.length} {i18n.language === 'sq' ? 'rezultate' : 'results'}
                  </Badge>
                </div>
              )}
            </div>

            {/* Type Filter */}
            <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
              <SelectTrigger className="h-12 bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-500 rounded-xl shadow-sm">
                <SelectValue placeholder={t('receipts.filterByType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <span>📊</span>
                    {t('receipts.allTypes')}
                  </div>
                </SelectItem>
                <SelectItem value="game">
                  <div className="flex items-center gap-2">
                    <span>🎱</span>
                    {t('receipts.gameSessions')}
                  </div>
                </SelectItem>
                <SelectItem value="order">
                  <div className="flex items-center gap-2">
                    <span>🍺</span>
                    {t('receipts.barOrders')}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Table Filter */}
            <Select value={tableFilter} onValueChange={setTableFilter}>
              <SelectTrigger className="h-12 bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-500 rounded-xl shadow-sm">
                <SelectValue placeholder={t('receipts.filterByTable')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('receipts.allTables')}</SelectItem>
                {filteredTableList
                  .slice()
                  .sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: 'base', numeric: true }))
                  .map((table) => (
                    <SelectItem key={table.number.toString()} value={table.number.toString()}>
                      {table.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>

            {/* User Filter */}
            <Select value={userFilter} onValueChange={setUserFilter}>
              <SelectTrigger className="h-12 bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-500 rounded-xl shadow-sm">
                <SelectValue placeholder={t('receipts.filterByUser')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <span>👥</span>
                    {t('receipts.allUsers')}
                  </div>
                </SelectItem>
                {allUsers.map((user) => (
                  <SelectItem key={user.username} value={user.username}>
                    <div className="flex items-center gap-2">
                      <span>👤</span>
                      {user.name} ({user.role})
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            {/* Date Range */}
            <div className="flex items-center gap-2">
              <Popover key={i18n.language}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[280px] justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {formatLocalizedDate(dateRange.from, "LLL dd, y", i18n.language)} - {formatLocalizedDate(dateRange.to, "LLL dd, y", i18n.language)}
                        </>
                      ) : (
                        formatLocalizedDate(dateRange.from, "LLL dd, y", i18n.language)
                      )
                    ) : (
                      <span>{t('receipts.pickDateRange')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent key={`popover-${i18n.language}-${calendarKey}`} className="w-auto p-0" align="start">
                  <div key={`calendar-wrapper-${i18n.language}-${calendarKey}`}>
                    <Calendar
                      key={`calendar-${i18n.language}-${calendarKey}`}
                      mode="range"
                      defaultMonth={dateRange.from}
                      selected={{
                        from: dateRange.from,
                        to: dateRange.to
                      }}
                      onSelect={(range: any) => setDateRange({
                        from: range?.from,
                        to: range?.to
                      })}
                      numberOfMonths={2}
                    />
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Sort */}
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder={t('receipts.sortBy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">{t('receipts.date')}</SelectItem>
                <SelectItem value="amount">{t('receipts.amount')}</SelectItem>
                <SelectItem value="table">{t('receipts.table')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={(value: any) => setSortOrder(value)}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder={t('receipts.order')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">{t('receipts.desc')}</SelectItem>
                <SelectItem value="asc">{t('receipts.asc')}</SelectItem>
              </SelectContent>
            </Select>

            {/* Actions */}
            <Button onClick={clearFilters} variant="outline">
              {t('receipts.clearFilters')}
            </Button>

            <Button onClick={exportToCSV} variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              {t('receipts.exportCSV')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Smart Delete Button - Only show to admin users */}
      {canAccessSettings() && (
        <div className="mb-4 flex items-center justify-between p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl border border-red-200 dark:border-red-700/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-500 rounded-lg">
              <Trash2 className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="font-medium text-red-900 dark:text-red-100">
                {i18n.language === 'sq' ? 'Menaxhimi i Fshirjes' : 'Delete Management'}
              </p>
              <p className="text-sm text-red-700 dark:text-red-300">
                {selectedTransactions.length} {t('receipts.selected')}
              </p>
            </div>
          </div>
          <Button
            onClick={handleDelete}
            disabled={selectedTransactions.length === 0}
            className="bg-red-600 hover:bg-red-700 text-white disabled:opacity-50 disabled:cursor-not-allowed rounded-xl px-6 py-2 transition-all duration-200 hover:scale-105"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {t('receipts.delete')}
          </Button>
        </div>
      )}

      {/* Smart Transaction List */}
      <Card className="relative overflow-hidden bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
          <CardTitle className="flex items-center justify-between dark:text-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
                <Receipt className="h-5 w-5 text-white" />
              </div>
              <div>
                <span className="text-lg font-semibold">{t('receipts.transactionHistory')}</span>
                <p className="text-sm text-gray-600 dark:text-gray-400 font-normal">
                  {currentTransactions.length} {i18n.language === 'sq' ? 'transaksione të shfaqura' : 'transactions shown'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-white/50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-400 border-gray-300 dark:border-gray-600">
                <Clock className="h-3 w-3 mr-1" />
                {formatTime(lastUpdateTime)}
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500 dark:text-gray-400">{t('receipts.loading')}</div>
            </div>
          ) : currentTransactions.length === 0 ? (
            <div className="text-center py-12">
              <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <Search className="h-10 w-10 text-gray-400 dark:text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {(searchTerm || typeFilter !== "all" || tableFilter !== "all" || userFilter !== "all" || dateRange.from || dateRange.to)
                  ? (i18n.language === 'sq' ? 'Nuk u gjetën rezultate' : 'No results found')
                  : t('receipts.noTransactions')
                }
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {(searchTerm || typeFilter !== "all" || tableFilter !== "all" || userFilter !== "all" || dateRange.from || dateRange.to)
                  ? (i18n.language === 'sq' ? 'Provo të ndryshosh filtrat për të gjetur transaksione.' : 'Try adjusting your filters to find transactions.')
                  : (i18n.language === 'sq' ? 'Nuk ka transaksione të disponueshme.' : 'No transactions available.')
                }
              </p>
              {(searchTerm || typeFilter !== "all" || tableFilter !== "all" || userFilter !== "all" || dateRange.from || dateRange.to) && (
                <Button onClick={clearFilters} variant="outline" className="mt-2">
                  <Filter className="h-4 w-4 mr-2" />
                  {t('receipts.clearFilters')}
                </Button>
              )}
            </div>
          ) : (
            <>
              {/* Modern Table Header */}
              <div className="bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-700 dark:to-gray-600 rounded-xl p-4 mb-4 border border-gray-200 dark:border-gray-600">
                <div className={`grid gap-4 text-sm font-semibold text-gray-700 dark:text-gray-300 items-center ${canAccessSettings() ? 'grid-cols-10' : 'grid-cols-9'}`}>
                  {canAccessSettings() && (
                    <div className="col-span-1 flex items-center">
                      <input
                        ref={selectAllRef}
                        type="checkbox"
                        checked={currentTransactions.length > 0 && currentTransactions.every((r: Transaction) => selectedTransactions.includes(`${r.source}-${r.id}`))}
                        onChange={e => handleSelectAll(e.target.checked)}
                        className="rounded"
                      />
                    </div>
                  )}
                  <div className="col-span-1">{t('receipts.type')}</div>
                  <div className="col-span-1">{t('receipts.table')}</div>
                  <div className="col-span-3">{t('receipts.user')}</div>
                  <div className="col-span-2">{t('receipts.time')}</div>
                  <div className="col-span-1">{t('receipts.amount')}</div>
                  <div className="col-span-1">{t('receipts.actions')}</div>
                </div>
              </div>

              {/* Modern Transaction Rows */}
              <div className="space-y-3">
                {currentTransactions.map((tx: Transaction, index: number) => (
                  <div
                    key={`${tx.source}-${tx.id}`}
                    className={`grid gap-4 p-4 text-sm items-center hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-gray-700 dark:hover:to-gray-600 rounded-xl border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-md hover:scale-[1.01] ${
                      canAccessSettings() ? 'grid-cols-10' : 'grid-cols-9'
                    } ${index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-800'}`}
                  >
                    {/* Checkbox - Only show to admin users */}
                    {canAccessSettings() && (
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={selectedTransactions.includes(`${tx.source}-${tx.id}`)}
                          onChange={() => handleCheckboxChange(`${tx.source}-${tx.id}`)}
                          className="rounded"
                        />
                      </div>
                    )}

                    {/* Type */}
                    <div className="col-span-1">
                      <Badge
                        variant={tx.type === "game" ? "default" : "secondary"}
                        className="text-xs px-2 py-1"
                      >
                        {tx.type === "game" ? "🎱" : "🍺"}
                      </Badge>
                    </div>

                    {/* Table */}
                    <div className="col-span-1">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {tableNumberToName[tx.tableNumber?.toString()] || tx.tableNumber}
                      </span>
                    </div>

                    {/* User */}
                    <div className="col-span-3">
                      {tx.createdByName ? (
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">{tx.createdByName}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">@{tx.createdByUsername}</span>
                        </div>
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500 text-sm">Unknown</span>
                      )}
                    </div>

                    {/* Time */}
                    <div className="col-span-2">
                      <div className="flex flex-col">
                        <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                          {formatLocalizedDate(tx.timestamp, "MMM dd, HH:mm", i18n.language)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatLocalizedDate(tx.timestamp, "yyyy", i18n.language)}
                        </span>
                      </div>
                    </div>

                    {/* Amount */}
                    <div className="col-span-1">
                      <span className="font-bold text-gray-900 dark:text-gray-100 text-sm">
                        {formatCurrency(tx.amount)} L
                      </span>
                    </div>

                    {/* Modern Actions */}
                    <div className="col-span-1">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setSelectedTransaction(tx)}
                          className="h-9 w-9 p-0 rounded-xl hover:bg-gradient-to-r hover:from-blue-100 hover:to-blue-50 dark:hover:from-blue-900/50 dark:hover:to-blue-800/50 transition-all duration-200 hover:scale-110"
                        >
                          <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => reprintReceipt(tx)}
                          disabled={tx.status === "voided" || !canReprintReceipt(tx)}
                          className="h-9 w-9 p-0 rounded-xl hover:bg-gradient-to-r hover:from-green-100 hover:to-green-50 dark:hover:from-green-900/50 dark:hover:to-green-800/50 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:hover:scale-100"
                          title={!canReprintReceipt(tx) ? "No permission to reprint" : "Reprint receipt"}
                        >
                          <Printer className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, pagination.total)} of {pagination.total} transactions
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {pagination.totalPages}
                    </span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Transaction Details Modal */}
      {selectedTransaction && <TransactionDetailsModal transaction={selectedTransaction} />}
    </div>
  )
}
