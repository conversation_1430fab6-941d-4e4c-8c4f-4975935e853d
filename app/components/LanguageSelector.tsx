'use client'

import { useState } from 'react'
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { changeLanguageSafely } from '../utils/i18nInit'
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Globe, Check } from 'lucide-react'

const languages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'sq', name: 'Albanian', nativeName: 'Shqip', flag: '🇦🇱' }
]

export function LanguageSelector() {
  const { i18n, t } = useSafeTranslation()
  const [isOpen, setIsOpen] = useState(false)

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  const handleLanguageChange = async (languageCode: string) => {
    try {
      setIsOpen(false)

      // Use the safe language change function
      await changeLanguageSafely(languageCode)

      // Optional: Store in database for cross-device sync
      try {
        const { setUIPreference } = await import('../utils/storage')
        await setUIPreference('language', languageCode, true)
        console.log(`✅ Language preference synced to database: ${languageCode}`)
      } catch (error) {
        console.warn('Failed to sync language preference to database:', error)
      }
    } catch (error) {
      console.error('Failed to change language:', error)
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center justify-center h-8 w-8 p-0"
          title={t('settings.selectLanguage')}
        >
          <Globe className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">{language.flag}</span>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{language.nativeName}</span>
                <span className="text-xs text-muted-foreground">{language.name}</span>
              </div>
            </div>
            {i18n.language === language.code && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Hook for easy language detection and management
export function useLanguage() {
  const { i18n } = useSafeTranslation()

  const changeLanguage = async (code: string) => {
    try {
      await changeLanguageSafely(code)
    } catch (error) {
      console.error('Failed to change language:', error)
    }
  }

  return {
    currentLanguage: i18n?.language || 'en',
    isRTL: false, // Neither English nor Albanian are RTL
    changeLanguage,
    availableLanguages: languages
  }
}
