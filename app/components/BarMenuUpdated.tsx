/**
 * Example of how to update BarMenu component to use the new unified storage system
 * This shows the key changes needed to migrate from the old storage system
 */

"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Coffee, Plus, Minus, ShoppingCart } from 'lucide-react'

// NEW: Import the unified storage system instead of old managers
import { 
  getStorageManager, 
  setUIPreference, 
  getUIPreference, 
  setTempData, 
  getTempData,
  setUserSetting,
  getUserSetting,
  configureStorage
} from "../utils/storage"

import { useAuth } from "../contexts/AuthContext"
import { debounce } from "../utils/debounce"
import type { Order, OrderItem, DrinkItem, Table, TableOrder } from "../types"

interface BarMenuProps {
  recentOrders: Order[]
  setRecentOrders: (orders: Order[] | ((prev: Order[]) => Order[])) => void
  setTotalRevenue: (revenue: number | ((prev: number) => number)) => void
}

export function BarMenuUpdated({ recentOrders, setRecentOrders, setTotalRevenue }: BarMenuProps) {
  const { token } = useAuth()
  const [selectedTable, setSelectedTable] = useState<number | null>(null)
  const [tableOrders, setTableOrders] = useState<{ [key: number]: TableOrder }>({})
  const [selectedCategory, setSelectedCategory] = useState<string>("coffee")
  const [searchQuery, setSearchQuery] = useState("")
  const [isStorageLoaded, setIsStorageLoaded] = useState(false)

  // NEW: Configure storage with auth token when it changes
  useEffect(() => {
    if (token) {
      configureStorage({ authToken: token })
    }
  }, [token])

  // NEW: Create debounced save functions to prevent excessive API calls
  const debouncedSaveTableOrders = debounce(async (orders: { [key: number]: TableOrder }) => {
    // Save each table order as temporary data (memory storage)
    for (const [tableNumber, orderData] of Object.entries(orders)) {
      await setTempData(`draft_order_${tableNumber}`, orderData)
    }
  }, 500) // 500ms debounce

  const debouncedSavePreferences = debounce(async (key: string, value: any) => {
    // Save UI preferences to localStorage
    await setUIPreference(key, value)
  }, 300) // 300ms debounce

  const debouncedSaveUserSettings = debounce(async (key: string, value: any) => {
    // Save user settings to database (if authenticated)
    await setUserSetting(key, value)
  }, 1000) // 1 second debounce for database operations

  // Load data from storage on mount
  useEffect(() => {
    const loadDataFromStorage = async () => {
      try {
        // Load UI preferences (from localStorage with fallback)
        const savedCategory = await getUIPreference<string>('selected_category', 'coffee')
        if (savedCategory) {
          setSelectedCategory(savedCategory)
        }

        const savedSearchQuery = await getUIPreference<string>('search_query', '')
        if (savedSearchQuery) {
          setSearchQuery(savedSearchQuery)
        }

        // Load user settings (from database with fallback to localStorage)
        const savedTable = await getUserSetting<number>('selected_table')
        if (savedTable) {
          setSelectedTable(savedTable)
        }

        // Load temporary draft orders (from memory storage)
        const memoryStorage = getStorageManager('memory')
        const keys = await memoryStorage.keys()
        const draftOrderKeys = keys.filter(key => key.startsWith('temp_draft_order_'))
        
        const loadedOrders: { [key: number]: TableOrder } = {}
        for (const key of draftOrderKeys) {
          const tableNumber = parseInt(key.replace('temp_draft_order_', ''))
          const orderData = await getTempData<TableOrder>(key.replace('temp_', ''))
          if (orderData) {
            loadedOrders[tableNumber] = orderData
          }
        }

        if (Object.keys(loadedOrders).length > 0) {
          setTableOrders(loadedOrders)
        }

        setIsStorageLoaded(true)
      } catch (error) {
        console.error("Error loading from storage:", error)
        setIsStorageLoaded(true)
      }
    }

    loadDataFromStorage()
  }, [])

  // NEW: Save table orders with debouncing (only after initial load)
  useEffect(() => {
    if (isStorageLoaded && Object.keys(tableOrders).length > 0) {
      debouncedSaveTableOrders(tableOrders)
    }
  }, [tableOrders, isStorageLoaded, debouncedSaveTableOrders])

  // NEW: Save selected table with debouncing
  useEffect(() => {
    if (selectedTable !== null) {
      // Save to both UI preferences (localStorage) and user settings (database)
      debouncedSavePreferences('selected_table', selectedTable)
      debouncedSaveUserSettings('selected_table', selectedTable)
    }
  }, [selectedTable, debouncedSavePreferences, debouncedSaveUserSettings])

  // NEW: Save selected category with debouncing
  useEffect(() => {
    if (selectedCategory) {
      debouncedSavePreferences('selected_category', selectedCategory)
    }
  }, [selectedCategory, debouncedSavePreferences])

  // NEW: Save search query with debouncing
  useEffect(() => {
    if (searchQuery) {
      debouncedSavePreferences('search_query', searchQuery)
    }
  }, [searchQuery, debouncedSavePreferences])

  // Example of how to clear temporary data when order is submitted
  const submitOrder = async () => {
    if (!selectedTable || !tableOrders[selectedTable]?.items.length) {
      return
    }

    try {
      // Submit order logic here...
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          table_number: selectedTable,
          items: tableOrders[selectedTable].items,
          total: tableOrders[selectedTable].total,
          status: "completed"
        }),
      })

      if (response.ok) {
        // NEW: Clear the temporary draft order after successful submission
        const memoryStorage = getStorageManager('memory')
        await memoryStorage.delete(`temp_draft_order_${selectedTable}`)
        
        // Remove from local state
        setTableOrders(prev => {
          const updated = { ...prev }
          delete updated[selectedTable]
          return updated
        })

        console.log("Order submitted and draft cleared successfully")
      }
    } catch (error) {
      console.error("Failed to submit order:", error)
    }
  }

  // Example of storage statistics for debugging
  const showStorageStats = async () => {
    const { getStorageStats } = await import("../utils/storage")
    const stats = await getStorageStats()
    console.log("Storage Statistics:", stats)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coffee className="h-5 w-5" />
            {t('bar.barMenuUpdated')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table selection */}
            <div>
              <label className="text-sm font-medium">Selected Table: {selectedTable || 'None'}</label>
              <div className="flex gap-2 mt-2">
                {[1, 2, 3, 4, 5].map(num => (
                  <Button
                    key={num}
                    variant={selectedTable === num ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedTable(num)}
                  >
                    Table {num}
                  </Button>
                ))}
              </div>
            </div>

            {/* Category selection */}
            <div>
              <label className="text-sm font-medium">{t('bar.category')}: {selectedCategory}</label>
              <div className="flex gap-2 mt-2">
                {['coffee', 'drinks', 'snacks'].map(cat => (
                  <Button
                    key={cat}
                    variant={selectedCategory === cat ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(cat)}
                  >
                    {t(`bar.${cat}`)}
                  </Button>
                ))}
              </div>
            </div>

            {/* Search */}
            <div>
              <label className="text-sm font-medium">{t('bar.search')}</label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={t('bar.searchItems')}
                className="w-full mt-1 px-3 py-2 border rounded-md"
              />
            </div>

            {/* Current order display */}
            {selectedTable && tableOrders[selectedTable] && (
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Table {selectedTable} Order</h3>
                <div className="space-y-2">
                  {tableOrders[selectedTable].items.map(item => (
                    <div key={item.id} className="flex justify-between items-center">
                      <span>{item.name} x{item.quantity}</span>
                      <span>${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                  <div className="border-t pt-2 font-medium">
                    Total: ${tableOrders[selectedTable].total.toFixed(2)}
                  </div>
                </div>
                <Button onClick={submitOrder} className="w-full mt-4">
                  Submit Order
                </Button>
              </div>
            )}

            {/* Debug button */}
            <Button onClick={showStorageStats} variant="outline" size="sm">
              Show Storage Stats (Debug)
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
