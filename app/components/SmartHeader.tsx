'use client'

import React, { useState, useEffect } from 'react'
import {
  Menu,
  X,
  ChevronDown,
  LogOut,
  User,
  Settings as SettingsIcon,
  Sparkles
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { LanguageSelector } from './LanguageSelector'
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { useAuth } from '../contexts/AuthContext'

interface SmartHeaderProps {
  navigationItems: Array<{
    key: string
    label: string
    icon: React.ComponentType<any>
    shortLabel: string
  }>
  activeView: string
  onViewChange: (view: string) => void
  onUserProfileOpen: () => void
  className?: string
}

export function SmartHeader({ 
  navigationItems, 
  activeView, 
  onViewChange, 
  onUserProfileOpen,
  className = ''
}: SmartHeaderProps) {
  const { t } = useSafeTranslation()
  const { user, logout } = useAuth()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Close mobile menu when view changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [activeView])

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false)
      }
    }
    
    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isMobileMenuOpen])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleNavigation = (key: string) => {
    onViewChange(key)
    setIsMobileMenuOpen(false)
  }

  const handleLogout = () => {
    try {
      logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <>
      <header className={`
        bg-white/80 dark:bg-gray-950/80
        backdrop-blur-2xl supports-[backdrop-filter]:bg-white/80 dark:supports-[backdrop-filter]:bg-gray-950/80
        border-b border-gray-200/50 dark:border-gray-800/50
        sticky top-0 z-50
        transition-all duration-300 ease-[cubic-bezier(0.4,0.0,0.2,1)]
        shadow-sm dark:shadow-gray-900/20
        ${className}
      `}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">

            {/* Apple-Style BBM Brand with Enhanced Design */}
            <button
              onClick={() => window.location.reload()}
              className="group flex items-center px-3 py-2 rounded-xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent"
              aria-label="Refresh application"
            >
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-200">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text">
                  BBM
                </h1>
              </div>
            </button>

            {/* Enhanced Apple-Style Navigation */}
            <nav className="hidden lg:flex items-center flex-1 justify-center" role="navigation" aria-label="Main navigation">
              <div className="flex items-center bg-gray-100/70 dark:bg-gray-800/70 rounded-2xl p-1 shadow-inner backdrop-blur-sm">
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  const isActive = activeView === item.key
                  return (
                    <Button
                      key={item.key}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleNavigation(item.key)}
                      className={`
                        relative flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-semibold
                        transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98]
                        focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent
                        ${isActive
                          ? 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white shadow-lg shadow-gray-200/50 dark:shadow-gray-900/50 border border-gray-200/50 dark:border-gray-700/50'
                          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-800/50'
                        }
                      `}
                      aria-current={isActive ? 'page' : undefined}
                    >
                      <Icon className={`h-4 w-4 transition-colors duration-200 ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} />
                      <span className="font-medium">{item.shortLabel}</span>
                      {isActive && (
                        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none" />
                      )}
                    </Button>
                  )
                })}
              </div>
            </nav>

            {/* Enhanced Apple-Style Right Section */}
            <div className="flex items-center space-x-3">
              {/* Enhanced Language Selector */}
              <div className="hidden sm:block">
                <div className="p-2 rounded-xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:ring-offset-2 focus-within:ring-offset-transparent">
                  <LanguageSelector />
                </div>
              </div>

              {/* Enhanced Apple-Style User Menu */}
              <div className="hidden sm:block">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="group flex items-center space-x-3 h-10 px-3 rounded-xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent"
                      aria-label="User menu"
                    >
                      <Avatar className="w-7 h-7 ring-2 ring-gray-200/50 dark:ring-gray-700/50 transition-all duration-200 group-hover:ring-blue-500/30">
                        <AvatarImage src={user?.profilePicture} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-semibold">
                          {user?.fullName ? getInitials(user.fullName) : "U"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="hidden md:block text-left">
                        <p className="text-sm font-semibold text-gray-900 dark:text-white truncate max-w-24 leading-tight">
                          {user?.fullName || 'User'}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-24 capitalize">
                          {user?.role || 'Member'}
                        </p>
                      </div>
                      <ChevronDown className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500 transition-all duration-200 group-data-[state=open]:rotate-180 group-data-[state=open]:text-gray-600 dark:group-data-[state=open]:text-gray-300" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="w-64 bg-white/95 dark:bg-gray-950/95 backdrop-blur-2xl border border-gray-200/50 dark:border-gray-800/50 shadow-2xl rounded-2xl overflow-hidden p-2"
                    sideOffset={8}
                  >
                    <div className="px-4 py-4 border-b border-gray-200/50 dark:border-gray-800/50 mb-2">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-10 h-10 ring-2 ring-gray-200/50 dark:ring-gray-700/50">
                          <AvatarImage src={user?.profilePicture} />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm font-semibold">
                            {user?.fullName ? getInitials(user.fullName) : "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">{user?.fullName}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate capitalize">{user?.role || 'Member'}</p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <DropdownMenuItem
                        onClick={onUserProfileOpen}
                        className="group rounded-xl px-4 py-3 hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 cursor-pointer focus:bg-gray-100/60 dark:focus:bg-gray-800/60 focus:outline-none"
                      >
                        <User className="mr-3 h-4 w-4 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">{t('navigation.profile')}</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleNavigation('settings')}
                        className="group rounded-xl px-4 py-3 hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 cursor-pointer focus:bg-gray-100/60 dark:focus:bg-gray-800/60 focus:outline-none"
                      >
                        <SettingsIcon className="mr-3 h-4 w-4 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">{t('navigation.settings')}</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="my-2 bg-gray-200/50 dark:bg-gray-800/50" />
                      <DropdownMenuItem
                        onClick={handleLogout}
                        className="group rounded-xl px-4 py-3 hover:bg-red-50/80 dark:hover:bg-red-900/20 transition-all duration-200 cursor-pointer focus:bg-red-50/80 dark:focus:bg-red-900/20 focus:outline-none"
                      >
                        <LogOut className="mr-3 h-4 w-4 text-red-500 dark:text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-200" />
                        <span className="text-sm font-medium text-red-600 dark:text-red-400 group-hover:text-red-700 dark:group-hover:text-red-300">{t('navigation.logout')}</span>
                      </DropdownMenuItem>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Enhanced Apple-Style Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2.5 rounded-xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent"
                aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
                aria-expanded={isMobileMenuOpen}
              >
                <div className="relative w-5 h-5">
                  <Menu className={`absolute inset-0 h-5 w-5 text-gray-700 dark:text-gray-300 transition-all duration-200 ${isMobileMenuOpen ? 'opacity-0 rotate-90 scale-75' : 'opacity-100 rotate-0 scale-100'}`} />
                  <X className={`absolute inset-0 h-5 w-5 text-gray-700 dark:text-gray-300 transition-all duration-200 ${isMobileMenuOpen ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-90 scale-75'}`} />
                </div>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Apple-Style Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-md z-40 lg:hidden transition-all duration-300 ease-[cubic-bezier(0.4,0.0,0.2,1)]"
          onClick={() => setIsMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Enhanced Apple-Style Mobile Menu */}
      <div className={`
        fixed top-14 left-0 right-0
        bg-white/95 dark:bg-gray-950/95 backdrop-blur-2xl
        border-b border-gray-200/50 dark:border-gray-800/50
        shadow-2xl z-40 lg:hidden
        transform transition-all duration-300 ease-[cubic-bezier(0.4,0.0,0.2,1)]
        ${isMobileMenuOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}
      `}>
        <div className="max-w-7xl mx-auto px-6 py-6">

          {/* Enhanced Mobile Navigation */}
          <div className="space-y-2 mb-8">
            {navigationItems.map((item, index) => {
              const Icon = item.icon
              const isActive = activeView === item.key
              return (
                <Button
                  key={item.key}
                  variant="ghost"
                  onClick={() => handleNavigation(item.key)}
                  className={`
                    group w-full justify-start space-x-4 h-14 rounded-2xl transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98]
                    focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent
                    ${isActive
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25'
                      : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/60 dark:hover:bg-gray-800/60'
                    }
                  `}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className={`p-2 rounded-xl transition-all duration-200 ${
                    isActive
                      ? 'bg-white/20'
                      : 'bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700'
                  }`}>
                    <Icon className={`h-5 w-5 transition-colors duration-200 ${
                      isActive ? 'text-white' : 'text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                    }`} />
                  </div>
                  <span className="font-semibold text-base">{item.label}</span>
                  {isActive && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full shadow-sm" />
                  )}
                </Button>
              )
            })}
          </div>

          {/* Enhanced Mobile User Section */}
          <div className="border-t border-gray-200/50 dark:border-gray-800/50 pt-6 space-y-4">
            <div className="flex items-center space-x-4 px-4 py-4 bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-gray-800/80 dark:to-gray-900/80 rounded-2xl backdrop-blur-sm">
              <Avatar className="w-12 h-12 ring-2 ring-gray-200/50 dark:ring-gray-700/50">
                <AvatarImage src={user?.profilePicture} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-lg">
                  {user?.fullName ? getInitials(user.fullName) : "U"}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-base font-semibold text-gray-900 dark:text-white">{user?.fullName}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{user?.role || 'Member'}</p>
              </div>
              <div className="p-2 rounded-xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm">
                <LanguageSelector />
              </div>
            </div>

            <div className="space-y-2">
              <Button
                variant="ghost"
                onClick={() => {
                  onUserProfileOpen()
                  setIsMobileMenuOpen(false)
                }}
                className="group w-full justify-start h-12 rounded-2xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 focus:ring-offset-transparent"
              >
                <div className="p-2 rounded-xl bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700 transition-colors duration-200 mr-3">
                  <User className="h-4 w-4 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
                </div>
                <span className="text-gray-900 dark:text-gray-100 font-medium">{t('navigation.profile')}</span>
              </Button>
              <Button
                variant="ghost"
                onClick={handleLogout}
                className="group w-full justify-start h-12 rounded-2xl text-red-600 dark:text-red-400 hover:bg-red-50/80 dark:hover:bg-red-900/20 transition-all duration-200 ease-[cubic-bezier(0.4,0.0,0.2,1)] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:ring-offset-2 focus:ring-offset-transparent"
              >
                <div className="p-2 rounded-xl bg-red-100/80 dark:bg-red-900/30 group-hover:bg-red-200/80 dark:group-hover:bg-red-800/40 transition-colors duration-200 mr-3">
                  <LogOut className="h-4 w-4 text-red-600 dark:text-red-400 transition-colors duration-200" />
                </div>
                <span className="font-medium">{t('navigation.logout')}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default SmartHeader
