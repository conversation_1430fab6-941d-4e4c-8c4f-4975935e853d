"use client"

import React from 'react'
import { clearAllStorage } from '../utils/storage'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    // Try to fix storage issues automatically
    try {
      // Clear potentially corrupted localStorage data
      if (typeof window !== 'undefined' && window.localStorage) {
        const keysToCheck = [
          'bbm_active_view',
          'bbm_settings_active_tab',
          'bbm_analytics_active_tab',
          'bbm_analytics_time_range',
          'bbm_bar_selected_category',
          'bbm_bar_search_query'
        ]

        keysToCheck.forEach(key => {
          try {
            const value = localStorage.getItem(key)
            if (value && value.includes('undefined') || value === 'null') {
              localStorage.removeItem(key)
              console.log(`🔧 Removed corrupted localStorage key: ${key}`)
            }
          } catch (e) {
            console.warn(`Failed to check localStorage key ${key}:`, e)
          }
        })
      }
    } catch (fixError) {
      console.error('Failed to fix storage issues:', fixError)
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
          <h2 className="text-lg font-semibold text-red-800 dark:text-red-400 mb-2">Something went wrong</h2>
          <p className="text-red-600 dark:text-red-400 mb-4">
            There was an error loading this component. This is often caused by corrupted browser data.
          </p>
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
            <button
              onClick={async () => {
                try {
                  const { getStorageManager } = await import('../utils/storage')
                  const localStorage = getStorageManager('local')
                  const memoryStorage = getStorageManager('memory')
                  await localStorage.clear()
                  await memoryStorage.clear()
                } catch (error) {
                  console.error('Failed to clear cache:', error)
                }
                window.location.reload()
              }}
              className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
            >
              Clear Cache
            </button>
            <button
              onClick={async () => {
                try {
                  await clearAllStorage()
                } catch (error) {
                  console.error('Failed to clear all storage:', error)
                }
                window.location.reload()
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Emergency Reset
            </button>
          </div>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mt-4">
              <summary className="cursor-pointer text-red-700 font-medium">Error Details</summary>
              <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">
                {this.state.error.stack}
              </pre>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    )
  }
}
