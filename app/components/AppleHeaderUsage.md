# Apple Header Component

A responsive header component that mimics Apple's website design with semi-transparent background, smooth scroll effects, and mobile menu.

## Features

✨ **Apple-Inspired Design**
- Semi-transparent background with backdrop blur
- Becomes opaque on scroll (just like Apple's website)
- Minimalist 44px height
- System font typography

📱 **Responsive**
- Mobile menu at md breakpoint (768px)
- Smooth hamburger menu animation
- Touch-friendly mobile interface

♿ **Accessible**
- Full keyboard navigation
- ARIA labels and roles
- Screen reader friendly
- Focus management

🎨 **Customizable**
- Custom navigation items
- SVG billiard logo included
- Dark/light mode support
- Reusable component

## Usage

### Basic Usage

```tsx
import { AppleHeader } from './components/AppleHeader'

function App() {
  const navigationItems = [
    { label: 'Tables', href: 'tables', isActive: true },
    { label: 'Bar', href: 'bar' },
    { label: 'Analytics', href: 'analytics' },
    { label: 'Receipts', href: 'receipts' },
    { label: 'Settings', href: 'settings' },
  ]

  const handleNavigation = (href: string) => {
    console.log('Navigate to:', href)
    // Your navigation logic here
  }

  return (
    <div>
      <AppleHeader 
        navigationItems={navigationItems}
        onNavigationClick={handleNavigation}
      />
      {/* Your content */}
    </div>
  )
}
```

### Props Interface

```typescript
interface NavigationItem {
  label: string
  href: string
  isActive?: boolean
}

interface AppleHeaderProps {
  navigationItems?: NavigationItem[]
  onNavigationClick?: (href: string) => void
  className?: string
}
```

### Default Navigation Items

If no `navigationItems` are provided, the component uses these defaults:
- Tables
- Bar  
- Analytics
- Receipts
- Settings

## Styling

The component uses Tailwind CSS classes and follows Apple's design principles:

- **Height**: 44px (h-11)
- **Background**: Semi-transparent with backdrop blur
- **Typography**: System font stack
- **Transitions**: Smooth with ease-out timing
- **Colors**: Semantic gray scale with blue accents

## Scroll Behavior

The header automatically detects scroll position and adjusts its appearance:

- **Not scrolled**: `bg-white/70 dark:bg-gray-950/70` with `backdrop-blur-lg`
- **Scrolled**: `bg-white/95 dark:bg-gray-950/95` with `backdrop-blur-xl` and border

## Mobile Menu

- Appears at `md` breakpoint (768px and below)
- Animated hamburger icon
- Slide-down menu with backdrop
- Closes on item click or outside click
- Keyboard accessible (ESC key)

## Accessibility Features

- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader announcements
- Semantic HTML structure

## Integration with Existing App

To replace the current SmartHeader:

1. Import the new AppleHeader component
2. Update your navigation state management
3. Replace SmartHeader with AppleHeader in your main component
4. Adjust any specific styling or functionality as needed

## Dark Mode Support

The component automatically supports dark mode through Tailwind's `dark:` variants and will adapt to your app's theme system.
