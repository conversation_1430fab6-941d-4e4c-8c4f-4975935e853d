'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Settings, 
  Shield, 
  Database, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Save,
  RotateCcw
} from 'lucide-react'
import { useSafeTranslation } from '../hooks/useSafeTranslation'

interface StorageConfig {
  quotas: {
    memory: number
    localStorage: number
    database: number
  }
  security: {
    enabled: boolean
    checksumAlgorithm: 'md5' | 'sha256'
    encryptSensitive: boolean
    suspiciousThresholds: {
      rapidWrites: number
      unknownKeys: number
      largeValues: number
    }
  }
  performance: {
    deboun<PERSON>Delay: number
    cacheSize: number
    autoCleanup: boolean
    compressionEnabled: boolean
  }
  analytics: {
    enabled: boolean
    retentionDays: number
    detailedTracking: boolean
    persistToDatabase: boolean
  }
}

export function StorageConfigPanel() {
  const { t } = useSafeTranslation()
  const [config, setConfig] = useState<StorageConfig | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    setIsLoading(true)
    try {
      // Load current configuration from storage managers
      const [
        { storageFactory },
        { securityMonitor },
        { storageInsights }
      ] = await Promise.all([
        import('../utils/storage/StorageManagerFactory'),
        import('../utils/storage/SecurityMonitor'),
        import('../utils/storage/StorageInsights')
      ])

      // Get current settings (these would normally be persisted)
      const defaultConfig: StorageConfig = {
        quotas: {
          memory: 1000,
          localStorage: 500,
          database: 10000
        },
        security: {
          enabled: true,
          checksumAlgorithm: 'sha256',
          encryptSensitive: true,
          suspiciousThresholds: {
            rapidWrites: 100,
            unknownKeys: 50,
            largeValues: 1024 * 1024 // 1MB
          }
        },
        performance: {
          debounceDelay: 300,
          cacheSize: 1000,
          autoCleanup: true,
          compressionEnabled: false
        },
        analytics: {
          enabled: true,
          retentionDays: 30,
          detailedTracking: true,
          persistToDatabase: true
        }
      }

      setConfig(defaultConfig)
    } catch (error) {
      console.error('Failed to load storage config:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const saveConfig = async () => {
    if (!config) return

    setIsSaving(true)
    try {
      // Apply configuration to storage managers
      const [
        { storageFactory },
        { securityMonitor }
      ] = await Promise.all([
        import('../utils/storage/StorageManagerFactory'),
        import('../utils/storage/SecurityMonitor')
      ])

      // Configure storage factory
      storageFactory.configure({
        debounceDelay: config.performance.debounceDelay,
        cacheSize: config.performance.cacheSize
      })

      // Configure security monitor
      securityMonitor.configure({
        enabled: config.security.enabled,
        checksumAlgorithm: config.security.checksumAlgorithm,
        suspiciousThresholds: config.security.suspiciousThresholds
      })

      // Save config to storage for persistence
      const configManager = storageFactory.getStorageManager('database')
      await configManager.setJSON('storage_config', config)

      setHasChanges(false)
      console.log('Storage configuration saved successfully')
    } catch (error) {
      console.error('Failed to save storage config:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const resetConfig = () => {
    loadConfig()
    setHasChanges(false)
  }

  const updateConfig = (path: string, value: any) => {
    if (!config) return

    const newConfig = { ...config }
    const keys = path.split('.')
    let current: any = newConfig

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]]
    }
    current[keys[keys.length - 1]] = value

    setConfig(newConfig)
    setHasChanges(true)
  }

  if (isLoading || !config) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Storage Configuration
          </h2>
          <p className="text-sm text-muted-foreground">
            Configure storage quotas, security, performance, and analytics settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={resetConfig} 
            variant="outline" 
            size="sm"
            disabled={!hasChanges}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button 
            onClick={saveConfig} 
            size="sm"
            disabled={!hasChanges || isSaving}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {hasChanges && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <span className="text-sm text-yellow-800">You have unsaved changes</span>
        </div>
      )}

      <Tabs defaultValue="quotas" className="space-y-4">
        <TabsList>
          <TabsTrigger value="quotas">Quotas</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="quotas" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Storage Quotas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="memory-quota">Memory Storage (items)</Label>
                  <Input
                    id="memory-quota"
                    type="number"
                    value={config.quotas.memory}
                    onChange={(e) => updateConfig('quotas.memory', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum items in memory cache
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="local-quota">Local Storage (items)</Label>
                  <Input
                    id="local-quota"
                    type="number"
                    value={config.quotas.localStorage}
                    onChange={(e) => updateConfig('quotas.localStorage', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum items in localStorage
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="db-quota">Database Storage (items)</Label>
                  <Input
                    id="db-quota"
                    type="number"
                    value={config.quotas.database}
                    onChange={(e) => updateConfig('quotas.database', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Estimated maximum database items
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="security-enabled">Enable Security Monitoring</Label>
                  <p className="text-sm text-muted-foreground">
                    Monitor for tampering and suspicious activity
                  </p>
                </div>
                <Switch
                  id="security-enabled"
                  checked={config.security.enabled}
                  onCheckedChange={(checked) => updateConfig('security.enabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="encrypt-sensitive">Encrypt Sensitive Data</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically encrypt sensitive values
                  </p>
                </div>
                <Switch
                  id="encrypt-sensitive"
                  checked={config.security.encryptSensitive}
                  onCheckedChange={(checked) => updateConfig('security.encryptSensitive', checked)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="rapid-writes">Rapid Writes Threshold</Label>
                  <Input
                    id="rapid-writes"
                    type="number"
                    value={config.security.suspiciousThresholds.rapidWrites}
                    onChange={(e) => updateConfig('security.suspiciousThresholds.rapidWrites', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Writes per minute before flagging
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unknown-keys">Unknown Keys Threshold</Label>
                  <Input
                    id="unknown-keys"
                    type="number"
                    value={config.security.suspiciousThresholds.unknownKeys}
                    onChange={(e) => updateConfig('security.suspiciousThresholds.unknownKeys', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Unknown keys per hour before flagging
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="large-values">Large Value Threshold (bytes)</Label>
                  <Input
                    id="large-values"
                    type="number"
                    value={config.security.suspiciousThresholds.largeValues}
                    onChange={(e) => updateConfig('security.suspiciousThresholds.largeValues', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Value size before flagging as large
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Performance Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="debounce-delay">Debounce Delay (ms)</Label>
                  <Input
                    id="debounce-delay"
                    type="number"
                    value={config.performance.debounceDelay}
                    onChange={(e) => updateConfig('performance.debounceDelay', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Delay before executing database writes
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cache-size">Cache Size (items)</Label>
                  <Input
                    id="cache-size"
                    type="number"
                    value={config.performance.cacheSize}
                    onChange={(e) => updateConfig('performance.cacheSize', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum items in performance cache
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-cleanup">Auto Cleanup</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically clean up old cache entries
                  </p>
                </div>
                <Switch
                  id="auto-cleanup"
                  checked={config.performance.autoCleanup}
                  onCheckedChange={(checked) => updateConfig('performance.autoCleanup', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="compression">Enable Compression</Label>
                  <p className="text-sm text-muted-foreground">
                    Compress large values to save space
                  </p>
                </div>
                <Switch
                  id="compression"
                  checked={config.performance.compressionEnabled}
                  onCheckedChange={(checked) => updateConfig('performance.compressionEnabled', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytics Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analytics-enabled">Enable Analytics</Label>
                  <p className="text-sm text-muted-foreground">
                    Track storage usage and performance metrics
                  </p>
                </div>
                <Switch
                  id="analytics-enabled"
                  checked={config.analytics.enabled}
                  onCheckedChange={(checked) => updateConfig('analytics.enabled', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="retention-days">Data Retention (days)</Label>
                <Input
                  id="retention-days"
                  type="number"
                  value={config.analytics.retentionDays}
                  onChange={(e) => updateConfig('analytics.retentionDays', parseInt(e.target.value))}
                />
                <p className="text-xs text-muted-foreground">
                  How long to keep historical analytics data
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="detailed-tracking">Detailed Tracking</Label>
                  <p className="text-sm text-muted-foreground">
                    Track individual key usage patterns
                  </p>
                </div>
                <Switch
                  id="detailed-tracking"
                  checked={config.analytics.detailedTracking}
                  onCheckedChange={(checked) => updateConfig('analytics.detailedTracking', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="persist-db">Persist to Database</Label>
                  <p className="text-sm text-muted-foreground">
                    Store analytics data in database for cross-device access
                  </p>
                </div>
                <Switch
                  id="persist-db"
                  checked={config.analytics.persistToDatabase}
                  onCheckedChange={(checked) => updateConfig('analytics.persistToDatabase', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
