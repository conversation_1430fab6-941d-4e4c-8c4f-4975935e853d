"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { User, Edit, Trash2, Plus, Save, Eye, EyeOff, UserCheck, UserX, ChevronDown, ChevronRight, X } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"

interface UserData {
  id: number
  username: string
  full_name: string
  role: string
  profile_picture?: string
  is_active: boolean
  created_at: string
}

export function UsersList() {
  const { t } = useSafeTranslation()
  const { user } = useAuth()
  const [users, setUsers] = useState<UserData[]>([])
  const [loading, setLoading] = useState(false)
  const [expandedUserId, setExpandedUserId] = useState<number | null>(null)
  const [editingUserId, setEditingUserId] = useState<number | null>(null)
  const [isCreating, setIsCreating] = useState(false)

  // Form states for each user
  const [formData, setFormData] = useState<{[key: number]: any}>({})
  const [newUserForm, setNewUserForm] = useState({
    username: "",
    fullName: "",
    role: "waiter",
    isActive: true,
    password: ""
  })
  const [showPasswords, setShowPasswords] = useState<{[key: number]: boolean}>({})
  const [showNewUserPassword, setShowNewUserPassword] = useState(false)
  const [saving, setSaving] = useState<{[key: number]: boolean}>({})

  // Load users
  useEffect(() => {
    if (user?.role === 'admin') {
      loadUsers()
    }
  }, [user])

  const loadUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/users', {
        credentials: 'include' // Use cookies for authentication
      })

      if (response.ok) {
        const data = await response.json()
        setUsers(data)
      }
    } catch (error) {
      console.error('Failed to load users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExpandUser = (userId: number) => {
    if (expandedUserId === userId) {
      setExpandedUserId(null)
      setEditingUserId(null)
    } else {
      setExpandedUserId(userId)
      setEditingUserId(null)
    }
  }

  const handleEditUser = (userId: number) => {
    setEditingUserId(userId)
    // Initialize form data for this user
    const userData = users.find(u => u.id === userId)
    if (userData) {
      setFormData(prev => ({
        ...prev,
        [userId]: {
          username: userData.username,
          fullName: userData.full_name,
          role: userData.role,
          isActive: userData.is_active,
          newPassword: ""
        }
      }))
    }
  }

  const handleCancelEdit = (userId: number) => {
    setEditingUserId(null)
    // Reset form data
    const userData = users.find(u => u.id === userId)
    if (userData) {
      setFormData(prev => ({
        ...prev,
        [userId]: {
          username: userData.username,
          fullName: userData.full_name,
          role: userData.role,
          isActive: userData.is_active,
          newPassword: ""
        }
      }))
    }
  }

  const handleSaveUser = async (userId: number) => {
    const userData = formData[userId]
    if (!userData?.username.trim() || !userData?.fullName.trim()) {
      alert(t('users.usernameAndNameRequired'))
      return
    }

    setSaving(prev => ({ ...prev, [userId]: true }))
    try {
      const requestData: any = {
        userId: userId,
        username: userData.username.trim(),
        fullName: userData.fullName.trim(),
        role: userData.role,
        isActive: userData.isActive
      }

      if (userData.newPassword.trim()) {
        requestData.newPassword = userData.newPassword
      }

      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        await loadUsers()
        setEditingUserId(null)
        alert(t('users.userUpdatedSuccessfully'))
      } else {
        const errorData = await response.json()
        alert(errorData.error || t('users.failedToUpdateUser'))
      }
    } catch (error) {
      console.error('Failed to update user:', error)
      alert(t('users.failedToUpdateUser'))
    } finally {
      setSaving(prev => ({ ...prev, [userId]: false }))
    }
  }

  const handleCreateUser = () => {
    setIsCreating(true)
    setNewUserForm({
      username: "",
      fullName: "",
      role: "waiter",
      isActive: true,
      password: ""
    })
  }

  const handleSaveNewUser = async () => {
    if (!newUserForm.username.trim() || !newUserForm.fullName.trim()) {
      alert(t('users.usernameAndNameRequired'))
      return
    }

    if (!newUserForm.password.trim()) {
      alert(t('users.passwordRequired'))
      return
    }

    setSaving(prev => ({ ...prev, [-1]: true }))
    try {
      const requestData = {
        username: newUserForm.username.trim(),
        fullName: newUserForm.fullName.trim(),
        role: newUserForm.role,
        password: newUserForm.password
      }

      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        await loadUsers()
        setIsCreating(false)
        alert(t('users.userCreatedSuccessfully'))
      } else {
        const errorData = await response.json()
        alert(errorData.error || t('users.failedToCreateUser'))
      }
    } catch (error) {
      console.error('Failed to create user:', error)
      alert(t('users.failedToCreateUser'))
    } finally {
      setSaving(prev => ({ ...prev, [-1]: false }))
    }
  }

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(t('users.confirmDeleteUser', { username }))) {
      return
    }

    try {
      const response = await fetch(`/api/admin/users?id=${userId}`, {
        method: 'DELETE',
        credentials: 'include' // Use cookies for authentication
      })

      if (response.ok) {
        await loadUsers()
        alert(t('users.userDeletedSuccessfully'))
      } else {
        const errorData = await response.json()
        alert(errorData.error || t('users.failedToDeleteUser'))
      }
    } catch (error) {
      console.error('Failed to delete user:', error)
      alert(t('users.failedToDeleteUser'))
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (user?.role !== 'admin') {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('users.userManagement')}
            </CardTitle>
            {!loading && (
              <p className="text-sm text-gray-500 mt-1">
                {users.length} {users.length === 1 ? t('users.user') : t('users.users')} {t('users.total')}
                {users.filter(u => u.is_active).length !== users.length &&
                  ` • ${users.filter(u => u.is_active).length} ${t('users.active')}`
                }
              </p>
            )}
          </div>
          <Button onClick={handleCreateUser} size="sm" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t('users.addUser')}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {loading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-sm text-gray-500">{t('users.loadingUsers')}</p>
          </div>
        ) : users.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <User className="h-8 w-8 text-gray-400 mb-2" />
            <p className="text-gray-500 font-medium">{t('users.noUsersFound')}</p>
            <p className="text-gray-400 text-sm">{t('users.clickAddUserToCreate')}</p>
          </div>
        ) : (
          <>
            {/* Create New User Form */}
            {isCreating && (
              <div className="border border-blue-200 bg-blue-50 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-blue-900">{t('users.createNewUser')}</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCreating(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <Label className="text-xs">{t('users.username')}</Label>
                    <Input
                      size="sm"
                      value={newUserForm.username}
                      onChange={(e) => setNewUserForm({ ...newUserForm, username: e.target.value })}
                      placeholder={t('users.enterUsername')}
                    />
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs">{t('users.fullName')}</Label>
                    <Input
                      size="sm"
                      value={newUserForm.fullName}
                      onChange={(e) => setNewUserForm({ ...newUserForm, fullName: e.target.value })}
                      placeholder={t('users.enterFullName')}
                    />
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs">{t('users.role')}</Label>
                    <Select value={newUserForm.role} onValueChange={(value) => setNewUserForm({ ...newUserForm, role: value })}>
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="waiter">{t('users.waiter')}</SelectItem>
                        <SelectItem value="admin">{t('users.admin')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs">{t('users.password')}</Label>
                    <div className="relative">
                      <Input
                        size="sm"
                        type={showNewUserPassword ? "text" : "password"}
                        value={newUserForm.password}
                        onChange={(e) => setNewUserForm({ ...newUserForm, password: e.target.value })}
                        placeholder={t('users.enterPassword')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewUserPassword(!showNewUserPassword)}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showNewUserPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" onClick={() => setIsCreating(false)}>
                    {t('users.cancel')}
                  </Button>
                  <Button size="sm" onClick={handleSaveNewUser} disabled={saving[-1]}>
                    {saving[-1] ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                        {t('users.creating')}
                      </>
                    ) : (
                      <>
                        <Save className="h-3 w-3 mr-1" />
                        {t('users.createUser')}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Users List */}
            <div className="space-y-2">
              {users.map((userData) => (
                <div key={userData.id} className={`border rounded-lg transition-all duration-200 ${
                  expandedUserId === userData.id
                    ? "border-blue-200 bg-blue-50/30"
                    : "border-gray-200 hover:border-gray-300"
                }`}>
                  {/* User Header */}
                  <div className="p-3">
                    <div className="flex items-center justify-between">
                      <div
                        className="flex items-center space-x-3 cursor-pointer flex-1 hover:bg-gray-50 rounded p-1 -m-1 transition-colors"
                        onClick={() => handleExpandUser(userData.id)}
                      >
                        {/* Expand/Collapse Indicator */}
                        <div className="flex-shrink-0">
                          {expandedUserId === userData.id ? (
                            <ChevronDown className="h-4 w-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          )}
                        </div>

                        <Avatar className="w-8 h-8">
                          <AvatarImage src={userData.profile_picture} />
                          <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
                            {getInitials(userData.full_name)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium text-gray-900 truncate">{userData.full_name}</h3>
                            <Badge variant={userData.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                              {userData.role}
                            </Badge>
                            {userData.is_active ? (
                              <Badge variant="outline" className="text-green-600 border-green-200 text-xs">
                                <UserCheck className="h-2 w-2 mr-1" />
                                {t('users.active')}
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-red-600 border-red-200 text-xs">
                                <UserX className="h-2 w-2 mr-1" />
                                {t('users.inactive')}
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-gray-500">@{userData.username}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        {userData.id !== user?.id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(userData.id, userData.username)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 h-7 w-7 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Expandable Edit Section */}
                    {expandedUserId === userData.id && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        {editingUserId === userData.id ? (
                          /* Edit Mode */
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900">{t('users.editUser')}</h4>
                              <div className="flex gap-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCancelEdit(userData.id)}
                                  className="h-7"
                                >
                                  {t('users.cancel')}
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveUser(userData.id)}
                                  disabled={saving[userData.id]}
                                  className="h-7"
                                >
                                  {saving[userData.id] ? (
                                    <>
                                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                                      {t('users.saving')}
                                    </>
                                  ) : (
                                    <>
                                      <Save className="h-3 w-3 mr-1" />
                                      {t('users.save')}
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="space-y-1">
                                <Label className="text-xs">{t('users.username')}</Label>
                                <Input
                                  size="sm"
                                  value={formData[userData.id]?.username || userData.username}
                                  onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    [userData.id]: {
                                      ...prev[userData.id],
                                      username: e.target.value
                                    }
                                  }))}
                                  placeholder={t('users.enterUsername')}
                                />
                              </div>

                              <div className="space-y-1">
                                <Label className="text-xs">{t('users.fullName')}</Label>
                                <Input
                                  size="sm"
                                  value={formData[userData.id]?.fullName || userData.full_name}
                                  onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    [userData.id]: {
                                      ...prev[userData.id],
                                      fullName: e.target.value
                                    }
                                  }))}
                                  placeholder={t('users.enterFullName')}
                                />
                              </div>

                              <div className="space-y-1">
                                <Label className="text-xs">{t('users.role')}</Label>
                                <Select
                                  value={formData[userData.id]?.role || userData.role}
                                  onValueChange={(value) => setFormData(prev => ({
                                    ...prev,
                                    [userData.id]: {
                                      ...prev[userData.id],
                                      role: value
                                    }
                                  }))}
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="waiter">{t('users.waiter')}</SelectItem>
                                    <SelectItem value="admin">{t('users.admin')}</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-1">
                                <Label className="text-xs">{t('users.newPasswordOptional')}</Label>
                                <div className="relative">
                                  <Input
                                    size="sm"
                                    type={showPasswords[userData.id] ? "text" : "password"}
                                    value={formData[userData.id]?.newPassword || ""}
                                    onChange={(e) => setFormData(prev => ({
                                      ...prev,
                                      [userData.id]: {
                                        ...prev[userData.id],
                                        newPassword: e.target.value
                                      }
                                    }))}
                                    placeholder={t('users.enterNewPassword')}
                                  />
                                  <button
                                    type="button"
                                    onClick={() => setShowPasswords(prev => ({ ...prev, [userData.id]: !prev[userData.id] }))}
                                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                                  >
                                    {showPasswords[userData.id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                                  </button>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id={`active-${userData.id}`}
                                  checked={formData[userData.id]?.isActive ?? userData.is_active}
                                  onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    [userData.id]: {
                                      ...prev[userData.id],
                                      isActive: e.target.checked
                                    }
                                  }))}
                                  className="rounded"
                                />
                                <Label htmlFor={`active-${userData.id}`} className="text-xs">{t('users.activeUser')}</Label>
                              </div>
                            </div>
                          </div>
                        ) : (
                          /* View Mode */
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900">{t('users.userDetails')}</h4>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditUser(userData.id)}
                                className="h-7"
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                {t('users.edit')}
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-3 text-xs">
                              <div>
                                <span className="font-medium text-gray-600">{t('users.username')}:</span>
                                <span className="ml-1 text-gray-900">@{userData.username}</span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-600">{t('users.role')}:</span>
                                <span className="ml-1 text-gray-900 capitalize">{userData.role}</span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-600">{t('users.status')}:</span>
                                <span className={`ml-1 ${userData.is_active ? 'text-green-600' : 'text-red-600'}`}>
                                  {userData.is_active ? t('users.active') : t('users.inactive')}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium text-gray-600">{t('users.created')}:</span>
                                <span className="ml-1 text-gray-900">{new Date(userData.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
