'use client'

import { useEffect, useState } from 'react'
import { I18nextProvider } from 'react-i18next'
import { initializeI18n, isI18nReady } from '../utils/i18nInit'
import i18n from '../utils/i18nInit'

interface I18nProviderProps {
  children: React.ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const setupI18n = async () => {
      try {
        // Check if already initialized
        if (isI18nReady()) {
          setIsInitialized(true)
          return
        }

        // Initialize i18n
        await initializeI18n()
        setIsInitialized(true)
      } catch (error) {
        console.warn('Failed to initialize i18n in provider:', error)
        // Still set to true to prevent infinite loading and use fallbacks
        setIsInitialized(true)
      }
    }

    setupI18n()
  }, [])

  // Show loading state while i18n is initializing
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  )
}
