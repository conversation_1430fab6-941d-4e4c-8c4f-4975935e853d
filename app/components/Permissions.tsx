"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Shield, Save, Users, Crown, Eye, EyeOff, Edit, Settings as SettingsIcon } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"

interface PermissionSet {
  games: {
    viewAllTables: boolean
    viewOwnTablesOnly: boolean
    createNewGames: boolean
    stopAnyGame: boolean
    stopOwnGamesOnly: boolean
    viewAllHistory: boolean
    viewOwnHistoryOnly: boolean
  }
  bar: {
    viewAllOrders: boolean
    viewOwnOrdersOnly: boolean
    createOrders: boolean
    editAnyOrder: boolean
    editOwnOrdersOnly: boolean
    viewAllHistory: boolean
    viewOwnHistoryOnly: boolean
  }
  analytics: {
    viewAnalytics: boolean
    viewOwnStats: boolean
  }
  receipts: {
    viewAllReceipts: boolean
    viewOwnReceiptsOnly: boolean
    reprintAnyReceipt: boolean
    reprintOwnReceiptsOnly: boolean
  }
  settings: {
    accessSettings: boolean
    manageUsers: boolean
    managePermissions: boolean
  }
}

interface Permissions {
  waiter: PermissionSet
  admin: PermissionSet
}

export function Permissions() {
  const { t } = useSafeTranslation()
  const { user } = useAuth()
  const [permissions, setPermissions] = useState<Permissions | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (user?.role === 'admin') {
      loadPermissions()
    }
  }, [user])

  const loadPermissions = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/permissions', {
        credentials: 'include' // Use cookies for authentication
      })

      if (response.ok) {
        const data = await response.json()
        setPermissions(data)
      }
    } catch (error) {
      console.error('Failed to load permissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const updatePermission = (role: 'waiter' | 'admin', category: string, permission: string, value: boolean) => {
    if (!permissions) return

    setPermissions(prev => ({
      ...prev!,
      [role]: {
        ...prev![role],
        [category]: {
          ...prev![role][category as keyof PermissionSet],
          [permission]: value
        }
      }
    }))
  }

  const savePermissions = async (role: 'waiter' | 'admin') => {
    if (!permissions) return

    setSaving(true)
    try {
      const response = await fetch('/api/permissions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          role,
          permissions: permissions[role]
        })
      })

      if (response.ok) {
        alert(t('settings.permissionsUpdatedSuccessfully', { role: role.charAt(0).toUpperCase() + role.slice(1) }))
      } else {
        const errorData = await response.json()
        alert(errorData.error || t('settings.failedToUpdatePermissions'))
      }
    } catch (error) {
      console.error('Failed to save permissions:', error)
      alert(t('settings.failedToSavePermissions'))
    } finally {
      setSaving(false)
    }
  }

  const PermissionToggle = ({ 
    label, 
    description, 
    checked, 
    onChange, 
    icon 
  }: { 
    label: string
    description: string
    checked: boolean
    onChange: (checked: boolean) => void
    icon?: React.ReactNode
  }) => (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center space-x-3">
        {icon && <div className="text-gray-500">{icon}</div>}
        <div>
          <div className="font-medium text-sm">{label}</div>
          <div className="text-xs text-gray-500">{description}</div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="rounded"
        />
        <Badge variant={checked ? "default" : "secondary"} className="text-xs">
          {checked ? t('settings.allowed') : t('settings.denied')}
        </Badge>
      </div>
    </div>
  )

  const RolePermissions = ({ role }: { role: 'waiter' | 'admin' }) => {
    if (!permissions) return null

    const rolePerms = permissions[role]

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {role === 'admin' ? <Crown className="h-5 w-5 text-yellow-600" /> : <Users className="h-5 w-5 text-blue-600" />}
            <h3 className="text-lg font-semibold">{role === 'admin' ? t('settings.adminPermissions') : t('settings.waiterPermissions')}</h3>
          </div>
          <Button onClick={() => savePermissions(role)} disabled={saving} size="sm">
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                {t('settings.saving')}
              </>
            ) : (
              <>
                <Save className="h-3 w-3 mr-1" />
                {t('settings.save')}
              </>
            )}
          </Button>
        </div>

        {/* Games Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">🎮 {t('settings.gameManagement')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <PermissionToggle
              label={t('settings.viewAllTables')}
              description={t('settings.viewAllTablesDescription')}
              checked={rolePerms.games.viewAllTables}
              onChange={(value) => updatePermission(role, 'games', 'viewAllTables', value)}
              icon={<Eye className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.viewOwnTablesOnly')}
              description={t('settings.viewOwnTablesOnlyDescription')}
              checked={rolePerms.games.viewOwnTablesOnly}
              onChange={(value) => updatePermission(role, 'games', 'viewOwnTablesOnly', value)}
              icon={<EyeOff className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.createNewGames')}
              description={t('settings.createNewGamesDescription')}
              checked={rolePerms.games.createNewGames}
              onChange={(value) => updatePermission(role, 'games', 'createNewGames', value)}
            />
            <PermissionToggle
              label={t('settings.stopAnyGame')}
              description={t('settings.stopAnyGameDescription')}
              checked={rolePerms.games.stopAnyGame}
              onChange={(value) => updatePermission(role, 'games', 'stopAnyGame', value)}
            />
            <PermissionToggle
              label={t('settings.viewAllHistory')}
              description={t('settings.viewAllHistoryDescription')}
              checked={rolePerms.games.viewAllHistory}
              onChange={(value) => updatePermission(role, 'games', 'viewAllHistory', value)}
            />
          </CardContent>
        </Card>

        {/* Bar Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">☕ {t('settings.barOrders')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <PermissionToggle
              label={t('settings.viewAllOrders')}
              description={t('settings.viewAllOrdersDescription')}
              checked={rolePerms.bar.viewAllOrders}
              onChange={(value) => updatePermission(role, 'bar', 'viewAllOrders', value)}
              icon={<Eye className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.viewOwnOrdersOnly')}
              description={t('settings.viewOwnOrdersOnlyDescription')}
              checked={rolePerms.bar.viewOwnOrdersOnly}
              onChange={(value) => updatePermission(role, 'bar', 'viewOwnOrdersOnly', value)}
              icon={<EyeOff className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.createOrders')}
              description={t('settings.createOrdersDescription')}
              checked={rolePerms.bar.createOrders}
              onChange={(value) => updatePermission(role, 'bar', 'createOrders', value)}
            />
            <PermissionToggle
              label={t('settings.editAnyOrder')}
              description={t('settings.editAnyOrderDescription')}
              checked={rolePerms.bar.editAnyOrder}
              onChange={(value) => updatePermission(role, 'bar', 'editAnyOrder', value)}
              icon={<Edit className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.viewAllHistory')}
              description={t('settings.viewAllHistoryDescription')}
              checked={rolePerms.bar.viewAllHistory}
              onChange={(value) => updatePermission(role, 'bar', 'viewAllHistory', value)}
            />
          </CardContent>
        </Card>

        {/* Analytics Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">📊 {t('settings.analytics')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <PermissionToggle
              label={t('settings.viewAnalytics')}
              description={t('settings.viewAnalyticsDescription')}
              checked={rolePerms.analytics.viewAnalytics}
              onChange={(value) => updatePermission(role, 'analytics', 'viewAnalytics', value)}
            />
            <PermissionToggle
              label={t('settings.viewOwnStats')}
              description={t('settings.viewOwnStatsDescription')}
              checked={rolePerms.analytics.viewOwnStats}
              onChange={(value) => updatePermission(role, 'analytics', 'viewOwnStats', value)}
            />
          </CardContent>
        </Card>

        {/* Settings Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">⚙️ {t('settings.settingsPermissions')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <PermissionToggle
              label={t('settings.accessSettings')}
              description={t('settings.accessSettingsDescription')}
              checked={rolePerms.settings.accessSettings}
              onChange={(value) => updatePermission(role, 'settings', 'accessSettings', value)}
              icon={<SettingsIcon className="h-4 w-4" />}
            />
            <PermissionToggle
              label={t('settings.manageUsers')}
              description={t('settings.manageUsersDescription')}
              checked={rolePerms.settings.manageUsers}
              onChange={(value) => updatePermission(role, 'settings', 'manageUsers', value)}
            />
            <PermissionToggle
              label={t('settings.managePermissions')}
              description={t('settings.managePermissionsDescription')}
              checked={rolePerms.settings.managePermissions}
              onChange={(value) => updatePermission(role, 'settings', 'managePermissions', value)}
            />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (user?.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('settings.accessDenied')}</h3>
          <p className="text-gray-500">{t('settings.onlyAdministratorsCanManage')}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('settings.permissionsManagement')}
        </CardTitle>
        <p className="text-sm text-gray-600">
          {t('settings.permissionsDescription')}
        </p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-sm text-gray-500 ml-2">{t('settings.loadingPermissions')}</p>
          </div>
        ) : (
          <Tabs defaultValue="waiter" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="waiter" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                {t('settings.waiters')}
              </TabsTrigger>
              <TabsTrigger value="admin" className="flex items-center gap-2">
                <Crown className="h-4 w-4" />
                {t('settings.admins')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="waiter" className="mt-6">
              <RolePermissions role="waiter" />
            </TabsContent>

            <TabsContent value="admin" className="mt-6">
              <RolePermissions role="admin" />
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
