'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Shield,
  Activity,
  Database,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  Trash2,
  Play,
  BarChart3,
  Clock,
  Zap
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { useSafeTranslation } from '../hooks/useSafeTranslation'

interface StorageHealthStats {
  cacheHitRate: number
  queueAge: number
  quotaUsage: {
    memory: number
    localStorage: number
    database: number
  }
  performance: {
    avgReadTime: number
    avgWriteTime: number
    errorRate: number
  }
  security: {
    healthScore: number
    criticalEvents: number
    recentTampers: number
  }
  insights: {
    topKeys: Array<{key: string, usage: number}>
    recommendations: string[]
    healthScore: number
  }
  historical?: Array<{
    date: string
    operations: number
    performance: number
    storage: number
  }>
}

export function AdminStorageDashboard() {
  const { t } = useSafeTranslation()
  const [stats, setStats] = useState<StorageHealthStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  useEffect(() => {
    loadStorageStats()

    // Auto-refresh every 5 minutes (reduced from 30 seconds)
    const interval = setInterval(loadStorageStats, 300000)
    return () => clearInterval(interval)
  }, [])

  const loadStorageStats = async () => {
    setIsLoading(true)
    try {
      // Import storage utilities dynamically
      const [
        { storageAnalytics },
        { storageInsights },
        { securityMonitor },
        { offlineManager }
      ] = await Promise.all([
        import('../utils/storage/StorageAnalytics'),
        import('../utils/storage/StorageInsights'),
        import('../utils/storage/SecurityMonitor'),
        import('../utils/storage/OfflineManager')
      ])

      const analyticsStats = storageAnalytics.getStats()
      const insightsData = storageInsights.getInsightsSummary()
      const securityData = securityMonitor.getSecuritySummary()
      const queueStatus = offlineManager.getQueueStatus()

      // Get historical data for charts
      const historicalMetrics = storageInsights.getHistoricalMetrics(7)
      const trends = storageInsights.getGrowthTrends('daily')

      const healthStats: StorageHealthStats = {
        cacheHitRate: analyticsStats.overall.hitRate,
        queueAge: queueStatus.oldestOperation?.timestamp 
          ? Date.now() - queueStatus.oldestOperation.timestamp 
          : 0,
        quotaUsage: {
          memory: (analyticsStats.memory.keys / 1000) * 100, // Assuming 1000 max items
          localStorage: (analyticsStats.localStorage.keys / 500) * 100, // Assuming 500 max items
          database: Math.min((analyticsStats.database.keys / 10000) * 100, 100) // Assuming 10k max items
        },
        performance: {
          avgReadTime: analyticsStats.overall.averageReadTime,
          avgWriteTime: analyticsStats.overall.averageWriteTime,
          errorRate: analyticsStats.overall.errorRate
        },
        security: {
          healthScore: securityData.healthScore,
          criticalEvents: securityData.criticalEvents,
          recentTampers: securityData.recentTamperAttempts
        },
        insights: {
          topKeys: insightsData.topKeys.map(key => ({
            key: key.key,
            usage: key.frequency
          })),
          recommendations: insightsData.recommendations,
          healthScore: insightsData.healthScore
        },
        historical: trends.data.map(day => ({
          date: new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          operations: day.operations,
          performance: day.performance,
          storage: day.storage
        }))
      }

      setStats(healthStats)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to load storage stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearCache = async () => {
    try {
      const { clearAllStorage } = await import('../utils/storage')
      await clearAllStorage()
      await loadStorageStats()
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }

  const handleRetryFailedOps = async () => {
    try {
      const { offlineManager } = await import('../utils/storage/OfflineManager')
      await offlineManager.forceSync()
      await loadStorageStats()
    } catch (error) {
      console.error('Failed to retry operations:', error)
    }
  }

  const handleRunStressTests = async () => {
    try {
      const { stressTests } = await import('../utils/storage/StressTests')
      await stressTests.runAllTests()
      console.log('Stress tests completed. Check console for results.')
    } catch (error) {
      console.error('Failed to run stress tests:', error)
    }
  }

  const handleRunComprehensiveTests = async () => {
    try {
      const { storageTestSuite } = await import('../utils/storage/StorageTestSuite')
      const results = await storageTestSuite.runFullTestSuite()
      const summary = storageTestSuite.getTestSummary()
      console.log('Comprehensive test suite completed:', summary)
      console.log('Detailed results:', results)
    } catch (error) {
      console.error('Failed to run comprehensive tests:', error)
    }
  }

  const handleExportLogs = async () => {
    try {
      const [
        { storageAnalytics },
        { storageInsights },
        { securityMonitor }
      ] = await Promise.all([
        import('../utils/storage/StorageAnalytics'),
        import('../utils/storage/StorageInsights'),
        import('../utils/storage/SecurityMonitor')
      ])

      const exportData = {
        analytics: storageAnalytics.getStats(),
        insights: storageInsights.exportInsights(),
        security: securityMonitor.exportSecurityData(),
        timestamp: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `storage-logs-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export logs:', error)
    }
  }

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getHealthBadge = (score: number) => {
    if (score >= 90) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>
    if (score >= 70) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Good</Badge>
    return <Badge variant="destructive">Needs Attention</Badge>
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('storage.title')} - Admin Dashboard</h2>
          {lastUpdated && (
            <p className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <Button onClick={loadStorageStats} disabled={isLoading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
          <Button onClick={handleExportLogs} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </Button>
        </div>
      </div>

      {/* Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.cacheHitRate.toFixed(1)}%</div>
            <Progress value={stats.cacheHitRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Health</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthColor(stats.security.healthScore)}`}>
              {stats.security.healthScore}
            </div>
            {getHealthBadge(stats.security.healthScore)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.performance.avgReadTime.toFixed(1)}ms
            </div>
            <p className="text-xs text-muted-foreground">Avg read time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queue Status</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.queueAge > 0 ? `${Math.round(stats.queueAge / 1000)}s` : '0s'}
            </div>
            <p className="text-xs text-muted-foreground">Oldest queued item</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="quotas">Quotas</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Average Read Time</span>
                  <span className="font-mono">{stats.performance.avgReadTime.toFixed(2)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Average Write Time</span>
                  <span className="font-mono">{stats.performance.avgWriteTime.toFixed(2)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Error Rate</span>
                  <span className="font-mono">{stats.performance.errorRate.toFixed(2)}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Keys by Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.insights.topKeys.slice(0, 5).map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="truncate font-mono">{item.key}</span>
                      <span className="text-muted-foreground">{item.usage}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Operations Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Operations Trend (7 Days)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={stats.historical || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="operations"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ fill: '#3b82f6' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Performance Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Performance Trend (7 Days)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={stats.historical || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}ms`, 'Avg Response Time']} />
                      <Line
                        type="monotone"
                        dataKey="performance"
                        stroke="#10b981"
                        strokeWidth={2}
                        dot={{ fill: '#10b981' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Storage Usage Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Storage Usage (7 Days)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={stats.historical || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} bytes`, 'Storage Used']} />
                      <Bar dataKey="storage" fill="#8b5cf6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Weekly Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Weekly Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {stats.historical?.reduce((sum, day) => sum + day.operations, 0) || 0}
                    </div>
                    <div className="text-sm text-blue-600">Total Operations</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {stats.historical?.length ?
                        (stats.historical.reduce((sum, day) => sum + day.performance, 0) / stats.historical.length).toFixed(1)
                        : 0}ms
                    </div>
                    <div className="text-sm text-green-600">Avg Performance</div>
                  </div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {stats.historical?.length ?
                      Math.max(...stats.historical.map(day => day.storage)).toLocaleString()
                      : 0} bytes
                  </div>
                  <div className="text-sm text-purple-600">Peak Storage Usage</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="quotas" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Memory Storage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{stats.quotaUsage.memory.toFixed(1)}%</span>
                  </div>
                  <Progress value={stats.quotaUsage.memory} />
                  <p className="text-xs text-muted-foreground">Max: 1,000 items</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Local Storage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{stats.quotaUsage.localStorage.toFixed(1)}%</span>
                  </div>
                  <Progress value={stats.quotaUsage.localStorage} />
                  <p className="text-xs text-muted-foreground">Max: 500 items</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Database Storage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{stats.quotaUsage.database.toFixed(1)}%</span>
                  </div>
                  <Progress value={stats.quotaUsage.database} />
                  <p className="text-xs text-muted-foreground">Estimated usage</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Health Score</span>
                  <div className="flex items-center gap-2">
                    <span className={`font-bold ${getHealthColor(stats.security.healthScore)}`}>
                      {stats.security.healthScore}
                    </span>
                    {getHealthBadge(stats.security.healthScore)}
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>Critical Events</span>
                  <span className={stats.security.criticalEvents > 0 ? 'text-red-600 font-bold' : ''}>
                    {stats.security.criticalEvents}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Recent Tamper Attempts</span>
                  <span className={stats.security.recentTampers > 0 ? 'text-red-600 font-bold' : ''}>
                    {stats.security.recentTampers}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.insights.recommendations.length > 0 ? (
                    stats.insights.recommendations.map((rec, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                        <span>{rec}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span>No recommendations. System is running optimally.</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Storage Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Overall Health Score</span>
                  <div className="flex items-center gap-2">
                    <span className={`font-bold ${getHealthColor(stats.insights.healthScore)}`}>
                      {stats.insights.healthScore}
                    </span>
                    {getHealthBadge(stats.insights.healthScore)}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Most Active Keys</h4>
                  <div className="space-y-1">
                    {stats.insights.topKeys.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="font-mono text-xs">{item.key}</span>
                        <span className="text-muted-foreground">{item.usage} operations</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cache Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button onClick={handleClearCache} variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Cache
                </Button>
                <Button onClick={handleRetryFailedOps} className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Failed Operations
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Testing & Diagnostics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button onClick={handleRunStressTests} variant="outline" className="w-full">
                  <Play className="h-4 w-4 mr-2" />
                  Run Stress Tests
                </Button>
                <Button onClick={handleRunComprehensiveTests} variant="outline" className="w-full">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Run Full Test Suite
                </Button>
                <Button onClick={handleExportLogs} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Logs & Metrics
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
