'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>u, X, User, Settings, LogOut, ChevronDown, Users, Coffee, BarChart3, Receipt, SettingsIcon } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { LanguageSelector } from './LanguageSelector'

// SVG Billiard Logo Component
const BilliardLogo = ({ className = "w-8 h-8" }: { className?: string }) => (
  <svg 
    viewBox="0 0 32 32" 
    className={className}
    aria-label="BBM Billiard Club"
  >
    <defs>
      <linearGradient id="ballGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#3B82F6" />
        <stop offset="100%" stopColor="#8B5CF6" />
      </linearGradient>
    </defs>
    {/* Billiard ball */}
    <circle 
      cx="16" 
      cy="16" 
      r="14" 
      fill="url(#ballGradient)" 
      stroke="white" 
      strokeWidth="1"
    />
    {/* Number 8 */}
    <text 
      x="16" 
      y="20" 
      textAnchor="middle" 
      className="fill-white font-bold text-sm"
      style={{ fontSize: '12px' }}
    >
      8
    </text>
    {/* Highlight */}
    <ellipse 
      cx="12" 
      cy="10" 
      rx="3" 
      ry="2" 
      fill="white" 
      opacity="0.3"
    />
  </svg>
)

interface NavigationItem {
  label: string
  href: string
  isActive?: boolean
}

interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
  profilePicture?: string
}

interface AppleHeaderProps {
  navigationItems?: NavigationItem[]
  onNavigationClick?: (href: string) => void
  user?: User | null
  onUserProfileOpen?: () => void
  onLogout?: () => void
  className?: string
}

const defaultNavigationItems: NavigationItem[] = [
  { label: 'Tables', href: 'tables' },
  { label: 'Bar', href: 'bar' },
  { label: 'Analytics', href: 'analytics' },
  { label: 'Receipts', href: 'receipts' },
  { label: 'Settings', href: 'settings' },
]

// Icon mapping for navigation items
const getNavigationIcon = (href: string) => {
  const iconMap = {
    'tables': Users,
    'games': Users,
    'bar': Coffee,
    'analytics': BarChart3,
    'receipts': Receipt,
    'settings': SettingsIcon,
  }
  return iconMap[href as keyof typeof iconMap] || Users
}

export function AppleHeader({
  navigationItems = defaultNavigationItems,
  onNavigationClick,
  user,
  onUserProfileOpen,
  onLogout,
  className = ''
}: AppleHeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Scroll detection for background opacity
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false)
      }
    }

    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
      return () => {
        document.removeEventListener('keydown', handleEscape)
        document.body.style.overflow = 'unset'
      }
    }
  }, [isMobileMenuOpen])

  const handleNavigationClick = (href: string) => {
    onNavigationClick?.(href)
    setIsMobileMenuOpen(false)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleLogout = () => {
    onLogout?.()
  }

  return (
    <>
      {/* Main Header */}
      <header
        className={`
          fixed top-0 left-0 right-0 z-50
          ${isScrolled
            ? 'bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl border-b border-gray-200/20 dark:border-gray-800/20'
            : 'bg-white/80 dark:bg-gray-950/80 backdrop-blur-lg'
          }
          ${className}
        `}
        role="banner"
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-11">

            {/* Logo */}
            <div className="flex items-center">
              <button
                onClick={() => window.location.reload()}
                className="flex items-center space-x-2 p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                aria-label="Refresh BBM Billiard Club"
              >
                <BilliardLogo />
                <span className="font-semibold text-gray-900 dark:text-white text-lg tracking-tight">
                  BBM
                </span>
              </button>
            </div>

            {/* Desktop Navigation */}
            <nav
              className="hidden md:flex items-center space-x-1"
              role="navigation"
              aria-label="Main navigation"
            >
              {navigationItems.map((item) => {
                const Icon = getNavigationIcon(item.href)
                return (
                  <button
                    key={item.href}
                    onClick={() => handleNavigationClick(item.href)}
                    className={`
                      relative flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg
                      hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-gray-800/50
                      focus:outline-none focus:ring-2 focus:ring-blue-500/20
                      ${item.isActive
                        ? 'text-gray-900 dark:text-white'
                        : 'text-gray-600 dark:text-gray-300'
                      }
                    `}
                    aria-current={item.isActive ? 'page' : undefined}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                    {item.isActive && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-blue-600 dark:bg-blue-400 rounded-full" />
                    )}
                  </button>
                )
              })}
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-3">
              {/* Language Selector - Desktop */}
              <div className="hidden md:block">
                <div className="p-2 rounded-xl hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-colors duration-200">
                  <LanguageSelector />
                </div>
              </div>

              {/* User Menu - Desktop */}
              {user && (
                <div className="hidden md:block">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="flex items-center space-x-2 h-8 px-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                        aria-label="User menu"
                      >
                        <Avatar className="w-6 h-6">
                          <AvatarImage src={user.profilePicture} />
                          <AvatarFallback className="bg-blue-600 text-white text-xs font-medium">
                            {user.fullName ? getInitials(user.fullName) : "U"}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-gray-900 dark:text-white max-w-20 truncate">
                          {user.fullName || user.username}
                        </span>
                        <ChevronDown className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-48 bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl border border-gray-200/50 dark:border-gray-800/50 shadow-xl rounded-xl"
                      sideOffset={8}
                    >
                      <div className="px-3 py-2 border-b border-gray-200/50 dark:border-gray-800/50">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{user.fullName}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{user.role}</p>
                      </div>
                      <div className="p-1">
                        <DropdownMenuItem
                          onClick={onUserProfileOpen}
                          className="rounded-lg px-3 py-2 hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-colors duration-200 cursor-pointer"
                        >
                          <User className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Profile</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleNavigationClick('settings')}
                          className="rounded-lg px-3 py-2 hover:bg-gray-100/60 dark:hover:bg-gray-800/60 transition-colors duration-200 cursor-pointer"
                        >
                          <Settings className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Settings</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="my-1 bg-gray-200/50 dark:bg-gray-800/50" />
                        <DropdownMenuItem
                          onClick={handleLogout}
                          className="rounded-lg px-3 py-2 hover:bg-red-50/80 dark:hover:bg-red-900/20 transition-colors duration-200 cursor-pointer"
                        >
                          <LogOut className="mr-2 h-4 w-4 text-red-500 dark:text-red-400" />
                          <span className="text-sm font-medium text-red-600 dark:text-red-400">Logout</span>
                        </DropdownMenuItem>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 rounded-lg hover:bg-gray-100/60 dark:hover:bg-gray-800/60 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
                aria-expanded={isMobileMenuOpen}
                aria-controls="mobile-menu"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                )}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          id="mobile-menu"
          className="fixed top-11 left-0 right-0 z-40 md:hidden bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl border-b border-gray-200/20 dark:border-gray-800/20"
          role="navigation"
          aria-label="Mobile navigation"
        >
        <div className="max-w-6xl mx-auto px-4 py-4">
          {/* Mobile Navigation */}
          <nav className="space-y-1 mb-6">
            {navigationItems.map((item) => {
              const Icon = getNavigationIcon(item.href)
              return (
                <button
                  key={item.href}
                  onClick={() => handleNavigationClick(item.href)}
                  className={`
                    w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium
                    focus:outline-none focus:ring-2 focus:ring-blue-500/20
                    ${item.isActive
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white'
                    }
                  `}
                  aria-current={item.isActive ? 'page' : undefined}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.label}</span>
                  {item.isActive && (
                    <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full" />
                  )}
                </button>
              )
            })}
          </nav>

          {/* Mobile Language Selector - Always visible */}
          {!user && (
            <div className="border-t border-gray-200/50 dark:border-gray-800/50 pt-4 pb-2">
              <div className="flex items-center justify-center">
                <div className="p-2 rounded-xl bg-gray-50/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <LanguageSelector />
                </div>
              </div>
            </div>
          )}

          {/* Mobile User Section */}
          {user && (
            <div className="border-t border-gray-200/50 dark:border-gray-800/50 pt-6">
              <div className="flex items-center space-x-3 px-4 py-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg mb-4">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={user.profilePicture} />
                  <AvatarFallback className="bg-blue-600 text-white font-medium">
                    {user.fullName ? getInitials(user.fullName) : "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{user.fullName}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{user.role}</p>
                </div>
                <div className="p-2 rounded-xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm">
                  <LanguageSelector />
                </div>
              </div>

              <div className="space-y-1">
                <button
                  onClick={() => {
                    onUserProfileOpen?.()
                    setIsMobileMenuOpen(false)
                  }}
                  className="w-full text-left px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                >
                  <User className="inline mr-2 h-4 w-4" />
                  Profile
                </button>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-3 rounded-lg text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-red-500/20"
                >
                  <LogOut className="inline mr-2 h-4 w-4" />
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
        </div>
      )}
    </>
  )
}

export default AppleHeader
