"use client"

import { useState, useRef, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, Play, Trash2, Volume2, ChevronDown, ChevronUp } from 'lucide-react'
import { useSafeTranslation } from '../hooks/useSafeTranslation'

interface SoundUploaderProps {
  value: string
  onChange: (url: string) => void
  label?: string
  placeholder?: string
  disabled?: boolean
}

interface SoundFile {
  filename: string
  url: string
  size: number
  modified: string
  extension: string
}

export function SoundUploader({
  value,
  onChange,
  label,
  placeholder,
  disabled = false
}: SoundUploaderProps) {
  const { t } = useSafeTranslation()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [availableSounds, setAvailableSounds] = useState<SoundFile[]>([])
  const [showSoundLibrary, setShowSoundLibrary] = useState(false)
  const [isLoadingSounds, setIsLoadingSounds] = useState(false)
  const [deletingSound, setDeletingSound] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Use translations with fallbacks
  const displayLabel = label || t('settings.customAlertSound')
  const displayPlaceholder = placeholder || t('settings.uploadSoundFile')

  // Load available sounds from server
  const loadAvailableSounds = async () => {
    setIsLoadingSounds(true)
    try {
      const response = await fetch('/api/sounds')
      if (response.ok) {
        const data = await response.json()
        setAvailableSounds(data.files || [])
      }
    } catch (error) {
      console.error('Failed to load available sounds:', error)
    } finally {
      setIsLoadingSounds(false)
    }
  }

  // Load sounds when component mounts or when library is opened
  useEffect(() => {
    if (showSoundLibrary && availableSounds.length === 0) {
      loadAvailableSounds()
    }
  }, [showSoundLibrary])

  // Delete a sound file
  const deleteSound = async (filename: string) => {
    setDeletingSound(filename)
    try {
      const response = await fetch(`/api/sounds?filename=${encodeURIComponent(filename)}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Remove from local state
        setAvailableSounds(prev => prev.filter(sound => sound.filename !== filename))

        // If the deleted sound was currently selected, clear it
        if (value === `/sounds/${filename}`) {
          onChange('')
        }
      } else {
        const result = await response.json()
        setUploadError(result.error || t('settings.failedToDeleteSound'))
      }
    } catch (error) {
      console.error('Delete error:', error)
      setUploadError(t('settings.failedToDeleteSound'))
    } finally {
      setDeletingSound(null)
    }
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    setUploadError(null)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload-sound', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (response.ok) {
        onChange(result.url)
        console.log('Sound uploaded successfully:', result)
        // Refresh the sound library
        if (showSoundLibrary) {
          loadAvailableSounds()
        }
      } else {
        setUploadError(result.error || t('settings.failedToUploadSound'))
      }
    } catch (error) {
      console.error('Upload error:', error)
      setUploadError(t('settings.failedToUploadSound'))
    } finally {
      setIsUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleTestSound = () => {
    if (!value) return
    
    try {
      const audio = new Audio(value)
      audio.volume = 0.3
      audio.play().catch(err => {
        console.error('Failed to play sound:', err)
        setUploadError(t('settings.failedToPlaySound'))
      })
    } catch (error) {
      console.error('Error creating audio:', error)
      setUploadError(t('settings.invalidSoundFile'))
    }
  }

  const handleClearSound = () => {
    onChange('')
    setUploadError(null)
  }

  const getFileName = (url: string) => {
    if (url.startsWith('/sounds/')) {
      const fileName = url.split('/').pop() || ''
      // Remove timestamp prefix if present
      return fileName.replace(/^\d+_/, '')
    }
    return url.split('/').pop() || url
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="soundUploader">{displayLabel}</Label>
      
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* URL Input */}
      <div className="flex gap-2">
        <Input
          id="soundUploader"
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={displayPlaceholder}
          disabled={disabled}
          className="flex-1"
        />
        
        {/* Upload Button */}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleUploadClick}
          disabled={disabled || isUploading}
          className="px-3"
        >
          <Upload className="h-4 w-4" />
        </Button>
      </div>

      {/* Currently Assigned Sound Display */}
      {value && value.trim() !== '' && (
        <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium text-green-800 dark:text-green-200">
                  {t('settings.currentlyAssigned')}
                </div>
                <div className="text-xs text-green-600 dark:text-green-400">
                  {value.startsWith('/sounds/') ? getFileName(value) : value}
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleTestSound}
                disabled={disabled}
                className="h-7 w-7 p-0 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                title={t('settings.testSound')}
              >
                <Play className="h-3 w-3" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClearSound}
                disabled={disabled}
                className="h-7 w-7 p-0 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20"
                title={t('settings.removeSound')}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Upload Status */}
      {isUploading && (
        <p className="text-xs text-blue-600">
          {t('settings.uploadingSoundFile')}
        </p>
      )}

      {/* Error Message */}
      {uploadError && (
        <p className="text-xs text-red-600">
          {uploadError}
        </p>
      )}

      {/* Sound Library Toggle */}
      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowSoundLibrary(!showSoundLibrary)}
          disabled={disabled}
          className="text-xs text-blue-600 hover:text-blue-700 p-0 h-auto"
        >
          {showSoundLibrary ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              {t('settings.hideSoundLibrary')}
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              {t('settings.showSoundLibrary')} ({availableSounds.length})
            </>
          )}
        </Button>
      </div>

      {/* Sound Library */}
      {showSoundLibrary && (
        <div className="border rounded-lg p-3 bg-gray-50 dark:bg-gray-800 space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">{t('settings.previouslyUploadedSounds')}</h4>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={loadAvailableSounds}
              disabled={disabled || isLoadingSounds}
              className="text-xs"
            >
              {isLoadingSounds ? t('common.loading') : t('common.refresh')}
            </Button>
          </div>

          {availableSounds.length === 0 ? (
            <p className="text-xs text-gray-500 text-center py-4">
              {t('settings.noSoundFilesUploaded')}
            </p>
          ) : (
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {availableSounds.map((sound) => {
                const isSelected = value === sound.url
                const isDeleting = deletingSound === sound.filename

                return (
                  <div
                    key={sound.filename}
                    className={`flex items-center justify-between p-3 rounded border text-xs transition-colors cursor-pointer hover:shadow-sm ${
                      isSelected
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                        : 'bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                    onClick={() => !isDeleting && onChange(sound.url)}
                    title={isSelected ? t('settings.currentlySelected') : t('settings.clickToSelect')}
                  >
                    <div className="flex-1 min-w-0" onClick={(e) => e.stopPropagation()}>
                      <div className="font-medium truncate flex items-center gap-2">
                        <span
                          className="cursor-pointer hover:text-blue-600 transition-colors"
                          onClick={() => !isDeleting && onChange(sound.url)}
                          title={t('settings.clickToSelect')}
                        >
                          {sound.filename.replace(/^\d+_/, '')}
                        </span>
                        {isSelected && (
                          <span className="text-blue-600 text-xs font-normal bg-blue-100 dark:bg-blue-900/30 px-2 py-0.5 rounded">
                            ✓ {t('settings.selected')}
                          </span>
                        )}
                      </div>
                      <div className="text-gray-500 text-xs">
                        {(sound.size / 1024).toFixed(1)} KB • {sound.extension.toUpperCase()}
                        {!isSelected && (
                          <span className="text-blue-500 ml-2">
                            • {t('settings.clickToUse')}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1 ml-2" onClick={(e) => e.stopPropagation()}>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          const audio = new Audio(sound.url)
                          audio.volume = 0.3
                          audio.play().catch(err => console.error('Failed to play:', err))
                        }}
                        className="h-7 w-7 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                        title={t('settings.testSound')}
                        disabled={isDeleting}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          onChange(sound.url)
                        }}
                        className={`h-7 w-7 p-0 ${
                          isSelected
                            ? 'text-blue-600 bg-blue-100 dark:bg-blue-900/20'
                            : 'text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20'
                        }`}
                        title={t('settings.useThisSound')}
                        disabled={isDeleting}
                      >
                        <Volume2 className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteSound(sound.filename)
                        }}
                        className="h-7 w-7 p-0 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20"
                        title={t('settings.deleteSound')}
                        disabled={isDeleting}
                      >
                        {isDeleting ? (
                          <div className="h-3 w-3 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                        ) : (
                          <Trash2 className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      )}

      {/* Help Text */}
      <p className="text-xs text-gray-500">
        {t('settings.soundFileDescription')}
        {value && value.startsWith('/sounds/') && ` ${t('settings.soundFileStoredLocally')}`}
      </p>
    </div>
  )
}
