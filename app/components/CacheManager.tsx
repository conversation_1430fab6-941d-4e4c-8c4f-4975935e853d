"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Trash2, RefreshCw, Database, HardDrive, Wifi, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import { clearAllStorage, getStorageStats, getStorageManager, storageAnalytics, storageDevTools, offlineManager, stressTests } from '../utils/storage'
import { AdminStorageDashboard } from './AdminStorageDashboard'
import { useAuth } from '../contexts/AuthContext'

interface CacheStats {
  memory: {
    size: number
    keys: number
    items: string[]
  }
  localStorage: {
    size: number
    keys: number
    items: string[]
  }
  database: {
    size: number
    keys: number
    items: string[]
  }
}

interface ServerCacheStats {
  size: number
  hitRate: number
}

export function CacheManager() {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null)
  const [serverCacheStats, setServerCacheStats] = useState<ServerCacheStats | null>(null)
  const [validationResult, setValidationResult] = useState<{ valid: boolean; errors: string[] } | null>(null)
  const [preserveAuth, setPreserveAuth] = useState(true)
  const [isOnline, setIsOnline] = useState(offlineManager.isCurrentlyOnline())
  const [queueStatus, setQueueStatus] = useState(offlineManager.getQueueStatus())
  const [showAdminDashboard, setShowAdminDashboard] = useState(false)

  // Load cache statistics
  const loadCacheStats = async () => {
    try {
      // Get unified storage stats
      const stats = await getStorageStats()
      setCacheStats(stats)

      // Get server-side cache stats (admin only)
      if (user?.role === 'admin') {
        const response = await fetch('/api/cache/clear', {
          method: 'GET',
          credentials: 'include' // Include cookies for authentication
        })

        if (response.ok) {
          const data = await response.json()
          setServerCacheStats(data.stats)
        }
      }

      // Storage validation is now built into the unified system
      setValidationResult({ valid: true, errors: [] })
    } catch (error) {
      console.error('Failed to load cache stats:', error)
      setValidationResult({ valid: false, errors: [error.message] })
    }
  }

  useEffect(() => {
    loadCacheStats()

    // Set up offline status monitoring
    const unsubscribe = offlineManager.addStatusListener((online) => {
      setIsOnline(online)
      setQueueStatus(offlineManager.getQueueStatus())
    })

    // Update queue status periodically
    const interval = setInterval(() => {
      setQueueStatus(offlineManager.getQueueStatus())
    }, 5000)

    return () => {
      unsubscribe()
      clearInterval(interval)
    }
  }, [user])

  const handleClearLocalStorage = async () => {
    setIsLoading(true)
    try {
      const localStorage = getStorageManager('local')
      await localStorage.clear()
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear localStorage:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearMemoryStorage = async () => {
    setIsLoading(true)
    try {
      const memoryStorage = getStorageManager('memory')
      await memoryStorage.clear()
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear memory storage:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearServerCache = async () => {
    if (user?.role !== 'admin') return

    setIsLoading(true)
    try {
      const response = await fetch('/api/cache/clear', {
        method: 'POST',
        credentials: 'include'
      })
      if (!response.ok) throw new Error('Failed to clear server cache')
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear server cache:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearAllCaches = async () => {
    setIsLoading(true)
    try {
      if (!preserveAuth) {
        await clearAllStorage()
      } else {
        // Clear only non-auth storage
        const localStorage = getStorageManager('local')
        const memoryStorage = getStorageManager('memory')
        await localStorage.clear()
        await memoryStorage.clear()
      }
      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear all caches:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearBarMenuData = async () => {
    setIsLoading(true)
    try {
      const memoryStorage = getStorageManager('memory')
      const localStorage = getStorageManager('local')

      // Clear bar menu related keys
      const barMenuKeys = ['temp_draft_order', 'ui_selected_table', 'ui_selected_category', 'ui_search_query']
      for (const key of barMenuKeys) {
        await memoryStorage.delete(key)
        await localStorage.delete(key)
      }

      await loadCacheStats()
    } catch (error) {
      console.error('Failed to clear bar menu data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Cache Management
            <div className="ml-auto flex items-center gap-2">
              <Badge variant={isOnline ? "default" : "destructive"} className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
                {isOnline ? 'Online' : 'Offline'}
              </Badge>
              {queueStatus.queueLength > 0 && (
                <Badge variant="outline" className="text-xs">
                  {queueStatus.queueLength} queued
                </Badge>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Cache Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                Memory Storage
              </h4>
              {cacheStats && (
                <div className="text-sm space-y-1">
                  <div>Size: <Badge variant="outline">{formatBytes(cacheStats.memory.size)}</Badge></div>
                  <div>Keys: <Badge variant="outline">{cacheStats.memory.keys}</Badge></div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                Local Storage
              </h4>
              {cacheStats && (
                <div className="text-sm space-y-1">
                  <div>Size: <Badge variant="outline">{formatBytes(cacheStats.localStorage.size)}</Badge></div>
                  <div>Keys: <Badge variant="outline">{cacheStats.localStorage.keys}</Badge></div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Database className="h-4 w-4" />
                Database Storage
              </h4>
              {cacheStats && (
                <div className="text-sm space-y-1">
                  <div>Size: <Badge variant="outline">{formatBytes(cacheStats.database.size)}</Badge></div>
                  <div>Keys: <Badge variant="outline">{cacheStats.database.keys}</Badge></div>
                </div>
              )}
            </div>
          </div>

          {/* Server Cache Stats (Admin Only) */}
          {user?.role === 'admin' && serverCacheStats && (
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Database className="h-4 w-4" />
                Server Cache
              </h4>
              <div className="text-sm space-y-1">
                <div>Entries: <Badge variant="outline">{serverCacheStats.size}</Badge></div>
                <div>Hit Rate: <Badge variant="outline">{serverCacheStats.hitRate}%</Badge></div>
              </div>
            </div>
          )}

          <Separator />

          {/* Validation Status */}
          {validationResult && (
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                {validationResult.valid ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                )}
                Storage Validation
              </h4>
              {validationResult.valid ? (
                <Badge variant="outline" className="text-green-600">All storage is valid</Badge>
              ) : (
                <div className="space-y-1">
                  <Badge variant="destructive">Issues detected</Badge>
                  {validationResult.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600">{error}</div>
                  ))}
                </div>
              )}
            </div>
          )}

          <Separator />

          {/* Cache Clear Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="preserve-auth"
                checked={preserveAuth}
                onCheckedChange={setPreserveAuth}
              />
              <Label htmlFor="preserve-auth">Preserve authentication when clearing</Label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button
                onClick={handleClearMemoryStorage}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Wifi className="h-4 w-4" />
                Clear Memory Storage
              </Button>

              <Button
                onClick={handleClearLocalStorage}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <HardDrive className="h-4 w-4" />
                Clear Local Storage
              </Button>

              <Button
                onClick={handleClearBarMenuData}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Clear Bar Menu Data
              </Button>

              {user?.role === 'admin' && (
                <Button
                  onClick={handleClearServerCache}
                  disabled={isLoading}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Database className="h-4 w-4" />
                  Clear Server Cache
                </Button>
              )}
            </div>



            {/* Clear All Button */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear All Caches
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Clear All Caches</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will clear all cached data including local storage, session storage, and server cache.
                    {preserveAuth ? ' Your authentication will be preserved.' : ' You will be logged out.'}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleClearAllCaches}>
                    Clear All
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Emergency Reset Button */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full flex items-center gap-2"
                >
                  <AlertTriangle className="h-4 w-4" />
                  Emergency Reset
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Emergency Reset</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will clear ALL data and reload the page. Use this only if the application is not working properly.
                    You will be logged out and all unsaved data will be lost.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={async () => {
                    await clearAllStorage()
                    window.location.reload()
                  }} className="bg-red-600 hover:bg-red-700">
                    Emergency Reset
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          {/* Analytics and Dev Tools */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              onClick={() => storageAnalytics.logStats()}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Info className="h-4 w-4" />
              Log Analytics
            </Button>

            <Button
              onClick={() => storageDevTools.logStorageState()}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Inspect Storage
            </Button>

            <Button
              onClick={() => offlineManager.forceSync()}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Force Sync
            </Button>

            <Button
              onClick={() => storageDevTools.testPerformance()}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Test Performance
            </Button>

            <Button
              onClick={() => stressTests.runAllTests()}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <AlertTriangle className="h-4 w-4" />
              Run Stress Tests
            </Button>
          </div>

          {/* Admin Dashboard Toggle */}
          {user?.role === 'admin' && (
            <div className="mt-4">
              <Button
                onClick={() => setShowAdminDashboard(!showAdminDashboard)}
                variant={showAdminDashboard ? "default" : "outline"}
                className="w-full"
              >
                <Database className="h-4 w-4 mr-2" />
                {showAdminDashboard ? 'Hide' : 'Show'} Admin Dashboard
              </Button>
            </div>
          )}

          {/* Refresh Stats Button */}
          <Button
            onClick={loadCacheStats}
            disabled={isLoading}
            variant="ghost"
            className="w-full flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh Statistics
          </Button>
        </CardContent>
      </Card>

      {/* Admin Dashboard */}
      {showAdminDashboard && user?.role === 'admin' && (
        <AdminStorageDashboard />
      )}
    </div>
  )
}
