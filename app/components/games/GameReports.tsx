"use client"

import { useState } from "react"
import { useSafeTranslation } from '../../hooks/useSafeTranslation'
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { formatCurrency, type CurrencySettings } from "../../lib/currency"
import { formatLocalizedDate } from "../../utils/dateLocalization"

interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  created_by_name?: string
  created_by_username?: string
  created_by_role?: string
}

interface Order {
  id: string
  table_number: number
  total: number
  items: any[]
  created_at: string
}

interface BusinessInfo {
  name: string
  address: string
  phone: string
  email: string
  vat_number: string
}

interface GameTable {
  id: string
  number: number
  name: string
  isActive: boolean
  hourlyRate: number
  tableType: string
}

interface BarTable {
  id: string
  number: number
  name: string
  isActive: boolean
}

interface GameReportsProps {
  activeGames: Game[]
  todayOrders: Order[]
  monthOrders: Order[]
  currencySettings: CurrencySettings | null
  businessInfo: BusinessInfo | null
  showReportTypeDialog: boolean
  pendingReportType: 'daily' | 'monthly' | null
  reportIncludes: { games: boolean; orders: boolean }
  gameTables: GameTable[]
  barTables: BarTable[]
  onShowReportTypeDialog: (show: boolean) => void
  onSetReportIncludes: (includes: { games: boolean; orders: boolean }) => void
  onFetchTodayOrders: () => void
  onFetchMonthOrders: () => void
}

export function GameReports({
  activeGames,
  todayOrders,
  monthOrders,
  currencySettings,
  businessInfo,
  showReportTypeDialog,
  pendingReportType,
  reportIncludes,
  gameTables,
  barTables,
  onShowReportTypeDialog,
  onSetReportIncludes,
  onFetchTodayOrders,
  onFetchMonthOrders
}: GameReportsProps) {
  const { t, i18n } = useSafeTranslation()
  const [showDailyReport, setShowDailyReport] = useState(false)
  const [showMonthlyReport, setShowMonthlyReport] = useState(false)

  const defaultCurrency = {
    currency: 'Albanian Lek',
    symbol: 'L',
    showDecimals: false,
    taxIncluded: false,
    taxEnabled: true,
    taxRate: 20,
    qrCodeEnabled: false,
    qrCodeUrl: '',
  }

  const currency = currencySettings || defaultCurrency

  // Helper functions to get table names
  const getGameTableName = (number: number) => {
    return gameTables.find(t => t.number === number)?.name || `Table ${number}`
  }

  const getBarTableName = (number: number) => {
    return barTables.find(t => t.number === number)?.name || `Table ${number}`
  }

  const handleGenerateReport = async () => {
    if (!pendingReportType) return

    if (pendingReportType === 'daily') {
      await onFetchTodayOrders()
      setShowDailyReport(true)
    } else {
      await onFetchMonthOrders()
      setShowMonthlyReport(true)
    }
    onShowReportTypeDialog(false)
  }

  const generateReportHTML = (type: 'daily' | 'monthly') => {
    const isDaily = type === 'daily'
    const reportDate = formatLocalizedDate(new Date(), "dd/MM/yyyy", i18n.language)
    
    const games = isDaily 
      ? activeGames.filter((game) =>
          game.status === "completed" &&
          game.endTime &&
          new Date(game.endTime).toDateString() === new Date().toDateString()
        )
      : activeGames.filter((game) => {
          if (game.status !== "completed" || !game.endTime) return false
          const gameDate = new Date(game.endTime)
          const currentDate = new Date()
          return gameDate.getMonth() === currentDate.getMonth() && 
                 gameDate.getFullYear() === currentDate.getFullYear()
        })

    const orders = isDaily ? todayOrders : monthOrders

    const totalGamesCost = reportIncludes.games ? games.reduce((sum, game) => {
      const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
      return sum + cost
    }, 0) : 0

    const totalOrdersCost = reportIncludes.orders ? orders.reduce((sum, order) => {
      const orderTotal = typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0)
      return sum + orderTotal
    }, 0) : 0

    const grandTotal = totalGamesCost + totalOrdersCost

    return `
      <html>
        <head>
          <title>${isDaily ? 'Daily' : 'Monthly'} Report</title>
          <style>
            body { font-family: monospace; font-size: 12px; margin: 20px; }
            .center { text-align: center; }
            .bold { font-weight: bold; }
            .border-bottom { border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; }
            .border-top { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; }
            .section { margin: 15px 0; }
            .item { display: flex; justify-content: space-between; margin: 2px 0; }
          </style>
        </head>
        <body>
          <div class="center border-bottom">
            <div class="bold">${businessInfo?.name || 'BILLIARD CLUB'}</div>
            <div>${isDaily ? 'Daily' : 'Monthly'} Report</div>
            <div>${reportDate}</div>
            ${businessInfo?.phone ? `<div>Tel: ${businessInfo.phone}</div>` : ''}
            ${businessInfo?.address ? `<div>Address: ${businessInfo.address}</div>` : ''}
          </div>

          ${reportIncludes.games ? `
            <div class="section">
              <div class="bold border-bottom">BILLIARD GAMES (${games.length})</div>
              ${games.length === 0 ?
                '<div class="center">No games completed</div>' :
                games.map(game => `
                  <div class="item">
                    <span>${getGameTableName(game.tableNumber)} - ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</span>
                    <span>${formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                  </div>
                `).join('')
              }
              <div class="item bold border-top">
                <span>Games Subtotal:</span>
                <span>${formatCurrency(totalGamesCost, currency)}</span>
              </div>
            </div>
          ` : ''}

          ${reportIncludes.orders ? `
            <div class="section">
              <div class="bold border-bottom">BAR ORDERS (${orders.length})</div>
              ${orders.length === 0 ?
                '<div class="center">No orders completed</div>' :
                orders.map(order => `
                  <div class="item">
                    <span>${getBarTableName(order.table_number)} - ${order.items?.length || 0} items</span>
                    <span>${formatCurrency(order.total, currency)}</span>
                  </div>
                `).join('')
              }
              <div class="item bold border-top">
                <span>Orders Subtotal:</span>
                <span>${formatCurrency(totalOrdersCost, currency)}</span>
              </div>
            </div>
          ` : ''}

          <div class="border-top section">
            <div class="item bold" style="font-size: 14px;">
              <span>${isDaily ? 'DAILY' : 'MONTHLY'} TOTAL:</span>
              <span>${formatCurrency(grandTotal, currency)}</span>
            </div>
          </div>

          <div class="center border-top" style="margin-top: 20px; padding-top: 10px; font-size: 10px;">
            <div>Report generated: ${formatLocalizedDate(new Date(), "dd MMM yyyy HH:mm", i18n.language)}</div>
            ${businessInfo?.vat_number ? `<div>VAT: ${businessInfo.vat_number}</div>` : ''}
          </div>
        </body>
      </html>
    `
  }

  const printReport = (type: 'daily' | 'monthly') => {
    const reportHtml = generateReportHTML(type)
    const printWindow = window.open('', '_blank', 'width=600,height=800')
    if (printWindow) {
      printWindow.document.write(reportHtml)
      printWindow.document.close()
      setTimeout(() => {
        printWindow.print()
        setTimeout(() => printWindow.close(), 100)
      }, 100)
    }
  }

  return (
    <>
      {/* Report Type Selection Dialog */}
      <Dialog open={showReportTypeDialog} onOpenChange={onShowReportTypeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              📊 Generate {pendingReportType === 'daily' ? 'Daily' : 'Monthly'} Report
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Select what to include in your {pendingReportType} report:
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-games"
                    checked={reportIncludes.games}
                    onCheckedChange={(checked) =>
                      onSetReportIncludes({ ...reportIncludes, games: !!checked })
                    }
                  />
                  <label htmlFor="include-games" className="text-sm font-medium">
                    Billiard Games
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-orders"
                    checked={reportIncludes.orders}
                    onCheckedChange={(checked) =>
                      onSetReportIncludes({ ...reportIncludes, orders: !!checked })
                    }
                  />
                  <label htmlFor="include-orders" className="text-sm font-medium">
                    Bar Orders
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => onShowReportTypeDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleGenerateReport}
                disabled={!reportIncludes.games && !reportIncludes.orders}
              >
                Generate Report
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Daily Report Dialog */}
      <Dialog open={showDailyReport} onOpenChange={setShowDailyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📊 Daily Report - {formatLocalizedDate(new Date(), "dd/MM/yyyy", i18n.language)}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
              <div className="text-center border-b border-gray-300 pb-4 mb-4">
                <div className="font-bold text-lg">{businessInfo?.name || 'BILLIARD CLUB'}</div>
                <div className="text-sm">Daily Report</div>
                <div className="text-xs">{formatLocalizedDate(new Date(), "dd/MM/yyyy", i18n.language)}</div>
              </div>
              
              {/* Report content would be rendered here */}
              <div className="text-center text-gray-500">
                Report preview will be displayed here
              </div>
            </div>

            <div className="flex justify-center">
              <Button onClick={() => printReport('daily')}>
                Print Daily Report
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Monthly Report Dialog */}
      <Dialog open={showMonthlyReport} onOpenChange={setShowMonthlyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📅 Monthly Report - {formatLocalizedDate(new Date(), "dd/MM/yyyy", i18n.language)}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
              <div className="text-center border-b border-gray-300 pb-4 mb-4">
                <div className="font-bold text-lg">{businessInfo?.name || 'BILLIARD CLUB'}</div>
                <div className="text-sm">Monthly Report</div>
                <div className="text-xs">{formatLocalizedDate(new Date(), "dd/MM/yyyy", i18n.language)}</div>
              </div>
              
              {/* Report content would be rendered here */}
              <div className="text-center text-gray-500">
                Report preview will be displayed here
              </div>
            </div>

            <div className="flex justify-center">
              <Button onClick={() => printReport('monthly')}>
                Print Monthly Report
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
