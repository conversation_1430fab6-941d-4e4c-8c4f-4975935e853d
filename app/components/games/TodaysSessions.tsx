"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../../hooks/useSafeTranslation'
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Clock, Eye, Printer } from 'lucide-react'
import { formatCurrency, generateQRCode, type CurrencySettings } from "../../lib/currency"
import { formatLocalizedDate, formatLocalizedTime } from "../../utils/dateLocalization"

interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  timeLimit?: number | "unlimited"
  hasAlerted?: boolean
  created_by_name?: string
  created_by_username?: string
  created_by_role?: string
}

interface BusinessInfo {
  name: string
  address: string
  phone: string
  email: string
  vat_number: string
}

interface TodaysSessionsProps {
  activeGames: Game[]
  currencySettings: CurrencySettings | null
  businessInfo: BusinessInfo | null
  height?: number
}

const formatTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`
}

export function TodaysSessions({
  activeGames,
  currencySettings,
  businessInfo,
  height
}: TodaysSessionsProps) {
  const { t, i18n } = useSafeTranslation()
  const [selectedGameForPreview, setSelectedGameForPreview] = useState<Game | null>(null)
  const [gameTables, setGameTables] = useState<any[]>([])

  // Load game tables to get table names
  useEffect(() => {
    const loadGameTables = async () => {
      try {
        const response = await fetch('/api/gametables/user', {
          credentials: 'include'
        })
        if (response.ok) {
          const gameTablesData = await response.json()
          setGameTables(gameTablesData.map((t: any) => ({
            id: t.id.toString(),
            number: t.number,
            name: t.name,
            isActive: t.is_active,
            hourlyRate: t.hourly_rate,
            tableType: t.table_type
          })))
        }
      } catch (error) {
        console.error('Failed to load game tables:', error)
      }
    }
    loadGameTables()
  }, [])

  const getTableName = (number: number) => {
    const table = gameTables.find(t => t.number === number)
    return table?.name || `Table ${number}`
  }

  const today = new Date()
  const todayGames = activeGames.filter((game) =>
    game.status === "completed" &&
    game.endTime &&
    new Date(game.endTime).toDateString() === today.toDateString()
  )

  const totalCost = todayGames.reduce((sum, game) => {
    const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
    return sum + cost
  }, 0)

  const totalMinutes = todayGames.reduce((sum, game) => sum + (game.duration || 0), 0)
  const totalHours = Math.floor(totalMinutes / 60)
  const remainingMinutes = totalMinutes % 60

  const defaultCurrency = {
    currency: 'Albanian Lek',
    symbol: 'L',
    showDecimals: false,
    taxIncluded: false,
    taxEnabled: true,
    taxRate: 20,
    qrCodeEnabled: false,
    qrCodeUrl: '',
  }

  const currency = currencySettings || defaultCurrency

  const handlePrintReceipt = async (game: Game) => {
    // Generate QR code if enabled
    let qrCodeDataURL = ''
    if (currency.qrCodeEnabled) {
      try {
        qrCodeDataURL = await generateQRCode(currency.qrCodeUrl || '', businessInfo)
      } catch (error) {
        console.error('Failed to generate QR code:', error)
      }
    }

    // Generate receipt HTML
    const receiptHtml = `
      <html>
        <head>
          <title>Game Receipt</title>
          <style>
            body { font-family: monospace; font-size: 12px; margin: 20px; }
            .center { text-align: center; }
            .bold { font-weight: bold; }
            .border-bottom { border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; }
            .border-top { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; }
          </style>
        </head>
        <body>
          <div class="center border-bottom">
            <div class="bold">${businessInfo?.name || 'BILLIARD CLUB'}</div>
            <div>${t('games.gameSessionReceipt')}</div>
          </div>
          <div>
            <div>${t('games.receiptNumber')}: G${game.id.toString().padStart(3, "0")}</div>
            <div>Date: ${game.endTime ? formatLocalizedDate(new Date(game.endTime), "MMM dd, yyyy HH:mm", i18n.language) : 'Recently'}</div>
            <div>${t('games.table')}: ${getTableName(game.tableNumber)}</div>
          </div>
          <br>
          <div>
            <div>${t('games.startTime')}: ${formatLocalizedTime(game.startTime, i18n.language)}</div>
            <div>${t('games.endTime')}: ${game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : 'Recently'}</div>
            <div>${t('games.duration')}: ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</div>
          </div>
          <div class="border-top">
            <div class="bold">TOTAL: ${formatCurrency(game.cost, currency)}</div>
          </div>
          ${qrCodeDataURL ? `
            <div style="margin: 15px 0; text-align: center; page-break-inside: avoid;">
              <img src="${qrCodeDataURL}" alt="QR Code" style="width: 80px; height: 80px; display: block; margin: 0 auto; -webkit-print-color-adjust: exact; print-color-adjust: exact;" />
              <p style="font-size: 10px; margin-top: 5px;">
                ${currency.qrCodeUrl ? t('games.scanForMoreInfo') : t('games.scanForContactInfo')}
              </p>
            </div>
          ` : ''}
          ${game.created_by_name ? `<div class="center border-top">${t('games.servedBy')}: ${game.created_by_name}</div>` : ''}
        </body>
      </html>
    `

    // Print the receipt
    const printWindow = window.open('', '_blank', 'width=300,height=400')
    if (printWindow) {
      printWindow.document.write(receiptHtml)
      printWindow.document.close()

      if (qrCodeDataURL) {
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print()
            setTimeout(() => printWindow.close(), 100)
          }, 100)
        }
      } else {
        setTimeout(() => {
          printWindow.print()
          setTimeout(() => printWindow.close(), 100)
        }, 50)
      }
    }
  }

  return (
    <div
      className="bg-white border border-gray-200 rounded-2xl shadow-lg overflow-hidden flex flex-col"
      style={{ height: height ? `${height}px` : 'auto' }}
    >
      {/* Header */}
      <div className="px-3 py-2 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3 text-gray-600" />
            <h3 className="text-xs font-semibold text-gray-900">{t('games.todaysSessions')}</h3>
          </div>
          <div className="flex items-center gap-3 text-xs text-gray-500">
            <span>{todayGames.length} {t('games.gamesCount')}</span>
            <span>•</span>
            <span className="font-semibold text-green-600">
              {formatCurrency(totalCost, currency)}
            </span>
            <span>•</span>
            <span>
              {totalHours > 0 ? `${totalHours}h ${remainingMinutes}m` : `${remainingMinutes}m`}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {todayGames.length === 0 ? (
            <div className="text-center py-4 px-3">
              <Clock className="w-6 h-6 mx-auto mb-1 text-gray-400" />
              <div className="text-xs text-gray-500">{t('games.noGamesToday')}</div>
            </div>
          ) : (
            <table className="w-full text-xs">
              <thead className="bg-gray-50 border-b border-gray-200 sticky top-0">
                <tr>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.tableShort')}</th>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.time')}</th>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.cost')}</th>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.player')}</th>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.end')}</th>
                  <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">•</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {todayGames
                  .sort((a, b) => new Date(b.endTime || b.startTime).getTime() - new Date(a.endTime || a.startTime).getTime())
                  .map((game) => (
                    <tr key={game.id} className="hover:bg-gray-50">
                      <td className="px-2 py-1">
                        <div className="w-4 h-4 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center" title={getTableName(game.tableNumber)}>
                          {game.tableNumber}
                        </div>
                      </td>
                      <td className="px-2 py-1">
                        <div className="text-xs font-medium text-gray-900">
                          {formatTime(game.duration)}
                        </div>
                      </td>
                      <td className="px-2 py-1">
                        <div className="text-xs font-semibold text-green-600">
                          {formatCurrency(game.cost, currency)}
                        </div>
                      </td>
                      <td className="px-2 py-1">
                        <div className="text-xs text-gray-600 truncate max-w-16">
                          {game.created_by_name ? game.created_by_name.split(' ')[0] : t('games.unknown')}
                        </div>
                      </td>
                      <td className="px-2 py-1">
                        <div className="text-xs text-gray-500">
                          {game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : t('games.now')}
                        </div>
                      </td>
                      <td className="px-2 py-1">
                        <div className="flex items-center gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => setSelectedGameForPreview(game)}
                              >
                                <Eye className="h-3 w-3 text-blue-600" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-md">
                              <DialogHeader>
                                <DialogTitle className="flex items-center gap-2">
                                  <div className="w-6 h-6 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center">
                                    {game.tableNumber}
                                  </div>
                                  {getTableName(game.tableNumber)} - {t('games.gameSessionReceipt')}
                                </DialogTitle>
                              </DialogHeader>
                              <div className="space-y-4">
                                {/* Receipt Preview */}
                                <div
                                  className="bg-white border rounded mx-auto"
                                  style={{
                                    fontFamily: "'Courier New', monospace",
                                    fontSize: '12px',
                                    lineHeight: '1.4',
                                    maxWidth: '300px',
                                    padding: '10px'
                                  }}
                                >
                                  {/* Header */}
                                  <div className="text-center border-b-2 border-black pb-2 mb-3">
                                    <div className="font-bold text-base mb-1">
                                      {businessInfo?.name || 'BILLIARD CLUB'}
                                    </div>
                                    <div className="text-xs mb-1">{t('games.gameSessionReceipt')}</div>
                                    {businessInfo?.phone && (
                                      <div className="text-xs mb-1">Tel: {businessInfo.phone}</div>
                                    )}
                                    {businessInfo?.address && (
                                      <div className="text-xs mb-1">Address: {businessInfo.address}</div>
                                    )}
                                  </div>

                                  {/* Receipt Info */}
                                  <div className="mb-3">
                                    <div className="flex justify-between mb-1">
                                      <span>{t('games.receiptNumber')}:</span>
                                      <span>G{game.id.toString().padStart(3, "0")}</span>
                                    </div>
                                    <div className="flex justify-between mb-1">
                                      <span>Date:</span>
                                      <span>{game.endTime ? formatLocalizedDate(new Date(game.endTime), "LLL dd, y", i18n.language) : t('common.today')}</span>
                                    </div>
                                    <div className="flex justify-between mb-1">
                                      <span>{t('games.table')}:</span>
                                      <span>{getTableName(game.tableNumber)}</span>
                                    </div>
                                  </div>

                                  {/* Game Details */}
                                  <div className="mb-3">
                                    <div className="flex justify-between mb-1">
                                      <span>{t('games.startTime')}:</span>
                                      <span>{formatLocalizedTime(game.startTime, i18n.language)}</span>
                                    </div>
                                    <div className="flex justify-between mb-1">
                                      <span>{t('games.endTime')}:</span>
                                      <span>{game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : t('games.now')}</span>
                                    </div>
                                    <div className="flex justify-between mb-1">
                                      <span>{t('games.duration')}:</span>
                                      <span>{Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                    </div>
                                  </div>

                                  {/* Total */}
                                  <div className="border-t border-black pt-2">
                                    <div className="flex justify-between font-bold">
                                      <span>{t('games.total')}:</span>
                                      <span>{formatCurrency(game.cost, currency)}</span>
                                    </div>
                                  </div>


                                </div>

                                {/* Print Button */}
                                <div className="flex justify-center">
                                  <Button
                                    onClick={() => handlePrintReceipt(game)}
                                    className="flex items-center gap-2"
                                  >
                                    <Printer className="h-4 w-4" />
                                    {t('bar.printReceipt')}
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  )
}
