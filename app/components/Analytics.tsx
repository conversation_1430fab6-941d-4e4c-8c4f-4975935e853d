"use client"

import { useState, useEffect, useMemo } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Legend,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Coffee,
  Shield,
  Activity,
  Users,
  Target,
  Zap,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Calendar,
  RefreshCw,
  Download,
  Filter,
  Eye,
  Sparkles,
  Brain,
  Lightbulb
} from "lucide-react"
import { usePermissions } from "../hooks/usePermissions"

// Smart color palette for modern charts
const COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  accent: '#F59E0B',
  danger: '#EF4444',
  purple: '#8B5CF6',
  pink: '#EC4899',
  indigo: '#6366F1',
  teal: '#14B8A6'
}

const CHART_COLORS = [COLORS.primary, COLORS.secondary, COLORS.accent, COLORS.purple, COLORS.pink, COLORS.indigo]

export function Analytics() {
  const { t } = useSafeTranslation()
  const { canViewAnalytics, canViewOwnStats, filterGameData, filterBarData } = usePermissions()
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  // Initialize activeTab from localStorage or default to 'overview'
  const [activeTab, setActiveTab] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedTab = localStorage.getItem('bbm_analytics_active_tab')
      const validTabs = ['overview', 'revenue', 'performance', 'insights']
      if (savedTab && validTabs.includes(savedTab)) {
        return savedTab
      }
    }
    return 'overview'
  })

  // Initialize timeRange from localStorage or default to 'today'
  const [timeRange, setTimeRange] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedRange = localStorage.getItem('bbm_analytics_time_range')
      const validRanges = ['today', 'week', 'month']
      if (savedRange && validRanges.includes(savedRange)) {
        return savedRange
      }
    }
    return 'today'
  })
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Auto-refresh effect with smart intervals
  useEffect(() => {
    fetchAnalyticsData()

    if (autoRefresh) {
      const interval = setInterval(fetchAnalyticsData, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh, timeRange])

  // Save activeTab to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_analytics_active_tab', activeTab)
    }
  }, [activeTab])

  // Save timeRange to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_analytics_time_range', timeRange)
    }
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      console.log(`🔄 Fetching analytics data for timeRange: ${timeRange}`)

      const response = await fetch(`/api/analytics?timeRange=${timeRange}`, {
        credentials: 'include'
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch analytics')
      }
      const analyticsData = await response.json()
      console.log(`📊 Analytics data received for ${timeRange}:`, analyticsData)

      // Convert string numbers to actual numbers for charts
      if (analyticsData.hourlyUsage) {
        analyticsData.hourlyUsage = analyticsData.hourlyUsage.map((item: any) => ({
          ...item,
          games: Number(item.games),
          bar: Number(item.bar),
          total: Number(item.total)
        }))
      }

      if (analyticsData.dailyRevenue) {
        analyticsData.dailyRevenue = analyticsData.dailyRevenue.map((item: any) => ({
          ...item,
          games: Number(item.games),
          bar: Number(item.bar),
          total: Number(item.total)
        }))
      }

      if (analyticsData.weeklyRevenue) {
        analyticsData.weeklyRevenue = analyticsData.weeklyRevenue.map((item: any) => ({
          ...item,
          games: Number(item.games),
          bar: Number(item.bar),
          total: Number(item.total)
        }))
      }

      if (analyticsData.monthlyRevenue) {
        analyticsData.monthlyRevenue = analyticsData.monthlyRevenue.map((item: any) => ({
          ...item,
          games: Number(item.games),
          bar: Number(item.bar),
          total: Number(item.total)
        }))
      }

      if (analyticsData.popularItems) {
        analyticsData.popularItems = analyticsData.popularItems.map((item: any) => ({
          ...item,
          value: Number(item.value)
        }))
      }

      setData(analyticsData)
    } catch (error) {
      console.error('Analytics error:', error)
      setData(null)
    } finally {
      setLoading(false)
    }
  }

  // Get chart data based on time range
  const getChartData = useMemo(() => {
    if (!data) return null

    switch (timeRange) {
      case 'today':
        return {
          revenueData: data.dailyRevenue || [],
          title: t('analytics.todaysRevenue'),
          chartTitle: t('games.todaysRevenueByHour')
        }
      case 'week':
        return {
          revenueData: data.weeklyRevenue || data.dailyRevenue || [],
          title: t('analytics.weeklyRevenue'),
          chartTitle: t('games.weeklyRevenue')
        }
      case 'month':
        return {
          revenueData: data.monthlyRevenue || data.dailyRevenue || [],
          title: t('games.monthlyRevenue'),
          chartTitle: t('games.monthlyRevenue')
        }
      default:
        return {
          revenueData: data.dailyRevenue || [],
          title: t('analytics.todaysRevenue'),
          chartTitle: t('games.revenueOverview')
        }
    }
  }, [data, timeRange, t])

  // Smart insights calculations
  const smartInsights = useMemo(() => {
    if (!data) return null

    const todayRevenue = Number(data.todayStats?.today_revenue || 0)
    const yesterdayRevenue = Number(data.todayStats?.yesterday_revenue || 0)
    const revenueChange = yesterdayRevenue > 0 ? ((todayRevenue - yesterdayRevenue) / yesterdayRevenue) * 100 : 0

    const avgDuration = Number(data.todayStats?.avg_duration || 0)
    const optimalDuration = 45 // minutes
    const efficiencyScore = Math.min((avgDuration / optimalDuration) * 100, 100)

    const tableUtilization = Number(data.performanceMetrics?.table_utilization || 0)
    const peakHour = data.performanceMetrics?.peak_hour || 'N/A'

    return {
      revenueChange,
      revenueChangeText: revenueChange > 0 ? `+${revenueChange.toFixed(1)}%` : `${revenueChange.toFixed(1)}%`,
      revenueChangeColor: revenueChange > 0 ? 'text-green-600' : revenueChange < 0 ? 'text-red-600' : 'text-gray-600',
      efficiencyScore,
      efficiencyText: efficiencyScore > 80 ? t('games.excellent') : efficiencyScore > 60 ? t('games.good') : t('games.needsImprovement'),
      efficiencyColor: efficiencyScore > 80 ? 'text-green-600' : efficiencyScore > 60 ? 'text-yellow-600' : 'text-red-600',
      utilizationLevel: tableUtilization > 70 ? t('games.high') : tableUtilization > 40 ? t('games.medium') : t('games.low'),
      utilizationColor: tableUtilization > 70 ? 'text-green-600' : tableUtilization > 40 ? 'text-yellow-600' : 'text-red-600',
      peakHour,
      isRushHour: new Date().getHours() >= 18 && new Date().getHours() <= 22
    }
  }, [data])

  // Check permissions first - allow access if user can view own stats
  if (!canViewAnalytics() && !canViewOwnStats()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <div className="text-lg text-gray-600 mb-2">{t('analytics.accessDenied')}</div>
          <div className="text-sm text-gray-500">{t('analytics.noPermission')}</div>
        </div>
      </div>
    )
  }

  // Data is already filtered by the server based on user permissions
  const filteredData = data

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                {t('games.smartAnalytics')}
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              </h1>
              <p className="text-sm text-gray-600">Loading {timeRange} data...</p>
            </div>
          </div>
        </div>

        {/* Stats Grid Skeleton */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="bg-gray-50 border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <Card key={i} className="shadow-sm">
              <CardHeader>
                <div className="animate-pulse h-6 bg-gray-200 rounded w-1/3"></div>
              </CardHeader>
              <CardContent>
                <div className="animate-pulse h-[320px] bg-gray-100 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!filteredData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg text-red-600">{t('analytics.loadError')}</div>
      </div>
    )
  }

  const stats = [
    {
      title: t('analytics.todaysRevenue'),
      value: `${Number(filteredData.todayStats?.today_revenue || 0).toFixed(0)} L`,
      change: smartInsights?.revenueChangeText || "+0%",
      changeColor: smartInsights?.revenueChangeColor || "text-gray-600",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-50",
      trend: smartInsights?.revenueChange > 0 ? TrendingUp : TrendingDown,
      insight: smartInsights?.revenueChange > 10 ? t('games.excellentDay') : smartInsights?.revenueChange > 0 ? t('games.growingTrend') : t('games.needsAttention')
    },
    {
      title: t('analytics.averageGameDuration'),
      value: `${Math.round(Number(filteredData.todayStats?.avg_duration || 0))} min`,
      change: smartInsights?.efficiencyText || t('games.good'),
      changeColor: smartInsights?.efficiencyColor || "text-blue-600",
      icon: Clock,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      trend: Activity,
      insight: smartInsights?.efficiencyScore > 80 ? t('games.optimalTiming') : t('games.roomForImprovement')
    },
    {
      title: t('analytics.peakHours'),
      value: filteredData.performanceMetrics?.peak_hour || "N/A",
      change: smartInsights?.isRushHour ? t('games.rushHourNow') : t('games.normalTime'),
      changeColor: smartInsights?.isRushHour ? "text-red-600" : "text-purple-600",
      icon: Target,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      trend: Zap,
      insight: smartInsights?.isRushHour ? t('games.peakTimeNow') : t('games.planForPeak')
    },
    {
      title: t('analytics.barOrders'),
      value: `${Number(filteredData.todayStats?.order_count || 0)} ${t('analytics.orders')}`,
      change: smartInsights?.utilizationLevel || t('games.medium'),
      changeColor: smartInsights?.utilizationColor || "text-orange-600",
      icon: Coffee,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      trend: Users,
      insight: Number(filteredData.todayStats?.order_count || 0) > 20 ? "🎉 " + t('games.excellentDay') : "☕ " + t('games.stable')
    },
  ]

  return (
    <div className="space-y-6">
      {/* Smart Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {t('games.smartAnalytics')}
              <Sparkles className="h-5 w-5 text-yellow-500" />
            </h1>
            <p className="text-sm text-gray-600">
              {t('games.realTimeInsights')} • {t('games.lastUpdated')}: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Time Range Selector */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { key: 'today', label: t('games.today'), icon: '📅' },
              { key: 'week', label: t('games.week'), icon: '📊' },
              { key: 'month', label: t('games.month'), icon: '📈' }
            ].map((range) => (
              <Button
                key={range.key}
                variant={timeRange === range.key ? "default" : "ghost"}
                size="sm"
                onClick={() => setTimeRange(range.key)}
                disabled={loading}
                className={`text-xs flex items-center gap-1 ${
                  timeRange === range.key
                    ? 'bg-white shadow-sm text-blue-600 font-medium'
                    : 'hover:bg-white/50'
                }`}
              >
                <span>{range.icon}</span>
                {range.label}
                {loading && timeRange === range.key && (
                  <div className="animate-spin rounded-full h-3 w-3 border border-blue-600 border-t-transparent"></div>
                )}
              </Button>
            ))}
          </div>

          {/* Auto Refresh Toggle */}
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className="flex items-center gap-1"
          >
            <RefreshCw className={`h-3 w-3 ${autoRefresh ? 'animate-spin' : ''}`} />
            {t('games.auto')}
          </Button>

          {/* Manual Refresh */}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchAnalyticsData}
            disabled={loading}
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Smart Stats Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index} className={`${stat.bgColor} border-0 shadow-sm hover:shadow-md transition-all duration-200`}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <stat.icon className={`h-5 w-5 ${stat.color}`} />
                    <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                      {stat.title}
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </div>
                  <div className="flex items-center gap-2">
                    <stat.trend className={`h-3 w-3 ${stat.changeColor}`} />
                    <span className={`text-sm font-medium ${stat.changeColor}`}>
                      {stat.change}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {stat.insight}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Smart Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-gray-100">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            {t('games.overview')}
          </TabsTrigger>
          <TabsTrigger value="revenue" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            {t('games.revenue')}
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            {t('games.performance')}
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            {t('games.insights')}
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enhanced Revenue Chart */}
            <Card className="shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  {getChartData?.chartTitle || t('analytics.weeklyRevenue')}
                  <Badge variant="outline" className="ml-auto text-xs">
                    {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {getChartData?.revenueData && getChartData.revenueData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={320}>
                    <AreaChart data={getChartData.revenueData}>
                    <defs>
                      <linearGradient id="gamesGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0.1}/>
                      </linearGradient>
                      <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={COLORS.secondary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={COLORS.secondary} stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="day_formatted"
                      tick={{ fontSize: 11, fill: '#666' }}
                      axisLine={false}
                      tickLine={false}
                    />
                    <YAxis
                      tick={{ fontSize: 11, fill: '#666' }}
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value) => `${value}L`}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                      formatter={(value, name) => [
                        `${Number(value).toFixed(0)} L`,
                        name === 'games' ? t('games.gamesIcon') : t('games.barIcon')
                      ]}
                      labelFormatter={(label) => `📅 ${label}`}
                    />
                    <Area
                      type="monotone"
                      dataKey="games"
                      stackId="1"
                      stroke={COLORS.primary}
                      fill="url(#gamesGradient)"
                      strokeWidth={2}
                    />
                    <Area
                      type="monotone"
                      dataKey="bar"
                      stackId="1"
                      stroke={COLORS.secondary}
                      fill="url(#barGradient)"
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[320px] text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <div className="text-lg font-medium mb-2">{t('games.noDataAvailableForTimeRange', { timeRange })}</div>
                      <div className="text-sm">{t('games.dataWillAppearHere')}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchAnalyticsData}
                        className="mt-4"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Refresh
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Enhanced Hourly Usage Chart */}
            <Card className="shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-600" />
                  {t('analytics.tableUsageByHour')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredData.hourlyUsage && filteredData.hourlyUsage.length > 0 ? (
                  <ResponsiveContainer width="100%" height={320}>
                    <LineChart data={filteredData.hourlyUsage}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="hour"
                        tick={{ fontSize: 11, fill: '#666' }}
                        axisLine={false}
                        tickLine={false}
                      />
                      <YAxis
                        tick={{ fontSize: 11, fill: '#666' }}
                        axisLine={false}
                        tickLine={false}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                        formatter={(value, name) => [
                          `${value} ${value !== 1 ? t('games.tables') : t('games.table')}`,
                          name === 'games' ? t('games.gamesIcon') : t('games.barOrdersIcon')
                        ]}
                        labelFormatter={(label) => `⏰ ${label}:00`}
                      />
                      <Line
                        type="monotone"
                        dataKey="games"
                        stroke={COLORS.primary}
                        strokeWidth={3}
                        dot={{ fill: COLORS.primary, strokeWidth: 2, r: 5 }}
                        activeDot={{ r: 7, stroke: COLORS.primary, strokeWidth: 2, fill: 'white' }}
                        name={t('games.gamesIcon')}
                      />
                      <Line
                        type="monotone"
                        dataKey="bar"
                        stroke={COLORS.secondary}
                        strokeWidth={3}
                        dot={{ fill: COLORS.secondary, strokeWidth: 2, r: 5 }}
                        activeDot={{ r: 7, stroke: COLORS.secondary, strokeWidth: 2, fill: 'white' }}
                        name={t('games.barOrdersIcon')}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[320px] text-gray-500">
                    <div className="text-center">
                      <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <div className="text-lg font-medium mb-2">{t('analytics.noDataAvailable')}</div>
                      <div className="text-sm">{t('analytics.tableUsageDataWillAppear')}</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        {/* Revenue Tab */}
        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Revenue Breakdown Pie Chart */}
            <Card className="lg:col-span-2 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5 text-green-600" />
                  {t('games.revenueBreakdown')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: t('games.billiard'), value: Number(filteredData.todayStats?.games_revenue || 0), fill: COLORS.primary },
                        { name: t('bar.title'), value: Number(filteredData.todayStats?.bar_revenue || 0), fill: COLORS.secondary }
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {[
                        { name: t('games.billiard'), value: Number(filteredData.todayStats?.games_revenue || 0), fill: COLORS.primary },
                        { name: t('bar.title'), value: Number(filteredData.todayStats?.bar_revenue || 0), fill: COLORS.secondary }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [`${Number(value).toFixed(0)} L`, 'Revenue']}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Revenue Insights */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-600" />
                  {t('games.revenueInsights')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">{t('games.bestPerformer')}</span>
                  </div>
                  <p className="text-sm text-green-700">
                    {Number(filteredData.todayStats?.games_revenue || 0) > Number(filteredData.todayStats?.bar_revenue || 0)
                      ? t('games.gamesAreDrivingRevenue')
                      : t('games.barSalesAreLeading')}
                  </p>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-800">{t('games.opportunity')}</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    {smartInsights?.revenueChange > 0
                      ? t('games.growingTrend')
                      : t('games.needsAttention')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Game Room Analytics */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  🎱 {t('analytics.gameRoomAnalytics')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{t('analytics.tableUtilization')}</span>
                    <span className="font-medium">{Math.round(filteredData.performanceMetrics?.table_utilization || 0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${Math.min(filteredData.performanceMetrics?.table_utilization || 0, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">{Math.round(Number(filteredData.todayStats?.avg_duration || 0))}</div>
                    <div className="text-xs text-gray-600">{t('games.avgDurationMin')}</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">{filteredData.performanceMetrics?.games_today || 0}</div>
                    <div className="text-xs text-gray-600">{t('games.gamesToday')}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bar Performance */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-600">
                  🍺 {t('analytics.barPerformance')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-lg font-bold text-orange-900">{Number(filteredData.todayStats?.order_count || 0)}</div>
                    <div className="text-xs text-orange-700">{t('games.ordersToday')}</div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-lg font-bold text-orange-900">{Math.round(filteredData.performanceMetrics?.avg_order_value || 0)}L</div>
                    <div className="text-xs text-orange-700">{t('games.avgOrder')}</div>
                  </div>
                </div>

                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="text-sm font-medium text-orange-800 mb-1">{t('games.topItem')}</div>
                  <div className="text-xs text-orange-700">{filteredData.performanceMetrics?.bestseller || 'N/A'}</div>
                </div>
              </CardContent>
            </Card>

            {/* Peak Hours */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-600">
                  {t('games.peakPerformance')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-900">{filteredData.performanceMetrics?.peak_hour || 'N/A'}</div>
                  <div className="text-sm text-purple-700">{t('games.peakHour')}</div>
                  {smartInsights?.isRushHour && (
                    <Badge className="mt-2 bg-red-100 text-red-800">{t('games.rushHourNow')}</Badge>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">{t('games.mostUsedTable')}</span>
                    <span className="font-medium">{t('games.table')} {filteredData.performanceMetrics?.most_used_table || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">{t('games.activeTables')}</span>
                    <span className="font-medium">{filteredData.performanceMetrics?.active_tables || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Smart Recommendations */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  {t('games.smartRecommendations')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {smartInsights?.revenueChange > 10 && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Sparkles className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-800">{t('games.excellentPerformance')}</span>
                    </div>
                    <p className="text-sm text-green-700">{t('games.revenueIsUp', { changeText: smartInsights.revenueChangeText })}</p>
                  </div>
                )}

                {smartInsights?.efficiencyScore < 60 && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium text-yellow-800">{t('games.optimizationOpportunity')}</span>
                    </div>
                    <p className="text-sm text-yellow-700">{t('games.considerOptimizingGameDurations')}</p>
                  </div>
                )}

                {Number(filteredData.performanceMetrics?.table_utilization || 0) < 40 && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Lightbulb className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-800">{t('games.marketingSuggestion')}</span>
                    </div>
                    <p className="text-sm text-blue-700">{t('games.tableUtilizationIsLow')}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Business Health Score */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-600" />
                  {t('games.businessHealthScore')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-purple-600 mb-2">
                    {Math.round((smartInsights?.efficiencyScore || 0) * 0.4 + (Number(filteredData.performanceMetrics?.table_utilization || 0)) * 0.6)}
                  </div>
                  <div className="text-sm text-gray-600">{t('games.overallHealthScore')}</div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{t('games.revenueTrend')}</span>
                    <Badge variant={smartInsights?.revenueChange > 0 ? "default" : "secondary"}>
                      {smartInsights?.revenueChange > 0 ? t('games.growing') : t('games.stable')}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{t('games.efficiency')}</span>
                    <Badge variant={smartInsights?.efficiencyScore > 80 ? "default" : "secondary"}>
                      {smartInsights?.efficiencyText}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{t('games.utilization')}</span>
                    <Badge variant={Number(filteredData.performanceMetrics?.table_utilization || 0) > 70 ? "default" : "secondary"}>
                      {smartInsights?.utilizationLevel}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
