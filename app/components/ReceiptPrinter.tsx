"use client"

import { useRef, useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Button } from "@/components/ui/button"
import { Printer } from "lucide-react"
import { logReceiptToHistory } from "../utils/receiptLogger"
import { fetchCurrencySettings, formatCurrency, formatReceiptItem, formatReceiptTotals, type CurrencySettings } from "../lib/currency"

interface Game {
  id: string
  tableNumber: number
  tableName?: string  // Optional table name
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
}

interface Order {
  id: string
  table_number: number
  table_name?: string  // Optional table name
  items: Array<{
    name: string
    price: number
    quantity: number
  }>
  total: number
  status: "pending" | "completed"
  created_at: Date
}

interface ReceiptPrinterProps {
  type: "game" | "order"
  data: Game | Order
  onPrint?: () => void
}

export function ReceiptPrinter({ type, data, onPrint }: ReceiptPrinterProps) {
  const { t } = useSafeTranslation()
  const printRef = useRef<HTMLDivElement>(null)
  const [businessInfo, setBusinessInfo] = useState<{
    name: string
    address: string
    phone: string
    email: string
    vat_number: string
  } | null>(null)
  const [currencySettings, setCurrencySettings] = useState<CurrencySettings | null>(null)

  // Get currency settings with fallback for use in components
  const currency = currencySettings || {
    currency: 'Albanian Lek',
    symbol: 'L',
    showDecimals: false,
    taxIncluded: false,
    taxEnabled: true,
    taxRate: 20,
    qrCodeEnabled: false,
    qrCodeUrl: '',
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch business info
        const businessResponse = await fetch('/api/business-info')
        if (businessResponse.ok) {
          const data = await businessResponse.json()
          setBusinessInfo(data)
        }

        // Fetch currency settings
        const currencyData = await fetchCurrencySettings()
        setCurrencySettings(currencyData)
      } catch (error) {
        console.error('Failed to fetch data:', error)
      }
    }
    fetchData()
  }, [])

  const handlePrint = async () => {
    if (printRef.current) {
      // Log receipt to database
      try {
        await logReceiptToHistory({
          type,
          data,
          printedBy: "Current User", // In real app, get from auth context
          printedAt: new Date(),
        })
      } catch (error) {
        // Silently handle receipt logging errors
      }

      // Get currency settings with fallback
      const currency = currencySettings || {
        currency: 'Albanian Lek',
        symbol: 'L',
        showDecimals: false,
        taxIncluded: false,
        taxEnabled: true,
        taxRate: 20,
        qrCodeEnabled: false,
        qrCodeUrl: '',
      }

      const printWindow = window.open("", "_blank")
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Receipt</title>
              <style>
                @media print {
                  @page {
                    size: 80mm auto;
                    margin: 0;
                  }
                  body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    line-height: 1.4;
                  }
                }
                body {
                  font-family: 'Courier New', monospace;
                  font-size: 12px;
                  line-height: 1.4;
                  max-width: 300px;
                  margin: 0 auto;
                  padding: 10px;
                }
                .receipt-header {
                  text-align: center;
                  border-bottom: 2px solid #000;
                  padding-bottom: 10px;
                  margin-bottom: 15px;
                }
                .receipt-title {
                  font-size: 16px;
                  font-weight: bold;
                  margin-bottom: 5px;
                }
                .receipt-subtitle {
                  font-size: 10px;
                  margin-bottom: 5px;
                }
                .receipt-info {
                  margin-bottom: 15px;
                }
                .receipt-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 3px;
                }
                .receipt-items {
                  border-top: 1px dashed #000;
                  border-bottom: 1px dashed #000;
                  padding: 10px 0;
                  margin: 15px 0;
                }
                .receipt-item {
                  margin-bottom: 5px;
                }
                .receipt-item-name {
                  font-weight: bold;
                }
                .receipt-item-details {
                  display: flex;
                  justify-content: space-between;
                  font-size: 11px;
                }
                .receipt-total {
                  border-top: 2px solid #000;
                  padding-top: 10px;
                  margin-top: 15px;
                }
                .receipt-total-row {
                  display: flex;
                  justify-content: space-between;
                  font-weight: bold;
                  font-size: 14px;
                }
                .receipt-footer {
                  text-align: center;
                  margin-top: 20px;
                  padding-top: 10px;
                  border-top: 1px dashed #000;
                  font-size: 10px;
                }
                .receipt-barcode {
                  text-align: center;
                  font-family: 'Courier New', monospace;
                  font-size: 8px;
                  margin: 10px 0;
                  letter-spacing: 2px;
                }
              </style>
            </head>
            <body>
              ${type === "game" ?
                `<div class="receipt">
                  <div class="receipt-header">
                    <div class="receipt-title">${businessInfo?.name || 'BILLIARD CLUB'}</div>
                    <div class="receipt-subtitle">${t('games.gameSessionReceipt')}</div>
                  </div>

                  <div class="receipt-info">
                    <div class="receipt-row">
                      <span>Date:</span>
                      <span>${formatTime(new Date())}</span>
                    </div>
                    <div class="receipt-row">
                      <span>Table:</span>
                      <span>${(data as Game).tableName || `Table ${(data as Game).tableNumber}`}</span>
                    </div>
                  </div>

                  <div class="receipt-items">
                    <div class="receipt-row">
                      <span>Start Time:</span>
                      <span>${formatTime((data as Game).startTime)}</span>
                    </div>
                    ${(data as Game).endTime ? `
                      <div class="receipt-row">
                        <span>End Time:</span>
                        <span>${formatTime(new Date((data as Game).endTime as string))}</span>
                      </div>
                    ` : ''}
                    <div class="receipt-row">
                      <span>Duration:</span>
                      <span>${formatDuration((data as Game).duration)}</span>
                    </div>
                  </div>

                  <div class="receipt-total">
                    ${(() => {
                      const totalsLines = formatReceiptTotals((data as Game).cost, currency)
                      return totalsLines.map(line => `<div class="${line.includes('Total:') ? 'receipt-total-row' : 'receipt-row'}"><span>${line.split(':')[0]}:</span><span>${line.split(':')[1]?.trim()}</span></div>`).join('')
                    })()}
                  </div>


                </div>` :
                `<div class="receipt">
                  <div class="receipt-header">
                    <div class="receipt-title">${businessInfo?.name || 'BILLIARD CLUB'}</div>
                    <div class="receipt-subtitle">Bar Order Receipt</div>
                  </div>

                  <div class="receipt-info">
                    <div class="receipt-row">
                      <span>Date:</span>
                      <span>${formatTime(new Date((data as Order).created_at.toString()))}</span>
                    </div>
                    <div class="receipt-row">
                      <span>Table:</span>
                      <span>${(data as Order).table_name || `Table ${(data as Order).table_number}`}</span>
                    </div>
                  </div>

                  <div class="receipt-items">
                    ${(data as Order).items.map((item) => {
                      return `
                        <div class="receipt-item">
                          <div class="receipt-item-name">${item.name}</div>
                          <div class="receipt-item-details">
                            <span>${item.quantity} x ${formatCurrency(item.price, currency)}</span>
                            <span>${formatCurrency(item.quantity * item.price, currency)}</span>
                          </div>
                        </div>
                      `
                    }).join('')}
                  </div>

                  <div class="receipt-total">
                    ${(() => {
                      const orderSubtotal = (data as Order).items.reduce((sum, item) => sum + (item.quantity * item.price), 0)
                      const totalsLines = formatReceiptTotals(orderSubtotal, currency)
                      return totalsLines.map(line => `<div class="receipt-row"><span>${line.split(':')[0]}:</span><span>${line.split(':')[1]?.trim()}</span></div>`).join('')
                    })()}
                  </div>


                </div>`
              }
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
        printWindow.close()
        onPrint?.()
      }
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  return (
    <>
      <Button onClick={handlePrint} size="sm" variant="ghost" className="flex items-center gap-1 p-1.5 h-auto min-w-0 text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors">
        <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
        <span className="hidden sm:inline text-xs">{t('receipt.print')}</span>
      </Button>

      <div ref={printRef} className="hidden">
        {type === "game" && <GameReceipt game={data as Game} formatTime={formatTime} formatDuration={formatDuration} businessInfo={businessInfo} currency={currency} />}
        {type === "order" && <OrderReceipt order={data as Order} formatTime={formatTime} businessInfo={businessInfo} />}
      </div>
    </>
  )
}

function GameReceipt({
  game,
  formatTime,
  formatDuration,
  businessInfo,
  currency,
}: {
  game: Game
  formatTime: (date: Date) => string
  formatDuration: (minutes: number) => string
  businessInfo: {
    name: string
    address: string
    phone: string
    email: string
    vat_number: string
  } | null
  currency: CurrencySettings
}) {
  return (
    <div className="receipt">
      <div className="receipt-header">
        <div className="receipt-title">{businessInfo?.name || 'BILLIARD CLUB'}</div>
        <div className="receipt-subtitle">{t('games.gameSessionReceipt')}</div>
      </div>

      <div className="receipt-info">
        <div className="receipt-row">
          <span>Date:</span>
          <span>{formatTime(game.endTime ? new Date(game.endTime) : new Date())}</span>
        </div>
        <div className="receipt-row">
          <span>Table:</span>
          <span>{game.tableName || `Table ${game.tableNumber}`}</span>
        </div>
      </div>

      <div className="receipt-items">
        <div className="receipt-row">
          <span>Start Time:</span>
          <span>{formatTime(game.startTime)}</span>
        </div>
        {game.endTime && (
          <div className="receipt-row">
            <span>End Time:</span>
            <span>{formatTime(new Date(game.endTime))}</span>
          </div>
        )}
        <div className="receipt-row">
          <span>Duration:</span>
          <span>{formatDuration(game.duration)}</span>
        </div>
      </div>

      <div className="receipt-total">
        {(() => {
          const totalsLines = formatReceiptTotals(game.cost, currency)
          return totalsLines.map((line, index) => (
            <div key={index} className={line.includes('Total:') ? 'receipt-total-row' : 'receipt-row'}>
              <span>{line.split(':')[0]}:</span>
              <span>{line.split(':')[1]?.trim()}</span>
            </div>
          ))
        })()}
      </div>


    </div>
  )
}

function OrderReceipt({
  order,
  formatTime,
  businessInfo
}: {
  order: Order
  formatTime: (date: Date) => string
  businessInfo: {
    name: string
    address: string
    phone: string
    email: string
    vat_number: string
  } | null
}) {
  return (
    <div className="receipt">
      <div className="receipt-header">
        <div className="receipt-title">{businessInfo?.name || 'BILLIARD CLUB'}</div>
        <div className="receipt-subtitle">Bar Order Receipt</div>
      </div>

      <div className="receipt-info">
        <div className="receipt-row">
          <span>Date:</span>
          <span>{formatTime(new Date(order.created_at.toString()))}</span>
        </div>
        <div className="receipt-row">
          <span>Table:</span>
          <span>{order.table_name || `Table ${order.table_number}`}</span>
        </div>
      </div>

      <div className="receipt-items">
        {order.items.map((item, index) => (
          <div key={index} className="receipt-item">
            <div className="receipt-item-name">{item.name}</div>
            <div className="receipt-item-details">
              <span>{item.quantity} x {item.price} L</span>
              <span>{item.quantity * item.price} L</span>
            </div>
          </div>
        ))}
      </div>

      <div className="receipt-total">
        <div className="receipt-row">
          <span>Subtotal:</span>
          <span>{order.total} L</span>
        </div>
        <div className="receipt-row">
          <span>Tax (20%):</span>
          <span>{Math.round(order.total * 0.2)} L</span>
        </div>
        <div className="receipt-total-row">
          <span>TOTAL:</span>
          <span>{Math.round(order.total * 1.2)} L</span>
        </div>
      </div>


    </div>
  )
}
