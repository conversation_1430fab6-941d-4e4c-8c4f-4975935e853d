'use client'

import React, { useState } from 'react'
import { AppleHeader } from './AppleHeader'

// Demo component showing how to use the AppleHeader
export function AppleHeaderDemo() {
  const [activeView, setActiveView] = useState('tables')

  const navigationItems = [
    { label: 'Tables', href: 'tables', isActive: activeView === 'tables' },
    { label: 'Bar', href: 'bar', isActive: activeView === 'bar' },
    { label: 'Analytics', href: 'analytics', isActive: activeView === 'analytics' },
    { label: 'Receipts', href: 'receipts', isActive: activeView === 'receipts' },
    { label: 'Settings', href: 'settings', isActive: activeView === 'settings' },
  ]

  const handleNavigationClick = (href: string) => {
    setActiveView(href)
    console.log('Navigating to:', href)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Apple Header */}
      <AppleHeader 
        navigationItems={navigationItems}
        onNavigationClick={handleNavigationClick}
      />

      {/* Demo Content */}
      <main className="pt-16">
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Apple-Style Header Demo
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              This header component mimics Apple's website design with a semi-transparent background, 
              smooth scroll effects, and responsive mobile menu.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Scroll Effects
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Background becomes more opaque as you scroll down the page.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Responsive Design
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Mobile menu appears at medium breakpoint with smooth animations.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Accessibility
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Full keyboard navigation and screen reader support.
              </p>
            </div>
          </div>

          {/* Current View Display */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Current View: {activeView.charAt(0).toUpperCase() + activeView.slice(1)}
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Click on navigation items to see the active state change.
            </p>
          </div>

          {/* Scroll Demo Content */}
          <div className="mt-12 space-y-8">
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Demo Section {i + 1}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Scroll down to see the header background become more opaque. 
                  This mimics Apple's website behavior where the header becomes 
                  more prominent as you scroll.
                </p>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}

export default AppleHeaderDemo
