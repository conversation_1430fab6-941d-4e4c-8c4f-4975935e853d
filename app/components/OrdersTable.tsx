"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Coffee, Clock } from "lucide-react"
import { ReceiptPrinter } from "./ReceiptPrinter"
import type { Order } from "../types"

interface OrdersTableProps {
  orders: Order[]
}

export function OrdersTable({ orders }: OrdersTableProps) {
  const [availableTables, setAvailableTables] = useState<any[]>([])

  // Load bar tables to get table names
  useEffect(() => {
    const loadTables = async () => {
      try {
        const response = await fetch('/api/tables/user', {
          credentials: 'include'
        })
        if (response.ok) {
          const tablesData = await response.json()
          const mappedTables = tablesData
            .filter((t: any) => t.is_active)
            .map((t: any) => ({
              id: t.id.toString(),
              number: t.number,
              name: t.name,
              isActive: t.is_active
            }))
          setAvailableTables(mappedTables)
        }
      } catch (error) {
        console.error('Failed to load bar tables:', error)
      }
    }
    loadTables()
  }, [])

  const getTableName = (number: number) => {
    const table = availableTables.find(t => t.number === number)
    return table?.name || `Table ${number}`
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleString()
  }

  const completeOrder = async (orderId: string) => {
    try {
      const response = await fetch('/api/orders', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: orderId,
          status: 'completed',
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to complete order')
      }

      // Refresh the page to get updated orders
      window.location.reload()
    } catch (error) {
      console.error('Failed to complete order:', error)
    }
  }

  const pendingOrders = orders.filter(order => order.status === "pending")
  const completedOrders = orders.filter(order => order.status === "completed").slice(0, 3)

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      <div className="border-b border-gray-200 p-3">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-orange-100 rounded flex items-center justify-center">
            <Coffee className="h-3 w-3 text-orange-600" />
          </div>
          <div>
            <div className="text-sm font-bold text-gray-900">Recent Orders</div>
            <div className="text-xs text-gray-500">{pendingOrders.length} pending</div>
          </div>
        </div>
      </div>
      <div className="p-3">
        {orders.length === 0 ? (
          <div className="text-center py-6">
            <div className="text-sm text-gray-500">No orders yet</div>
            <div className="text-xs text-gray-400 mt-1">Orders will appear here when placed</div>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Pending Orders Table */}
            {pendingOrders.slice(0, 3).map((order) => (
              <div key={order.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-orange-600 rounded text-white text-xs font-bold flex items-center justify-center" title={getTableName(order.table_number)}>
                    {getTableName(order.table_number).charAt(0)}
                  </div>
                  <div>
                    <div className="text-sm font-bold text-gray-900">{order.total} L</div>
                    <div className="text-xs text-gray-500">
                      {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                      {order.created_by_name && ` • ${order.created_by_name.split(' ')[0]}`}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-xs h-6 px-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                    onClick={() => completeOrder(order.id)}
                  >
                    ✓
                  </Button>
                  <ReceiptPrinter
                    type="order"
                    data={order}
                    onPrint={() => {}}
                  />
                </div>
              </div>
            ))}

            {/* Section Divider for Completed Orders */}
            {completedOrders.length > 0 && pendingOrders.length > 0 && (
              <div className="border-t border-gray-200 pt-2 mt-2">
                <div className="text-xs text-gray-500 mb-2">Recent Completed</div>
              </div>
            )}

            {/* Recent Completed Orders - Table Format */}
            {completedOrders.map((order) => (
              <div key={order.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gray-600 rounded text-white text-xs font-bold flex items-center justify-center" title={getTableName(order.table_number)}>
                    {getTableName(order.table_number).charAt(0)}
                  </div>
                  <div>
                    <div className="text-sm font-bold text-gray-900">{order.total} L</div>
                    <div className="text-xs text-gray-500">
                      {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                      {order.created_by_name && ` • ${order.created_by_name.split(' ')[0]}`}
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <ReceiptPrinter
                    type="order"
                    data={order}
                    onPrint={() => console.log(`Printed receipt for order ${order.id}`)}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
