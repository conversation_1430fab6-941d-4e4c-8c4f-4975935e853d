"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { User, Lock, Camera, Save, Eye, EyeOff, Users as UsersIcon } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { UsersList } from "./UsersList"

interface UserProfileProps {
  isOpen: boolean
  onClose: () => void
}

interface UserData {
  id: number
  username: string
  full_name: string
  role: string
  profile_picture?: string
  created_at: string
}

export function UserProfile({ isOpen, onClose }: UserProfileProps) {
  const { t } = useSafeTranslation()
  const { user, updateUser } = useAuth()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  // Form states
  const [fullName, setFullName] = useState("")
  const [username, setUsername] = useState("")
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [profilePicture, setProfilePicture] = useState("")
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Load user data
  useEffect(() => {
    if (isOpen && user) {
      loadUserData()
    }
  }, [isOpen, user])

  const loadUserData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/user-profile', {
        credentials: 'include' // Use cookies for authentication
      })

      if (response.ok) {
        const data = await response.json()
        setUserData(data)
        setFullName(data.full_name)
        setUsername(data.username)
        setProfilePicture(data.profile_picture || "")
      }
    } catch (error) {
      console.error('Failed to load user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async () => {
    if (!fullName.trim()) {
      alert(t('profile.fullNameRequired'))
      return
    }

    if (!username.trim()) {
      alert(t('profile.usernameRequired'))
      return
    }

    setSaving(true)
    try {
      const updateData: any = {
        fullName: fullName.trim(),
        username: username.trim(),
        profilePicture: profilePicture.trim() || null
      }

      // If changing password
      if (newPassword) {
        if (newPassword !== confirmPassword) {
          alert(t('profile.passwordsDontMatch'))
          setSaving(false)
          return
        }

        if (newPassword.length < 6) {
          alert(t('profile.passwordTooShort'))
          setSaving(false)
          return
        }

        updateData.currentPassword = currentPassword
        updateData.newPassword = newPassword
      }

      const response = await fetch('/api/user-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify(updateData)
      })

      if (response.ok) {
        const updatedUser = await response.json()

        // Update auth context
        updateUser({
          ...user!,
          username: updatedUser.username,
          fullName: updatedUser.full_name,
          profilePicture: updatedUser.profile_picture
        })

        // Reset password fields
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")

        alert(t('profile.profileUpdatedSuccessfully'))
        onClose()
      } else {
        const errorData = await response.json()
        alert(errorData.error || t('profile.failedToUpdateProfile'))
      }
    } catch (error) {
      console.error('Failed to update profile:', error)
      alert(t('profile.failedToUpdateProfile'))
    } finally {
      setSaving(false)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // For now, we'll use a simple base64 approach
      // In production, you'd want to upload to a proper image service
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setProfilePicture(result)
      }
      reader.readAsDataURL(file)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('profile.userProfile')}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className={`grid w-full ${user?.role === 'admin' ? 'grid-cols-3' : 'grid-cols-2'}`}>
              <TabsTrigger value="profile">{t('profile.profile')}</TabsTrigger>
              <TabsTrigger value="security">{t('profile.security')}</TabsTrigger>
              {user?.role === 'admin' && (
                <TabsTrigger value="users">{t('profile.users')}</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">{t('profile.profileInformation')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Profile Picture */}
                  <div className="flex flex-col items-center space-y-2">
                    <Avatar className="w-20 h-20">
                      <AvatarImage src={profilePicture} />
                      <AvatarFallback className="text-lg">
                        {fullName ? getInitials(fullName) : "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex items-center gap-2">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="profile-picture-upload"
                      />
                      <label htmlFor="profile-picture-upload">
                        <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                          <span>
                            <Camera className="h-4 w-4 mr-1" />
                            {t('profile.changePhoto')}
                          </span>
                        </Button>
                      </label>
                    </div>
                  </div>

                  {/* Full Name */}
                  <div className="space-y-2">
                    <Label htmlFor="fullName">{t('profile.fullName')}</Label>
                    <Input
                      id="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      placeholder={t('profile.enterFullName')}
                    />
                  </div>

                  {/* Username (editable) */}
                  <div className="space-y-2">
                    <Label htmlFor="username">{t('profile.username')}</Label>
                    <Input
                      id="username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      placeholder={t('profile.enterUsername')}
                    />
                  </div>

                  {/* Role (readonly, hidden for admins) */}
                  {user?.role !== 'admin' && (
                    <div className="space-y-2">
                      <Label htmlFor="role">{t('profile.role')}</Label>
                      <Input
                        id="role"
                        value={userData?.role || ""}
                        disabled
                        className="bg-gray-50 capitalize"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    {t('profile.changePassword')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Current Password */}
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">{t('profile.currentPassword')}</Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showCurrentPassword ? "text" : "password"}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        placeholder={t('profile.enterCurrentPassword')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {/* New Password */}
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">{t('profile.newPassword')}</Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showNewPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder={t('profile.enterNewPassword')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password */}
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">{t('profile.confirmNewPassword')}</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder={t('profile.confirmNewPassword')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    {t('profile.passwordFieldsNote')}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {user?.role === 'admin' && (
              <TabsContent value="users" className="space-y-4">
                <UsersList />
              </TabsContent>
            )}
          </Tabs>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            {t('profile.cancel')}
          </Button>
          <Button onClick={handleSaveProfile} disabled={saving || loading}>
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('profile.saving')}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {t('profile.saveChanges')}
              </>
            )}
          </Button>
        </div>
      </DialogContent>


    </Dialog>
  )
}
