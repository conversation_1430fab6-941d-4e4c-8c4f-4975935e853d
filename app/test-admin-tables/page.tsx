'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestAdminTablesPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAdminTableVisibility = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🧪 Testing admin table visibility...')
      
      const tests = []
      
      // Test 1: Check current user profile
      try {
        const profileResponse = await fetch('/api/user-profile', {
          credentials: 'include'
        })
        if (profileResponse.ok) {
          const profile = await profileResponse.json()
          tests.push({
            name: 'Current User Profile',
            success: true,
            data: profile
          })
        } else {
          tests.push({
            name: 'Current User Profile',
            success: false,
            error: `Status: ${profileResponse.status}`
          })
        }
      } catch (error) {
        tests.push({
          name: 'Current User Profile',
          success: false,
          error: error.message
        })
      }
      
      // Test 2: Check tables visible in bar interface (should be user-specific)
      try {
        const userTablesResponse = await fetch('/api/tables/user', {
          credentials: 'include'
        })
        if (userTablesResponse.ok) {
          const userTables = await userTablesResponse.json()

          // Filter only active tables (like BarMenu does)
          const activeTables = userTables.filter((t: any) => t.is_active)

          tests.push({
            name: 'Bar Interface Tables (User-Specific)',
            success: true,
            data: userTables,
            activeTables: activeTables,
            count: userTables.length,
            activeCount: activeTables.length,
            details: userTables.map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
              assignedUserId: t.assigned_user_id,
              assignedUsername: t.assigned_username
            }))
          })
        } else {
          tests.push({
            name: 'Bar Interface Tables (User-Specific)',
            success: false,
            error: `Status: ${userTablesResponse.status}`
          })
        }
      } catch (error) {
        tests.push({
          name: 'Bar Interface Tables (User-Specific)',
          success: false,
          error: error.message
        })
      }
      
      // Test 3: Check all tables (admin management view)
      try {
        const allTablesResponse = await fetch('/api/tables', {
          credentials: 'include'
        })
        if (allTablesResponse.ok) {
          const allTables = await allTablesResponse.json()
          tests.push({
            name: 'Settings Management Tables (All Tables)',
            success: true,
            data: allTables,
            count: allTables.length
          })
        } else {
          tests.push({
            name: 'Settings Management Tables (All Tables)',
            success: false,
            error: `Status: ${allTablesResponse.status}`
          })
        }
      } catch (error) {
        tests.push({
          name: 'Settings Management Tables (All Tables)',
          success: false,
          error: error.message
        })
      }
      
      setResult({
        message: 'Admin table visibility test completed!',
        tests,
        summary: {
          total: tests.length,
          passed: tests.filter(t => t.success).length,
          failed: tests.filter(t => !t.success).length
        }
      })
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Test Admin Table Visibility</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will test if admin users see only their own tables in the bar interface while still having access to all tables in settings.
          </p>
          
          <Button onClick={testAdminTableVisibility} disabled={loading} size="lg">
            {loading ? 'Testing...' : 'Test Admin Table Visibility'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Test Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>Expected Results:</strong></p>
            <p>• Bar Interface Tables: Should show only tables assigned to admin user + unassigned tables</p>
            <p>• Settings Management Tables: Should show ALL tables for management purposes</p>
            <p>• This ensures admin sees only their own tables when using the bar, but can manage all tables in settings</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
