'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function TestRoleChangePage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAllAuthentication = async () => {
    setLoading(true)
    setResult(null)

    try {
      console.log('🧪 Testing ALL authentication fixes...')

      const tests = []

      // Test 1: User Profile
      try {
        const profileResponse = await fetch('/api/user-profile', {
          credentials: 'include'
        })
        tests.push({
          name: 'User Profile GET',
          success: profileResponse.ok,
          status: profileResponse.status
        })
      } catch (error) {
        tests.push({
          name: 'User Profile GET',
          success: false,
          error: error.message
        })
      }

      // Test 2: Admin Users List
      try {
        const usersResponse = await fetch('/api/admin/users', {
          credentials: 'include'
        })
        tests.push({
          name: 'Admin Users List',
          success: usersResponse.ok,
          status: usersResponse.status
        })
      } catch (error) {
        tests.push({
          name: 'Admin Users List',
          success: false,
          error: error.message
        })
      }

      // Test 3: Business Info
      try {
        const businessResponse = await fetch('/api/business-info', {
          credentials: 'include'
        })
        tests.push({
          name: 'Business Info',
          success: businessResponse.ok,
          status: businessResponse.status
        })
      } catch (error) {
        tests.push({
          name: 'Business Info',
          success: false,
          error: error.message
        })
      }

      // Test 4: Currency Settings
      try {
        const currencyResponse = await fetch('/api/currency-settings', {
          credentials: 'include'
        })
        tests.push({
          name: 'Currency Settings',
          success: currencyResponse.ok,
          status: currencyResponse.status
        })
      } catch (error) {
        tests.push({
          name: 'Currency Settings',
          success: false,
          error: error.message
        })
      }

      setResult({
        message: 'Authentication tests completed!',
        tests,
        summary: {
          total: tests.length,
          passed: tests.filter(t => t.success).length,
          failed: tests.filter(t => !t.success).length
        }
      })

    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const runBarTablesMigration = async () => {
    setLoading(true)
    setResult(null)

    try {
      console.log('🔄 Running bar tables migration...')

      const response = await fetch('/api/migrate-bar-tables', {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        const migrationResult = await response.json()
        setResult({
          success: true,
          message: 'Bar tables migration completed!',
          migrationResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Migration failed',
          status: response.status,
          errorText: error
        })
      }

    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testRoleChange = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🧪 Testing admin role change functionality...')
      
      // First, let's check the current user profile
      const profileResponse = await fetch('/api/user-profile', {
        credentials: 'include'
      })
      
      let currentUser = null
      if (profileResponse.ok) {
        currentUser = await profileResponse.json()
        console.log('✅ Current user profile:', currentUser)
      } else {
        setResult({ error: 'Failed to get user profile', status: profileResponse.status })
        return
      }
      
      // Test the admin users API
      const usersResponse = await fetch('/api/admin/users', {
        credentials: 'include'
      })
      
      if (usersResponse.ok) {
        const users = await usersResponse.json()
        console.log('✅ Users list:', users)
        
        // Find Gesti user
        const gesti = users.find((u: any) => u.username === 'gesti')
        if (gesti) {
          console.log('👤 Found Gesti:', gesti)
          
          // Try to change Gesti's role to waiter
          const updateResponse = await fetch('/api/admin/users', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              userId: gesti.id,
              username: gesti.username,
              fullName: gesti.fullName,
              role: 'waiter',
              isActive: true
            })
          })
          
          if (updateResponse.ok) {
            const updatedUser = await updateResponse.json()
            setResult({
              success: true,
              message: 'Role changed successfully!',
              currentUser,
              originalGesti: gesti,
              updatedGesti: updatedUser,
              users
            })
          } else {
            const errorText = await updateResponse.text()
            setResult({
              error: 'Failed to change role',
              status: updateResponse.status,
              errorText,
              currentUser,
              gesti,
              users
            })
          }
        } else {
          setResult({ error: 'Gesti user not found', users, currentUser })
        }
      } else {
        const errorText = await usersResponse.text()
        setResult({
          error: 'Failed to get users',
          status: usersResponse.status,
          errorText,
          currentUser
        })
      }
      
    } catch (error) {
      setResult({ error: error.message, currentUser })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Test Admin Role Change</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This will test if the admin user can change Gesti's role from admin to waiter.
        </p>
        
        <div className="space-x-4">
          <Button onClick={testAllAuthentication} disabled={loading}>
            {loading ? 'Testing...' : 'Test All Authentication'}
          </Button>

          <Button onClick={testRoleChange} disabled={loading} variant="outline">
            {loading ? 'Testing...' : 'Test Role Change'}
          </Button>

          <Button onClick={runBarTablesMigration} disabled={loading} variant="secondary">
            {loading ? 'Running...' : 'Run Bar Tables Migration'}
          </Button>
        </div>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">Test Result:</h3>
            <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
