import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAdmin } from '@/app/utils/auth'

export async function GET() {
  try {
    const result = await pool.query('SELECT * FROM businessinfo ORDER BY id DESC LIMIT 1')
    return NextResponse.json(result.rows[0] || null)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch business info" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { name, address, phone, email, vat_number } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: "Business name is required" }, { status: 400 })
    }

    // Check if record exists
    const existing = await pool.query('SELECT id FROM businessinfo ORDER BY id DESC LIMIT 1')

    let result
    if (existing.rows.length > 0) {
      // Update existing record
      result = await pool.query(
        `UPDATE businessinfo
         SET name = $1, address = $2, phone = $3, email = $4, vat_number = $5, updated_at = CURRENT_TIMESTAMP
         WHERE id = $6
         RETURNING *`,
        [name, address, phone, email, vat_number, existing.rows[0].id]
      )
    } else {
      // Insert new record
      result = await pool.query(
        `INSERT INTO businessinfo (name, address, phone, email, vat_number)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [name, address, phone, email, vat_number]
      )
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to save business info" }, { status: 500 })
  }
}