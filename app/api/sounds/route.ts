import { NextRequest, NextResponse } from 'next/server'
import { readdir, stat } from 'fs/promises'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const soundsDir = path.join(process.cwd(), 'public', 'sounds')
    
    try {
      const files = await readdir(soundsDir)
      
      // Filter for audio files and get file info
      const audioFiles = []
      for (const file of files) {
        if (file === '.gitkeep') continue
        
        const filePath = path.join(soundsDir, file)
        const stats = await stat(filePath)
        
        // Check if it's an audio file by extension
        const ext = path.extname(file).toLowerCase()
        const audioExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.webm', '.aac']
        
        if (audioExtensions.includes(ext)) {
          audioFiles.push({
            filename: file,
            url: `/sounds/${file}`,
            size: stats.size,
            modified: stats.mtime,
            extension: ext
          })
        }
      }
      
      // Sort by modification date (newest first)
      audioFiles.sort((a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime())
      
      return NextResponse.json({
        success: true,
        files: audioFiles,
        count: audioFiles.length
      })
      
    } catch (error) {
      // Directory doesn't exist or is empty
      return NextResponse.json({
        success: true,
        files: [],
        count: 0
      })
    }
    
  } catch (error) {
    console.error('Error listing sound files:', error)
    return NextResponse.json({ 
      error: 'Failed to list sound files' 
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('filename')
    
    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 })
    }
    
    // Security: ensure filename doesn't contain path traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json({ error: 'Invalid filename' }, { status: 400 })
    }
    
    const filePath = path.join(process.cwd(), 'public', 'sounds', filename)
    
    try {
      const { unlink } = await import('fs/promises')
      await unlink(filePath)
      
      return NextResponse.json({
        success: true,
        message: 'File deleted successfully'
      })
      
    } catch (error) {
      return NextResponse.json({ 
        error: 'File not found or could not be deleted' 
      }, { status: 404 })
    }
    
  } catch (error) {
    console.error('Error deleting sound file:', error)
    return NextResponse.json({ 
      error: 'Failed to delete file' 
    }, { status: 500 })
  }
}
