import { NextResponse } from "next/server"
import pool from '@/lib/db'

// POST /api/migrate-bar-tables - Fix database constraint to allow duplicate table numbers
export async function POST() {
  try {
    console.log('🔄 Running bar tables constraint migration...')
    const results = []

    // Step 1: Remove the unique constraint on table number
    try {
      await pool.query('ALTER TABLE tables DROP CONSTRAINT IF EXISTS tables_number_key')
      results.push({ step: 'Remove unique constraint', status: 'success', message: 'Removed unique constraint on table number' })
    } catch (error) {
      results.push({ step: 'Remove unique constraint', status: 'warning', message: error.message })
    }

    // Step 2: Add composite unique constraint (number + assigned_user_id)
    try {
      await pool.query(`
        ALTER TABLE tables ADD CONSTRAINT unique_table_number_per_user
        UNIQUE (number, assigned_user_id)
      `)
      results.push({ step: 'Add composite constraint', status: 'success', message: 'Added composite unique constraint (number, assigned_user_id)' })
    } catch (error) {
      if (error.message.includes('already exists')) {
        results.push({ step: 'Add composite constraint', status: 'success', message: 'Composite constraint already exists' })
      } else {
        results.push({ step: 'Add composite constraint', status: 'error', message: error.message })
      }
    }

    // Step 3: Check current constraints
    const constraintCheck = await pool.query(`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'tables' AND constraint_type = 'UNIQUE'
    `)

    return NextResponse.json({
      success: true,
      message: 'Bar tables migration completed successfully!',
      description: 'You can now create multiple tables with the same number assigned to different users',
      results,
      currentConstraints: constraintCheck.rows,
      summary: {
        total: results.length,
        successful: results.filter(r => r.status === 'success').length,
        warnings: results.filter(r => r.status === 'warning').length,
        errors: results.filter(r => r.status === 'error').length
      }
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json({
      error: "Migration failed",
      details: error.message
    }, { status: 500 })
  }
}


