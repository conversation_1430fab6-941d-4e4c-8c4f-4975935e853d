import { NextResponse } from "next/server"
import pool from '@/lib/db'

// POST /api/remove-reservation-system - Remove table reservation system from database
export async function POST() {
  try {
    console.log('🔄 Removing table reservation system...')
    const results = []

    // Step 1: Drop table_sessions table if it exists
    try {
      await pool.query('DROP TABLE IF EXISTS table_sessions CASCADE')
      results.push({ step: 'Drop table_sessions table', status: 'success', message: 'Removed table_sessions table' })
    } catch (error) {
      results.push({ step: 'Drop table_sessions table', status: 'warning', message: error.message })
    }

    // Step 2: Remove reservation columns from gametables
    try {
      await pool.query(`
        ALTER TABLE gametables 
        DROP COLUMN IF EXISTS current_user_id,
        DROP COLUMN IF EXISTS current_username,
        DROP COLUMN IF EXISTS locked_at,
        DROP COLUMN IF EXISTS session_expires_at
      `)
      results.push({ step: 'Remove reservation columns', status: 'success', message: 'Removed reservation columns from gametables' })
    } catch (error) {
      results.push({ step: 'Remove reservation columns', status: 'warning', message: error.message })
    }

    // Step 3: Drop reservation-related functions if they exist
    try {
      await pool.query('DROP FUNCTION IF EXISTS reserve_table(INTEGER, INTEGER, VARCHAR, VARCHAR, INTEGER) CASCADE')
      await pool.query('DROP FUNCTION IF EXISTS release_table(INTEGER, VARCHAR) CASCADE')
      await pool.query('DROP FUNCTION IF EXISTS cleanup_expired_table_sessions() CASCADE')
      results.push({ step: 'Drop reservation functions', status: 'success', message: 'Removed reservation-related functions' })
    } catch (error) {
      results.push({ step: 'Drop reservation functions', status: 'warning', message: error.message })
    }

    // Step 4: Drop reservation-related views if they exist
    try {
      await pool.query('DROP VIEW IF EXISTS table_status_view CASCADE')
      results.push({ step: 'Drop reservation views', status: 'success', message: 'Removed table_status_view' })
    } catch (error) {
      results.push({ step: 'Drop reservation views', status: 'warning', message: error.message })
    }

    // Step 5: Drop reservation-related indexes if they exist
    try {
      await pool.query('DROP INDEX IF EXISTS idx_gametables_current_user')
      await pool.query('DROP INDEX IF EXISTS idx_gametables_locked_at')
      results.push({ step: 'Drop reservation indexes', status: 'success', message: 'Removed reservation-related indexes' })
    } catch (error) {
      results.push({ step: 'Drop reservation indexes', status: 'warning', message: error.message })
    }

    // Step 6: Check remaining columns
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'gametables'
      ORDER BY column_name
    `)

    return NextResponse.json({
      success: true,
      message: 'Table reservation system removed successfully!',
      description: 'All reservation-related tables, columns, functions, and views have been removed',
      results,
      remainingColumns: columnCheck.rows.map(row => row.column_name),
      summary: {
        total: results.length,
        successful: results.filter(r => r.status === 'success').length,
        warnings: results.filter(r => r.status === 'warning').length,
        errors: results.filter(r => r.status === 'error').length
      }
    })

  } catch (error) {
    console.error('Reservation system removal error:', error)
    return NextResponse.json({ 
      error: "Failed to remove reservation system", 
      details: error.message 
    }, { status: 500 })
  }
}
