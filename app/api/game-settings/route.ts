import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAdmin } from '@/app/utils/auth'

export async function GET() {
  try {
    const result = await pool.query('SELECT * FROM game_settings ORDER BY id DESC LIMIT 1')

    if (result.rows.length === 0) {
      // Return default settings if none exist
      return NextResponse.json({
        defaultHourlyRate: 400,
        minimumGameTime: 15,
        autoEndAfterHours: 8,
        autoPrintReceipts: true,
        taxRate: 20,
      })
    }

    return NextResponse.json({
      defaultHourlyRate: result.rows[0].default_hourly_rate,
      minimumGameTime: result.rows[0].minimum_game_time,
      autoEndAfterHours: result.rows[0].auto_end_after_hours,
      autoPrintReceipts: result.rows[0].auto_print_receipts,
      taxRate: result.rows[0].tax_rate,
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Failed to fetch game settings' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { defaultHourlyRate, minimumGameTime, autoEndAfterHours, autoPrintReceipts, taxRate, updateExistingTables } = body

    // Validate required fields
    if (defaultHourlyRate === undefined || minimumGameTime === undefined || autoEndAfterHours === undefined || taxRate === undefined) {
      return NextResponse.json({ error: "All settings fields are required" }, { status: 400 })
    }

    const client = await pool.connect()

    try {
      // Check if game settings exist
      const existingResult = await client.query(
        'SELECT id FROM game_settings ORDER BY id DESC LIMIT 1'
      )

      if (existingResult.rows.length > 0) {
        // Update existing settings
        const result = await client.query(
          `UPDATE game_settings SET
           default_hourly_rate = $1,
           minimum_game_time = $2,
           auto_end_after_hours = $3,
           auto_print_receipts = $4,
           tax_rate = $5,
           updated_at = NOW()
           WHERE id = $6
           RETURNING *`,
          [defaultHourlyRate, minimumGameTime, autoEndAfterHours, autoPrintReceipts, taxRate, existingResult.rows[0].id]
        )

        // Update existing game tables if requested
        if (updateExistingTables) {
          await client.query(
            'UPDATE gametables SET hourly_rate = $1',
            [defaultHourlyRate]
          )
        }

        return NextResponse.json({
          defaultHourlyRate: result.rows[0].default_hourly_rate,
          minimumGameTime: result.rows[0].minimum_game_time,
          autoEndAfterHours: result.rows[0].auto_end_after_hours,
          autoPrintReceipts: result.rows[0].auto_print_receipts,
          taxRate: result.rows[0].tax_rate,
        })
      } else {
        // Insert new settings
        const result = await client.query(
          `INSERT INTO game_settings (default_hourly_rate, minimum_game_time, auto_end_after_hours, auto_print_receipts, tax_rate, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
           RETURNING *`,
          [defaultHourlyRate, minimumGameTime, autoEndAfterHours, autoPrintReceipts, taxRate]
        )

        // Update existing game tables if requested
        if (updateExistingTables) {
          await client.query(
            'UPDATE gametables SET hourly_rate = $1',
            [defaultHourlyRate]
          )
        }

        return NextResponse.json({
          defaultHourlyRate: result.rows[0].default_hourly_rate,
          minimumGameTime: result.rows[0].minimum_game_time,
          autoEndAfterHours: result.rows[0].auto_end_after_hours,
          autoPrintReceipts: result.rows[0].auto_print_receipts,
          taxRate: result.rows[0].tax_rate,
        })
      }
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Failed to save game settings' }, { status: 500 })
  }
}
