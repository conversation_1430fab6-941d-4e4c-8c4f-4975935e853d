import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user
    // Determine if user can see all data or only their own
    const isAdmin = user.role === 'admin'

    // Build query with user filtering for non-admin users
    // Waiters can see: ALL active/pending orders (for table availability) + only their own completed orders
    const query = isAdmin ? `
      SELECT
        o.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        u.role as created_by_role
      FROM orders o
      LEFT JOIN users u ON o.created_by = u.id
      ORDER BY o.created_at DESC
    ` : `
      SELECT
        o.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        u.role as created_by_role
      FROM orders o
      LEFT JOIN users u ON o.created_by = u.id
      WHERE o.status IN ('pending', 'active') OR (o.status = 'completed' AND o.created_by = $1)
      ORDER BY o.created_at DESC
    `

    const params = isAdmin ? [] : [user.id]
    const result = await pool.query(query, params)
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch orders" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user

    const body = await request.json()
    const { table_number, items, total, status, created_at } = body

    // Validate required fields
    if (!table_number || !items || !total) {
      return NextResponse.json({
        error: "Missing required fields",
        details: "table_number, items, and total are required"
      }, { status: 400 })
    }

    // Validate items array
    if (!Array.isArray(items) || items.length === 0) {
      return NextResponse.json({
        error: "Invalid items",
        details: "items must be a non-empty array"
      }, { status: 400 })
    }

    // Validate each item has required fields
    for (const item of items) {
      if (!item.name || !item.price || !item.quantity) {
        return NextResponse.json({
          error: "Invalid item format",
          details: "Each item must have name, price, and quantity"
        }, { status: 400 })
      }
    }

    // Use provided created_at or current timestamp
    const orderTimestamp = created_at ? new Date(created_at) : new Date()

    console.log('Creating order with data:', { table_number, items, total, status, created_at: orderTimestamp, created_by: user.id })

    const result = await pool.query(
      'INSERT INTO orders (table_number, items, total, status, created_by, created_at) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [table_number, JSON.stringify(items), total, status || 'pending', user.id, orderTimestamp]
    )

    return NextResponse.json(result.rows[0], { status: 201 })
  } catch (error) {
    console.error('Database error details:', error)
    return NextResponse.json({
      error: "Failed to create order",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user

    const body = await request.json()
    const { id, status } = body

    // Validate required fields
    if (!id || !status) {
      return NextResponse.json({ error: "ID and status are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE orders SET status = $1 WHERE id = $2 RETURNING *',
      [status, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update order" }, { status: 500 })
  }
}
