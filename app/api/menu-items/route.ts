import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth, requireAdmin } from '@/lib/auth'

export async function GET() {
  try {
    const result = await pool.query('SELECT * FROM menu_items ORDER BY category, name')
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch menu items" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { name, price, category, available } = body

    // Validate required fields
    if (!name || price === undefined || !category) {
      return NextResponse.json({ error: "Name, price, and category are required" }, { status: 400 })
    }

    const result = await pool.query(
      'INSERT INTO menu_items (name, price, category, available) VALUES ($1, $2, $3, $4) RETURNING *',
      [name, price, category, available !== undefined ? available : true]
    )

    return NextResponse.json(result.rows[0], { status: 201 })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create menu item" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { id, name, price, category, available } = body

    // Validate required fields
    if (!id || !name || price === undefined || !category) {
      return NextResponse.json({ error: "ID, name, price, and category are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE menu_items SET name = $1, price = $2, category = $3, available = $4 WHERE id = $5 RETURNING *',
      [name, price, category, available, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Menu item not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update menu item" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 })
    }

    const result = await pool.query('DELETE FROM menu_items WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Menu item not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "Menu item deleted successfully" })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete menu item" }, { status: 500 })
  }
}