import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body = await request.json()
    const { name, isActive, hourlyRate, tableType, assignedUserId, assignedUsername, customSoundUrl } = body
    const resolvedParams = await params
    const tableId = resolvedParams.id

    // Validate required fields
    if (!name || hourlyRate === undefined) {
      return NextResponse.json({ error: "Name and hourly rate are required" }, { status: 400 })
    }

    // Ensure the custom_sound_url column exists
    try {
      await pool.query(`
        ALTER TABLE gametables
        ADD COLUMN IF NOT EXISTS custom_sound_url TEXT
      `)
    } catch (columnError) {
      console.log('Column custom_sound_url might already exist:', columnError.message)
    }

    // Try to update by ID first, if that fails try by number (backward compatibility)
    let result

    // Build dynamic query to only update provided fields
    // This prevents overwriting assignment fields with undefined values
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    // Always update these core fields
    updateFields.push(`name = $${paramIndex++}`)
    updateValues.push(name)

    updateFields.push(`is_active = $${paramIndex++}`)
    updateValues.push(isActive)

    updateFields.push(`hourly_rate = $${paramIndex++}`)
    updateValues.push(hourlyRate)

    updateFields.push(`table_type = $${paramIndex++}`)
    updateValues.push(tableType || 'billiard')

    // Only update assignment fields if they are explicitly provided
    if (assignedUserId !== undefined) {
      updateFields.push(`assigned_user_id = $${paramIndex++}`)
      updateValues.push(assignedUserId)
    }

    if (assignedUsername !== undefined) {
      updateFields.push(`assigned_username = $${paramIndex++}`)
      updateValues.push(assignedUsername)
    }

    // Update custom sound URL if provided (including empty string to clear it)
    if (customSoundUrl !== undefined) {
      updateFields.push(`custom_sound_url = $${paramIndex++}`)
      updateValues.push(customSoundUrl)
    }

    // Add the WHERE clause parameter
    const whereParam = `$${paramIndex}`
    updateValues.push(tableId)

    // First try: Update by table ID (most precise)
    result = await pool.query(
      `UPDATE gametables
       SET ${updateFields.join(', ')}
       WHERE id = ${whereParam}
       RETURNING *`,
      updateValues
    )

    // If no rows affected and tableId looks like a number, try updating by table number
    if (result.rows.length === 0 && !isNaN(parseInt(tableId))) {
      // Update the last parameter for number-based lookup
      updateValues[updateValues.length - 1] = parseInt(tableId)

      result = await pool.query(
        `UPDATE gametables
         SET ${updateFields.join(', ')}
         WHERE number = ${whereParam}
         RETURNING *`,
        updateValues
      )
    }

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Game table not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update game table" }, { status: 500 })
  }
}
