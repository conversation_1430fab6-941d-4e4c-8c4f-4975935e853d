import { NextResponse } from "next/server"
import pool from '@/lib/db'
import bcrypt from 'bcryptjs'

export async function POST() {
  try {
    console.log('🔄 Creating admin user...')

    // Check if admin user already exists
    const existingAdmin = await pool.query('SELECT id FROM users WHERE username = $1', ['admin'])
    
    if (existingAdmin.rows.length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Admin user already exists',
        adminExists: true
      })
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10)

    // Create admin user
    const result = await pool.query(
      'INSERT INTO users (username, password_hash, role, full_name, is_active) VALUES ($1, $2, $3, $4, $5) RETURNING id, username, role, full_name',
      ['admin', hashedPassword, 'admin', 'Administrator', true]
    )

    console.log('✅ Admin user created successfully:', result.rows[0])

    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: result.rows[0]
    })
  } catch (error) {
    console.error('❌ Failed to create admin user:', error)
    return NextResponse.json({ 
      success: false,
      error: error.message 
    }, { status: 500 })
  }
}
