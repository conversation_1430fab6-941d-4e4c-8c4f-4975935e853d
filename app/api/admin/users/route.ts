import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import bcrypt from 'bcryptjs'
import { requireAuth } from '@/lib/auth'

// GET all users (admin only)
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const result = await pool.query(
      'SELECT id, username, full_name, role, profile_picture, is_active, created_at FROM users ORDER BY created_at DESC'
    )

    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}

// PUT update user (admin only)
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { userId, username, fullName, role, profilePicture, isActive, newPassword } = body

    // Validate required fields
    if (!userId || !username || !fullName || !role) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Check if username is already taken by another user
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE username = $1 AND id != $2',
      [username, userId]
    )

    if (existingUser.rows.length > 0) {
      return NextResponse.json({ error: "Username is already taken" }, { status: 400 })
    }

    // Validate role
    if (!['admin', 'waiter'].includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 })
    }

    let result
    if (newPassword && newPassword.trim()) {
      // Update with new password
      if (newPassword.length < 6) {
        return NextResponse.json({ error: "Password must be at least 6 characters long" }, { status: 400 })
      }

      const hashedPassword = await bcrypt.hash(newPassword, 10)
      result = await pool.query(
        `UPDATE users
         SET username = $1, full_name = $2, role = $3, profile_picture = $4, is_active = $5, password_hash = $6, updated_at = CURRENT_TIMESTAMP
         WHERE id = $7
         RETURNING id, username, full_name, role, profile_picture, is_active, created_at`,
        [username, fullName, role, profilePicture || null, isActive !== false, hashedPassword, userId]
      )
    } else {
      // Update without password change
      result = await pool.query(
        `UPDATE users
         SET username = $1, full_name = $2, role = $3, profile_picture = $4, is_active = $5, updated_at = CURRENT_TIMESTAMP
         WHERE id = $6
         RETURNING id, username, full_name, role, profile_picture, is_active, created_at`,
        [username, fullName, role, profilePicture || null, isActive !== false, userId]
      )
    }

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update user" }, { status: 500 })
  }
}

// POST create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { username, fullName, role, password, profilePicture } = body

    // Validate required fields
    if (!username || !fullName || !role || !password) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Check if username already exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE username = $1',
      [username]
    )

    if (existingUser.rows.length > 0) {
      return NextResponse.json({ error: "Username is already taken" }, { status: 400 })
    }

    // Validate role
    if (!['admin', 'waiter'].includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 })
    }

    // Validate password
    if (password.length < 6) {
      return NextResponse.json({ error: "Password must be at least 6 characters long" }, { status: 400 })
    }

    // Hash password and create user
    const hashedPassword = await bcrypt.hash(password, 10)
    const result = await pool.query(
      `INSERT INTO users (username, full_name, role, password_hash, profile_picture, is_active)
       VALUES ($1, $2, $3, $4, $5, true)
       RETURNING id, username, full_name, role, profile_picture, is_active, created_at`,
      [username, fullName, role, hashedPassword, profilePicture || null]
    )

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create user" }, { status: 500 })
  }
}

// DELETE user (admin only)
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('id')

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 })
    }

    // Prevent admin from deleting themselves
    if (parseInt(userId) === user.userId) {
      return NextResponse.json({ error: "Cannot delete your own account" }, { status: 400 })
    }

    const result = await pool.query(
      'DELETE FROM users WHERE id = $1 RETURNING id',
      [userId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "User deleted successfully" })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete user" }, { status: 500 })
  }
}
