import { NextResponse } from "next/server"
import pool from '@/lib/db'

export async function GET() {
  try {
    console.log('🔄 Running database migration for tables...')

    // Add user assignment columns to tables table (for bar tables) if they don't exist
    await pool.query(`
      ALTER TABLE tables
      ADD COLUMN IF NOT EXISTS assigned_username VARCHAR(100)
    `)

    console.log('✅ Added assigned_username column to tables table')

    // Verify the columns exist
    const result = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'tables'
      AND column_name IN ('assigned_user_id', 'assigned_username')
    `)

    const existingColumns = result.rows.map(row => row.column_name)

    return NextResponse.json({
      success: true,
      message: 'Migration completed successfully',
      tablesColumns: existingColumns,
      status: 'complete'
    })
  } catch (error) {
    console.error('❌ Migration failed:', error)
    return NextResponse.json({ 
      success: false,
      error: error.message 
    }, { status: 500 })
  }
}
