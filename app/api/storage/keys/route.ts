import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// GET /api/storage/keys - Get all storage keys for user
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      'SELECT storage_key FROM user_storage WHERE user_id = $1',
      [authResult.user.id]
    )

    const keys = result.rows.map(row => row.storage_key)

    return NextResponse.json({ keys })
  } catch (error) {
    console.error('Storage GET KEYS error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
