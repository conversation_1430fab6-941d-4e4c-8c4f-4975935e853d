import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'

// GET /api/storage/test - Test storage connectivity
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Storage is available',
      userId: authResult.user.id 
    })
  } catch (error) {
    console.error('Storage TEST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
