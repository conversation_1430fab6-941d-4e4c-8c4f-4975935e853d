import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// Handle offline operation format
async function handleOfflineOperation(userId: number, body: any) {
  const { operation, key, value } = body

  if (!operation || !key) {
    return NextResponse.json({ error: "Missing operation or key" }, { status: 400 })
  }

  switch (operation) {
    case 'set':
    case 'setJSON':
      if (value === undefined) {
        return NextResponse.json({ error: "Missing value for set operation" }, { status: 400 })
      }

      // Validate key length and format
      if (key.length > 255) {
        return NextResponse.json({ error: 'Key too long (max 255 characters)' }, { status: 400 })
      }

      // Validate value size (limit to 1MB)
      if (typeof value === 'string' && value.length > 1024 * 1024) {
        return NextResponse.json({ error: 'Value too large (max 1MB)' }, { status: 400 })
      }

      await pool.query(
        `INSERT INTO user_storage (user_id, storage_key, storage_value, created_at, updated_at)
         VALUES ($1, $2, $3, NOW(), NOW())
         ON CONFLICT (user_id, storage_key)
         DO UPDATE SET storage_value = $3, updated_at = NOW()`,
        [userId, key, value]
      )

      return NextResponse.json({ success: true })

    case 'delete':
      await pool.query(
        'DELETE FROM user_storage WHERE user_id = $1 AND storage_key = $2',
        [userId, key]
      )

      return NextResponse.json({ success: true })

    default:
      return NextResponse.json({ error: "Invalid operation" }, { status: 400 })
  }
}

// GET /api/storage?key=<key> - Get storage value by key
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json({ error: 'Key parameter is required' }, { status: 400 })
    }

    const result = await pool.query(
      'SELECT storage_value FROM user_storage WHERE user_id = $1 AND storage_key = $2',
      [authResult.user.id, key]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ value: null })
    }

    return NextResponse.json({ value: result.rows[0].storage_value })
  } catch (error) {
    console.error('Storage GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/storage - Set storage value or execute offline operations
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    // Support both direct key/value and offline operation format
    const { key, value, operation } = body

    if (operation) {
      // Handle offline operation format
      return await handleOfflineOperation(authResult.user.id, body)
    }

    if (!key || value === undefined) {
      return NextResponse.json({ error: 'Key and value are required' }, { status: 400 })
    }

    // Validate key length and format
    if (key.length > 255) {
      return NextResponse.json({ error: 'Key too long (max 255 characters)' }, { status: 400 })
    }

    // Validate value size (limit to 1MB)
    if (typeof value === 'string' && value.length > 1024 * 1024) {
      return NextResponse.json({ error: 'Value too large (max 1MB)' }, { status: 400 })
    }

    // Use UPSERT to insert or update
    await pool.query(
      `INSERT INTO user_storage (user_id, storage_key, storage_value, created_at, updated_at)
       VALUES ($1, $2, $3, NOW(), NOW())
       ON CONFLICT (user_id, storage_key)
       DO UPDATE SET storage_value = $3, updated_at = NOW()`,
      [authResult.user.id, key, value]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Storage POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/storage?key=<key> - Delete storage value by key
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json({ error: 'Key parameter is required' }, { status: 400 })
    }

    await pool.query(
      'DELETE FROM user_storage WHERE user_id = $1 AND storage_key = $2',
      [authResult.user.id, key]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Storage DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
