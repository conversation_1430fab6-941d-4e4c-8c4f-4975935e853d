import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// DELETE /api/storage/clear - Clear all storage for user
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    await pool.query(
      'DELETE FROM user_storage WHERE user_id = $1',
      [authResult.user.id]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Storage CLEAR error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
