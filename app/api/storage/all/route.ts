import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// GET /api/storage/all - Get all storage values for user
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      'SELECT storage_key, storage_value FROM user_storage WHERE user_id = $1',
      [authResult.user.id]
    )

    // Convert to key-value object
    const storage: Record<string, string> = {}
    result.rows.forEach(row => {
      storage[row.storage_key] = row.storage_value
    })

    return NextResponse.json(storage)
  } catch (error) {
    console.error('Storage GET ALL error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
