import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { ultraCache, invalidateCache } from '@/lib/cache'

export async function POST(request: NextRequest) {
  try {
    // Require authentication and admin role for cache clearing
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admin users can clear cache
    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json().catch(() => ({}))
    const { pattern, type = 'all' } = body

    let clearedCount = 0
    const results: { [key: string]: number | string } = {}

    switch (type) {
      case 'all':
        // Clear all cache
        ultraCache.clear()
        results.ultraCache = 'cleared'
        
        // Clear analytics cache
        const analyticsCleared = invalidateCache('analytics_')
        results.analyticsCache = analyticsCleared
        
        // Clear API cache
        const apiCleared = invalidateCache('GET:')
        results.apiCache = apiCleared
        
        clearedCount = analyticsCleared + apiCleared
        break

      case 'analytics':
        // Clear only analytics cache
        clearedCount = invalidateCache('analytics_')
        results.analyticsCache = clearedCount
        break

      case 'api':
        // Clear only API cache
        clearedCount = invalidateCache('GET:')
        results.apiCache = clearedCount
        break

      case 'pattern':
        // Clear cache matching specific pattern
        if (!pattern) {
          return NextResponse.json({ error: 'Pattern required for pattern-based clearing' }, { status: 400 })
        }
        clearedCount = invalidateCache(pattern)
        results.patternCache = clearedCount
        break

      default:
        return NextResponse.json({ error: 'Invalid cache type' }, { status: 400 })
    }

    // Log cache clear action
    console.log(`Cache cleared by admin ${authResult.user.username}:`, {
      type,
      pattern,
      clearedCount,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully',
      clearedCount,
      results,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Cache clear error:', error)
    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    )
  }
}

// GET endpoint to check cache status
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admin users can view cache stats
    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get cache statistics
    const cacheStats = ultraCache.getStats()
    
    return NextResponse.json({
      success: true,
      stats: cacheStats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Cache stats error:', error)
    return NextResponse.json(
      { error: 'Failed to get cache stats' },
      { status: 500 }
    )
  }
}
