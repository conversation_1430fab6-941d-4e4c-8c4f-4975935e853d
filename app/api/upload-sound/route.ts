import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type (audio files only)
    const allowedTypes = [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/ogg',      // OGG
      'audio/mp4',      // M4A
      'audio/webm',     // WebM
      'audio/aac',      // AAC
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Invalid file type. Please upload an audio file (MP3, WAV, OGG, M4A, WebM, AAC)' 
      }, { status: 400 })
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File too large. Maximum size is 5MB' 
      }, { status: 400 })
    }

    // Create sounds directory if it doesn't exist
    const soundsDir = path.join(process.cwd(), 'public', 'sounds')
    try {
      await mkdir(soundsDir, { recursive: true })
    } catch (error) {
      // Directory might already exist, that's fine
    }

    // Generate unique filename to avoid conflicts
    const timestamp = Date.now()
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_') // Sanitize filename
    const fileName = `${timestamp}_${originalName}`
    const filePath = path.join(soundsDir, fileName)

    // Convert file to buffer and save
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // Return the public URL
    const publicUrl = `/sounds/${fileName}`

    return NextResponse.json({ 
      success: true, 
      url: publicUrl,
      filename: fileName,
      originalName: file.name,
      size: file.size
    })

  } catch (error) {
    console.error('Error uploading sound file:', error)
    return NextResponse.json({ 
      error: 'Failed to upload file' 
    }, { status: 500 })
  }
}
