import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Invalid token' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      valid: true,
      user: authResult.user
    })
  } catch (error) {
    console.error('Token validation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/auth/validate - Alternative method for GET requests
export async function GET(request: NextRequest) {
  return POST(request)
}
