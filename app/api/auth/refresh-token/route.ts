import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth, createToken, setAuthCookie } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Get current user from token
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Get fresh user data from database
    const result = await pool.query(
      'SELECT id, username, role, full_name, is_active FROM users WHERE id = $1',
      [authResult.user.id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const user = result.rows[0]

    if (!user.is_active) {
      return NextResponse.json({ error: "Account is disabled" }, { status: 401 })
    }

    // Create new JWT token with fresh user data
    const userData = {
      id: user.id,
      username: user.username,
      role: user.role,
      fullName: user.full_name
    }
    const newToken = createToken(userData)

    // Create response with updated user data
    const response = NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        fullName: user.full_name
      },
      token: newToken,
      message: 'Token refreshed successfully'
    })

    // Set new HttpOnly cookie
    setAuthCookie(response, newToken)

    return response

  } catch (error) {
    console.error('Token refresh error:', error)
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    )
  }
}
