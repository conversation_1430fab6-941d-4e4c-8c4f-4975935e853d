import { NextResponse } from "next/server"
import pool from '@/lib/db'

// POST /api/fix-table-assignments - Fix table assignments so each user has their own Table 1
export async function POST() {
  try {
    console.log('🔧 Fixing table assignments...')
    const results = []
    
    // Step 1: Get all users
    const users = await pool.query('SELECT id, username, full_name FROM users ORDER BY username')
    results.push({ 
      step: 'Get users', 
      status: 'success', 
      message: `Found ${users.rows.length} users`,
      users: users.rows.map(u => ({ id: u.id, username: u.username, fullName: u.full_name }))
    })
    
    // Step 2: Check current table assignments
    const currentTables = await pool.query(`
      SELECT id, number, name, assigned_user_id, assigned_username, is_active
      FROM gametables 
      ORDER BY number, assigned_username
    `)
    
    results.push({
      step: 'Current tables',
      status: 'info',
      message: `Found ${currentTables.rows.length} total tables`,
      tables: currentTables.rows
    })
    
    // Step 3: Find unassigned Table 1s
    const unassignedT1s = await pool.query(`
      SELECT id, number, name, assigned_user_id, assigned_username, is_active
      FROM gametables 
      WHERE number = 1 AND assigned_user_id IS NULL
      ORDER BY id
    `)
    
    results.push({
      step: 'Unassigned Table 1s',
      status: 'info',
      message: `Found ${unassignedT1s.rows.length} unassigned Table 1 entries`,
      unassignedT1s: unassignedT1s.rows
    })
    
    // Step 4: Assign Table 1 to each user (if they don't have one)
    let assignmentsMade = 0
    
    for (const user of users.rows) {
      // Check if user already has a Table 1
      const existingT1 = await pool.query(`
        SELECT id FROM gametables 
        WHERE number = 1 AND assigned_user_id = $1
      `, [user.id])
      
      if (existingT1.rows.length === 0) {
        // User doesn't have Table 1, assign one
        if (unassignedT1s.rows.length > assignmentsMade) {
          const tableToAssign = unassignedT1s.rows[assignmentsMade]
          
          await pool.query(`
            UPDATE gametables 
            SET assigned_user_id = $1, 
                assigned_username = $2,
                is_active = true,
                name = $3
            WHERE id = $4
          `, [user.id, user.username, `Table 1`, tableToAssign.id])
          
          assignmentsMade++
          
          results.push({
            step: 'Assign Table 1',
            status: 'success',
            message: `Assigned Table 1 (ID: ${tableToAssign.id}) to ${user.username}`,
            assignment: {
              tableId: tableToAssign.id,
              userId: user.id,
              username: user.username,
              tableName: 'Table 1'
            }
          })
        } else {
          // No more unassigned Table 1s, create a new one
          const newTable = await pool.query(`
            INSERT INTO gametables (number, name, table_type, hourly_rate, is_active, assigned_user_id, assigned_username)
            VALUES (1, 'Table 1', 'billiard', 400.00, true, $1, $2)
            RETURNING id
          `, [user.id, user.username])
          
          results.push({
            step: 'Create Table 1',
            status: 'success',
            message: `Created new Table 1 (ID: ${newTable.rows[0].id}) for ${user.username}`,
            newTable: {
              tableId: newTable.rows[0].id,
              userId: user.id,
              username: user.username,
              tableName: 'Table 1'
            }
          })
        }
      } else {
        results.push({
          step: 'Check existing',
          status: 'info',
          message: `${user.username} already has Table 1 (ID: ${existingT1.rows[0].id})`
        })
      }
    }
    
    // Step 5: Final verification
    const finalTables = await pool.query(`
      SELECT number, assigned_username, COUNT(*) as count
      FROM gametables 
      WHERE assigned_user_id IS NOT NULL
      GROUP BY number, assigned_username
      ORDER BY number, assigned_username
    `)
    
    return NextResponse.json({
      success: true,
      message: 'Table assignments fixed successfully!',
      results,
      finalAssignments: finalTables.rows,
      summary: {
        totalUsers: users.rows.length,
        assignmentsMade,
        finalTableCount: (await pool.query('SELECT COUNT(*) FROM gametables')).rows[0].count
      }
    })

  } catch (error) {
    console.error('Table assignment fix error:', error)
    return NextResponse.json({ 
      error: "Failed to fix table assignments", 
      details: error.message 
    }, { status: 500 })
  }
}
