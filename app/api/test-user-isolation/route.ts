import { NextResponse } from "next/server"
import pool from '@/lib/db'

// GET /api/test-user-isolation - Test user table isolation
export async function GET() {
  try {
    console.log('🔍 Testing User Table Isolation...')
    
    // Get all users
    const users = await pool.query('SELECT id, username, full_name FROM users ORDER BY username')
    
    // Check game tables by user
    const userTableData = []
    for (const user of users.rows) {
      const userTables = await pool.query(
        'SELECT * FROM gametables WHERE assigned_user_id = $1 ORDER BY number',
        [user.id]
      )
      
      userTableData.push({
        user: {
          id: user.id,
          username: user.username,
          fullName: user.full_name
        },
        tables: userTables.rows.map(table => ({
          id: table.id,
          number: table.number,
          name: table.name,
          isActive: table.is_active
        }))
      })
    }
    
    // Check for table number conflicts (multiple users with same table number)
    const tableConflicts = await pool.query(`
      SELECT 
        number,
        COUNT(*) as user_count,
        array_agg(assigned_username ORDER BY assigned_username) as users,
        array_agg(id ORDER BY assigned_username) as table_ids
      FROM gametables 
      WHERE assigned_user_id IS NOT NULL
      GROUP BY number
      HAVING COUNT(*) > 1
      ORDER BY number
    `)
    
    // Check unassigned tables
    const unassigned = await pool.query('SELECT * FROM gametables WHERE assigned_user_id IS NULL ORDER BY number')
    
    // Get total counts
    const totalTables = await pool.query('SELECT COUNT(*) as count FROM gametables')
    const assignedTables = await pool.query('SELECT COUNT(*) as count FROM gametables WHERE assigned_user_id IS NOT NULL')
    
    return NextResponse.json({
      success: true,
      message: 'User isolation test completed',
      data: {
        users: userTableData,
        tableConflicts: tableConflicts.rows.map(conflict => ({
          tableNumber: conflict.number,
          userCount: conflict.user_count,
          users: conflict.users,
          tableIds: conflict.table_ids
        })),
        unassignedTables: unassigned.rows.map(table => ({
          id: table.id,
          number: table.number,
          name: table.name,
          isActive: table.is_active
        })),
        summary: {
          totalUsers: users.rows.length,
          totalTables: parseInt(totalTables.rows[0].count),
          assignedTables: parseInt(assignedTables.rows[0].count),
          unassignedTables: unassigned.rows.length,
          usersWithTables: userTableData.filter(u => u.tables.length > 0).length,
          perfectIsolation: unassigned.rows.length === 0
        }
      }
    })

  } catch (error) {
    console.error('User isolation test error:', error)
    return NextResponse.json({ 
      error: "Failed to test user isolation", 
      details: error.message 
    }, { status: 500 })
  }
}
