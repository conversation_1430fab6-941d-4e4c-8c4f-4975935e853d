import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Only admin users can access login settings
    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const result = await pool.query('SELECT * FROM login_settings ORDER BY id DESC LIMIT 1')
    
    if (result.rows.length === 0) {
      // Return default settings if none exist
      return NextResponse.json({
        waiters_section_enabled: true,
        waiters_section_title: 'Waiters Accounts:',
        waiter1_display_name: 'Waiter One:',
        waiter1_username: 'waiter1',
        waiter1_password: 'waiter1',
        waiter1_enabled: true,
        waiter2_display_name: 'Waiter Two:',
        waiter2_username: 'waiter2',
        waiter2_password: 'waiter2',
        waiter2_enabled: true
      })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch login settings" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Only admin users can modify login settings
    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const {
      waiters_section_enabled,
      waiters_section_title,
      waiter1_display_name,
      waiter1_username,
      waiter1_password,
      waiter1_enabled,
      waiter2_display_name,
      waiter2_username,
      waiter2_password,
      waiter2_enabled
    } = body

    // Check if settings exist
    const existingResult = await pool.query('SELECT id FROM login_settings LIMIT 1')
    
    if (existingResult.rows.length === 0) {
      // Insert new settings
      const result = await pool.query(`
        INSERT INTO login_settings (
          waiters_section_enabled,
          waiters_section_title,
          waiter1_display_name,
          waiter1_username,
          waiter1_password,
          waiter1_enabled,
          waiter2_display_name,
          waiter2_username,
          waiter2_password,
          waiter2_enabled,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
        RETURNING *
      `, [
        waiters_section_enabled,
        waiters_section_title,
        waiter1_display_name,
        waiter1_username,
        waiter1_password,
        waiter1_enabled,
        waiter2_display_name,
        waiter2_username,
        waiter2_password,
        waiter2_enabled
      ])
      return NextResponse.json(result.rows[0])
    } else {
      // Update existing settings
      const result = await pool.query(`
        UPDATE login_settings SET
          waiters_section_enabled = $1,
          waiters_section_title = $2,
          waiter1_display_name = $3,
          waiter1_username = $4,
          waiter1_password = $5,
          waiter1_enabled = $6,
          waiter2_display_name = $7,
          waiter2_username = $8,
          waiter2_password = $9,
          waiter2_enabled = $10,
          updated_at = NOW()
        WHERE id = $11
        RETURNING *
      `, [
        waiters_section_enabled,
        waiters_section_title,
        waiter1_display_name,
        waiter1_username,
        waiter1_password,
        waiter1_enabled,
        waiter2_display_name,
        waiter2_username,
        waiter2_password,
        waiter2_enabled,
        existingResult.rows[0].id
      ])
      return NextResponse.json(result.rows[0])
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to save login settings" }, { status: 500 })
  }
}
