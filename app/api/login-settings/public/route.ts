import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const result = await pool.query('SELECT * FROM login_settings ORDER BY id DESC LIMIT 1')

    if (result.rows.length === 0) {
      // Return default settings if none exist
      return NextResponse.json({
        waiters_section_enabled: true,
        waiters_section_title: 'Waiters Accounts:',
        waiter1_display_name: 'Waiter One:',
        waiter1_username: 'waiter1',
        waiter1_password: 'waiter1',
        waiter1_enabled: true,
        waiter2_display_name: 'Waiter Two:',
        waiter2_username: 'waiter2',
        waiter2_password: 'waiter2',
        waiter2_enabled: true,
        database_online: true
      })
    }

    return NextResponse.json({
      ...result.rows[0],
      database_online: true
    })
  } catch (error) {
    console.error('Database error:', error)
    // Return settings with waiters section disabled when database is offline
    return NextResponse.json({
      waiters_section_enabled: false, // Hide waiters section when DB is offline
      waiters_section_title: 'Waiters Accounts:',
      waiter1_display_name: 'Waiter One:',
      waiter1_username: 'waiter1',
      waiter1_password: 'waiter1',
      waiter1_enabled: false,
      waiter2_display_name: 'Waiter Two:',
      waiter2_username: 'waiter2',
      waiter2_password: 'waiter2',
      waiter2_enabled: false,
      database_online: false,
      database_error: error instanceof Error ? error.message : 'Database connection failed'
    })
  }
}
