import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user

    // Get all game tables from database
    const allGameTablesResult = await pool.query('SELECT * FROM gametables ORDER BY number')
    
    // Get user-specific game tables (what the games interface should show)
    const userGameTablesResult = await pool.query(
      'SELECT * FROM gametables WHERE assigned_user_id IS NULL OR assigned_user_id = $1 ORDER BY number',
      [user.id]
    )

    // Get only active user game tables (what actually gets displayed)
    const activeUserGameTables = userGameTablesResult.rows.filter(t => t.is_active)

    return NextResponse.json({
      currentUser: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        role: user.role
      },
      allGameTables: {
        count: allGameTablesResult.rows.length,
        tables: allGameTablesResult.rows.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          hourlyRate: t.hourly_rate,
          tableType: t.table_type,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      userGameTables: {
        count: userGameTablesResult.rows.length,
        tables: userGameTablesResult.rows.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          hourlyRate: t.hourly_rate,
          tableType: t.table_type,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      activeUserGameTables: {
        count: activeUserGameTables.length,
        tables: activeUserGameTables.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          hourlyRate: t.hourly_rate,
          tableType: t.table_type,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      summary: {
        totalGameTablesInDB: allGameTablesResult.rows.length,
        gameTablesVisibleToUser: userGameTablesResult.rows.length,
        activeGameTablesForUser: activeUserGameTables.length,
        gameTablesAssignedToThisUser: allGameTablesResult.rows.filter(t => t.assigned_user_id === user.id).length,
        unassignedGameTables: allGameTablesResult.rows.filter(t => t.assigned_user_id === null).length
      }
    })

  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({ 
      error: "Debug failed", 
      details: error.message 
    }, { status: 500 })
  }
}
