import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Simple database connectivity test with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database check timeout')), 5000) // 5 second timeout
    })

    const queryPromise = pool.query('SELECT 1 as status')

    const result = await Promise.race([queryPromise, timeoutPromise]) as any

    if (result.rows && result.rows.length > 0) {
      return NextResponse.json({
        online: true,
        status: 'connected',
        message: 'Database is online and accessible',
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        online: false,
        status: 'error',
        message: 'Database query returned no results',
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error('Database connectivity error:', error)

    return NextResponse.json({
      online: false,
      status: 'offline',
      message: error instanceof Error ? error.message : 'Database connection failed',
      timestamp: new Date().toISOString()
    }, { status: 503 })
  }
}

// Also support POST for consistency
export async function POST(request: NextRequest) {
  return GET(request)
}
