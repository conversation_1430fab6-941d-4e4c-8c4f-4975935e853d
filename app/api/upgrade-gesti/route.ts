import { NextResponse } from "next/server"
import pool from '@/lib/db'

export async function GET() {
  try {
    console.log('🔄 Upgrading Gesti to admin...')

    // Update Gest<PERSON>'s role to admin
    const result = await pool.query(
      'UPDATE users SET role = $1 WHERE username = $2 RETURNING id, username, role, full_name',
      ['admin', 'gesti']
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Gesti user not found'
      })
    }

    console.log('✅ Gesti upgraded to admin successfully:', result.rows[0])

    return NextResponse.json({
      success: true,
      message: '<PERSON><PERSON><PERSON> upgraded to admin successfully',
      user: result.rows[0]
    })
  } catch (error) {
    console.error('❌ Failed to upgrade Gesti:', error)
    return NextResponse.json({ 
      success: false,
      error: error.message 
    }, { status: 500 })
  }
}
