import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

// GET /api/tables/status - Get all tables with their current status
export async function GET() {
  try {
    // Get basic table status from gametables (no reservation system)
    const result = await pool.query(`
      SELECT
        number,
        name,
        is_active,
        hourly_rate,
        table_type,
        NULL as current_user_id,
        NULL as current_username,
        NULL as locked_at,
        NULL as session_expires_at,
        'available' as status
      FROM gametables
      ORDER BY number
    `)

    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error fetching table status:', error)
    return NextResponse.json(
      { error: 'Failed to fetch table status' },
      { status: 500 }
    )
  }
}

// POST /api/tables/status/heartbeat - Keep session alive (simplified)
export async function POST(request: NextRequest) {
  try {
    const { sessionId, tableNumber } = await request.json()

    if (!sessionId || !tableNumber) {
      return NextResponse.json(
        { error: 'Session ID and table number are required' },
        { status: 400 }
      )
    }

    // For now, just return success - heartbeat functionality can be added later
    return NextResponse.json({
      success: true,
      expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes from now
    })
  } catch (error) {
    console.error('Error updating session heartbeat:', error)
    return NextResponse.json(
      { error: 'Failed to update session' },
      { status: 500 }
    )
  }
}
