import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// POST /api/tables/bulk-create - Create a set of tables for a user (1-10)
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { userId, username, tableCount = 10, startNumber = 1 } = body

    // Validate required fields
    if (!userId || !username) {
      return NextResponse.json({ error: "User ID and username are required" }, { status: 400 })
    }

    if (tableCount < 1 || tableCount > 50) {
      return NextResponse.json({ error: "Table count must be between 1 and 50" }, { status: 400 })
    }

    // Check if user exists
    const userCheck = await pool.query('SELECT id, username, full_name FROM users WHERE id = $1', [userId])
    if (userCheck.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const user = userCheck.rows[0]
    const results = []
    const errors = []

    // Create tables for the user
    for (let i = 0; i < tableCount; i++) {
      const tableNumber = startNumber + i
      const tableName = `${username} - Table ${tableNumber}`

      try {
        // Check if this user already has a table with this number
        const existingTable = await pool.query(
          'SELECT id FROM tables WHERE number = $1 AND assigned_user_id = $2',
          [tableNumber, userId]
        )

        if (existingTable.rows.length > 0) {
          errors.push({
            tableNumber,
            error: `User already has table ${tableNumber}`
          })
          continue
        }

        // Create the table
        const result = await pool.query(
          'INSERT INTO tables (number, name, is_active, hourly_rate, assigned_user_id, assigned_username) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
          [tableNumber, tableName, true, 0, userId, username]
        )

        results.push({
          tableNumber,
          tableId: result.rows[0].id,
          tableName,
          success: true
        })

      } catch (error) {
        console.error(`Failed to create table ${tableNumber} for user ${username}:`, error)
        errors.push({
          tableNumber,
          error: error.message
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Created ${results.length} tables for ${username}`,
      user: {
        id: userId,
        username,
        fullName: user.full_name
      },
      created: results,
      errors,
      summary: {
        requested: tableCount,
        created: results.length,
        failed: errors.length
      }
    })

  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create tables" }, { status: 500 })
  }
}

// DELETE /api/tables/bulk-create - Remove all tables for a user
export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 })
    }

    // Get user info
    const userCheck = await pool.query('SELECT username, full_name FROM users WHERE id = $1', [userId])
    if (userCheck.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const user = userCheck.rows[0]

    // Delete all tables assigned to this user
    const result = await pool.query(
      'DELETE FROM tables WHERE assigned_user_id = $1 RETURNING number, name',
      [userId]
    )

    return NextResponse.json({
      success: true,
      message: `Deleted ${result.rows.length} tables for ${user.username}`,
      user: {
        id: parseInt(userId),
        username: user.username,
        fullName: user.full_name
      },
      deletedTables: result.rows,
      count: result.rows.length
    })

  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete tables" }, { status: 500 })
  }
}
