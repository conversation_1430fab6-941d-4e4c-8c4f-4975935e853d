import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body = await request.json()
    const { name, isActive, hourlyRate } = body
    const resolvedParams = await params
    const tableNumber = resolvedParams.id

    // Validate required fields
    if (!name || hourlyRate === undefined) {
      return NextResponse.json({ error: "Name and hourly rate are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE tables SET name = $1, is_active = $2, hourly_rate = $3 WHERE number = $4 RETURNING *',
      [name, isActive, hourlyRate, tableNumber]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Table not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update table" }, { status: 500 })
  }
}