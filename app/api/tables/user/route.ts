import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user

    // Complete user isolation - each user sees ONLY their assigned tables
    // No shared or unassigned tables to prevent cross-user interference
    const result = await pool.query(
      'SELECT * FROM tables WHERE assigned_user_id = $1 ORDER BY number',
      [user.id]
    )

    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch user tables" }, { status: 500 })
  }
}
