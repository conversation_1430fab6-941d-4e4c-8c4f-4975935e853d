import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
})

export async function GET() {
  try {
    const client = await pool.connect()

    try {
      // Get currency settings
      const result = await client.query(
        'SELECT * FROM currency_settings ORDER BY id DESC LIMIT 1'
      )

      if (result.rows.length === 0) {
        // Return default settings if none exist
        return NextResponse.json({
          currency: 'Albanian Lek',
          symbol: 'L',
          show_decimals: false,
          tax_included: false,
          tax_enabled: true,
          tax_rate: 20,
          qr_code_enabled: false,
          qr_code_url: ''
        })
      }

      return NextResponse.json(result.rows[0])
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Failed to fetch currency settings' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { currency, symbol, showDecimals, taxIncluded, taxEnabled, taxRate, qrCodeEnabled, qrCodeUrl } = body

    const client = await pool.connect()

    try {
      // Check if currency settings exist
      const existingResult = await client.query(
        'SELECT id FROM currency_settings ORDER BY id DESC LIMIT 1'
      )

      if (existingResult.rows.length > 0) {
        // Update existing settings
        const result = await client.query(
          `UPDATE currency_settings
           SET currency = $1, symbol = $2, show_decimals = $3, tax_included = $4, tax_enabled = $5, tax_rate = $6, qr_code_enabled = $7, qr_code_url = $8, updated_at = NOW()
           WHERE id = $9
           RETURNING *`,
          [currency, symbol, showDecimals, taxIncluded, taxEnabled, taxRate, qrCodeEnabled, qrCodeUrl, existingResult.rows[0].id]
        )
        return NextResponse.json(result.rows[0])
      } else {
        // Insert new settings
        const result = await client.query(
          `INSERT INTO currency_settings (currency, symbol, show_decimals, tax_included, tax_enabled, tax_rate, qr_code_enabled, qr_code_url, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
           RETURNING *`,
          [currency, symbol, showDecimals, taxIncluded, taxEnabled, taxRate, qrCodeEnabled, qrCodeUrl]
        )
        return NextResponse.json(result.rows[0])
      }
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Failed to save currency settings' }, { status: 500 })
  }
}
