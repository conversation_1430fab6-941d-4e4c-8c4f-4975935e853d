import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body = await request.json()
    const { endTime, duration, cost, status } = body
    const resolvedParams = await params
    const id = resolvedParams.id

    // Validate required fields
    if (!endTime || duration === undefined || cost === undefined || !status) {
      return NextResponse.json({ error: "End time, duration, cost, and status are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE games SET end_time = $1, duration = $2, cost = $3, status = $4 WHERE id = $5 RETURNING *',
      [new Date(endTime), duration, cost, status, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update game" }, { status: 500 })
  }
}