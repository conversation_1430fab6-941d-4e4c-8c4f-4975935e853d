import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user

    // Get all tables from database
    const allTablesResult = await pool.query('SELECT * FROM tables ORDER BY number')
    
    // Get user-specific tables (what the bar interface should show)
    const userTablesResult = await pool.query(
      'SELECT * FROM tables WHERE assigned_user_id IS NULL OR assigned_user_id = $1 ORDER BY number',
      [user.id]
    )

    // Get only active user tables (what actually gets displayed)
    const activeUserTables = userTablesResult.rows.filter(t => t.is_active)

    return NextResponse.json({
      currentUser: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        role: user.role
      },
      allTables: {
        count: allTablesResult.rows.length,
        tables: allTablesResult.rows.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      userTables: {
        count: userTablesResult.rows.length,
        tables: userTablesResult.rows.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      activeUserTables: {
        count: activeUserTables.length,
        tables: activeUserTables.map(t => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
      },
      summary: {
        totalTablesInDB: allTablesResult.rows.length,
        tablesVisibleToUser: userTablesResult.rows.length,
        activeTablesForUser: activeUserTables.length,
        tablesAssignedToThisUser: allTablesResult.rows.filter(t => t.assigned_user_id === user.id).length,
        unassignedTables: allTablesResult.rows.filter(t => t.assigned_user_id === null).length
      }
    })

  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({ 
      error: "Debug failed", 
      details: error.message 
    }, { status: 500 })
  }
}
