import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/receipts/bulk-delete called')

    // Authenticate user
    const authResult = await requireAuth(request)
    console.log('User authentication result:', authResult.success ? { userId: authResult.user?.id, role: authResult.user?.role } : 'No user')

    if (!authResult.success || !authResult.user) {
      console.log('Authentication failed - no user')
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user
    // Check if user has permission to delete (admin only)
    if (user.role !== 'admin') {
      console.log('Permission denied - user role:', user.role)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get data from request body
    const body = await request.json()
    const { items } = body
    console.log('Bulk delete request items:', items)

    if (!items || !Array.isArray(items) || items.length === 0) {
      console.log('Invalid items array')
      return NextResponse.json({ error: 'Items array is required' }, { status: 400 })
    }

    console.log('Permission check passed - proceeding with bulk deletion')

    const results = []
    const errors = []

    // Process each item
    for (const item of items) {
      const { id, source } = item

      if (!id || !source) {
        errors.push(`Invalid item: missing id or source - ${JSON.stringify(item)}`)
        continue
      }

      try {
        let result
        console.log(`Attempting to delete from ${source} table, id: ${id}`)

        switch (source) {
          case 'receipt':
            result = await pool.query('DELETE FROM receipts WHERE id = $1 RETURNING *', [id])
            break
          case 'game':
            result = await pool.query('DELETE FROM games WHERE id = $1 RETURNING *', [id])
            break
          case 'order':
            result = await pool.query('DELETE FROM orders WHERE id = $1 RETURNING *', [id])
            break
          default:
            errors.push(`Invalid source type: ${source} for id: ${id}`)
            continue
        }

        console.log(`Delete query result for ${id}:`, { rowCount: result.rowCount, rows: result.rows.length })

        if (result.rows.length === 0) {
          errors.push(`Transaction not found: ${id} in ${source}`)
        } else {
          results.push({
            id,
            source,
            deleted: result.rows[0]
          })
          console.log(`Successfully deleted transaction: ${id} from ${source}`)
        }
      } catch (error) {
        console.error(`Failed to delete ${id} from ${source}:`, error)
        errors.push(`Failed to delete ${id} from ${source}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    const response = {
      success: true,
      deletedCount: results.length,
      totalRequested: items.length,
      results,
      errors: errors.length > 0 ? errors : undefined
    }

    console.log('Bulk deletion completed:', response)

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to process bulk delete:', error)
    return NextResponse.json({
      error: 'Failed to process bulk delete',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
