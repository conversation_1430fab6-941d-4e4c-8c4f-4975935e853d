import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, data, printedBy, printedAt } = body

    console.log('Received receipt data:', { type, data, printedBy, printedAt });

    // Validate required fields
    if (!type || !data || !data.tableNumber || !data.cost) {
      console.error('Missing required fields:', { type, data });
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields",
          details: "type, data.tableNumber, and data.cost are required"
        },
        { status: 400 }
      );
    }

    // Generate receipt number
    const gameCount = await pool.query('SELECT COUNT(*) FROM receipts WHERE type = $1', ['game'])
    const orderCount = await pool.query('SELECT COUNT(*) FROM receipts WHERE type = $1', ['order'])

    const receiptNumber = type === "game"
      ? `G${String(parseInt(gameCount.rows[0].count) + 1).padStart(3, "0")}`
      : `O${String(parseInt(orderCount.rows[0].count) + 1).padStart(3, "0")}`

    console.log('Generated receipt number:', receiptNumber);

    try {
      const params = [
        type,
        receiptNumber,
        data.tableNumber,
        data.cost,
        type === "order" ? Math.round(data.cost * 0.2) : null,
        data.duration || null,
        printedAt ? new Date(printedAt) : new Date(),
        printedBy || "System",
        "printed",
        printedBy === "System Auto-Print" ? true : false,
        type === "game" ? JSON.stringify(data) : null,
        type === "order" ? JSON.stringify(data) : null
      ];

      console.log('Inserting receipt with params:', params);

      const result = await pool.query(
        `INSERT INTO receipts (
          type, receipt_number, table_number, amount, tax, duration,
          printed_at, printed_by, status, is_auto_print, game_data, order_data
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) RETURNING *`,
        params
      )

      console.log('Receipt inserted successfully:', result.rows[0]);

      return NextResponse.json({
        success: true,
        receiptId: result.rows[0].id,
        receiptNumber: result.rows[0].receipt_number,
        message: "Receipt printed and logged successfully",
      })
    } catch (dbError) {
      console.error("Database error details:", {
        error: dbError,
        message: dbError instanceof Error ? dbError.message : String(dbError),
        stack: dbError instanceof Error ? dbError.stack : undefined
      });
      return NextResponse.json(
        {
          success: false,
          error: "Database error",
          details: dbError instanceof Error ? dbError.message : String(dbError)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Print error details:", {
      error,
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      {
        success: false,
        error: "Failed to print receipt",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user

    const { searchParams } = new URL(request.url)
    const type = searchParams.get("type")
    const tableNumber = searchParams.get("tableNumber")
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = (page - 1) * limit

    // Determine if user can see all data or only their own
    const isAdmin = user.role === 'admin'

    const params: any[] = []
    let paramIndex = 0

    // Build WHERE conditions for games
    let gameConditions = "g.status = 'completed'"

    // Add user filtering for non-admin users
    if (!isAdmin) {
      paramIndex++
      gameConditions += ` AND g.created_by = $${paramIndex}`
      params.push(user.id)
    }

    if (tableNumber) {
      paramIndex++
      gameConditions += ` AND g.table_number = $${paramIndex}`
      params.push(parseInt(tableNumber))
    }
    if (startDate) {
      paramIndex++
      gameConditions += ` AND g.end_time >= $${paramIndex}`
      params.push(new Date(startDate))
    }
    if (endDate) {
      paramIndex++
      gameConditions += ` AND g.end_time <= $${paramIndex}`
      params.push(new Date(endDate))
    }

    // Build WHERE conditions for orders
    let orderConditions = "o.status = 'completed'"

    // Add user filtering for non-admin users
    if (!isAdmin) {
      const userParamIndex = params.findIndex(p => p === user.id) + 1
      orderConditions += ` AND o.created_by = $${userParamIndex}`
    }

    if (tableNumber) {
      // Use the same parameter as games
      const tableParamIndex = params.findIndex(p => p === parseInt(tableNumber)) + 1
      orderConditions += ` AND o.table_number = $${tableParamIndex}`
    }
    if (startDate) {
      // Use the same parameter as games
      const startParamIndex = params.findIndex(p => p instanceof Date && p.getTime() === new Date(startDate).getTime()) + 1
      orderConditions += ` AND o.created_at >= $${startParamIndex}`
    }
    if (endDate) {
      // Use the same parameter as games
      const endParamIndex = params.findIndex(p => p instanceof Date && p.getTime() === new Date(endDate).getTime()) + 1
      orderConditions += ` AND o.created_at <= $${endParamIndex}`
    }

    let query = `
      WITH all_transactions AS (
    `

    // Add games query if type is not 'order'
    if (type !== 'order') {
      query += `
        -- Games
        SELECT
          'game' as source,
          g.id,
          'game' as type,
          NULL as receipt_number,
          g.table_number,
          g.cost as amount,
          NULL as tax,
          g.duration,
          g.end_time as timestamp,
          'System' as printed_by,
          g.status,
          false as is_auto_print,
          jsonb_build_object(
            'startTime', g.start_time,
            'endTime', g.end_time,
            'duration', g.duration,
            'cost', g.cost
          ) as game_data,
          NULL as order_data,
          u.username as created_by_username,
          u.full_name as created_by_name,
          u.role as created_by_role
        FROM games g
        LEFT JOIN users u ON g.created_by = u.id
        WHERE ${gameConditions}
      `
    }

    // Add UNION ALL if both types are included
    if (type !== 'order' && type !== 'game') {
      query += ` UNION ALL `
    }

    // Add orders query if type is not 'game'
    if (type !== 'game') {
      query += `
        -- Orders
        SELECT
          'order' as source,
          o.id,
          'order' as type,
          NULL as receipt_number,
          o.table_number,
          o.total as amount,
          ROUND(o.total * 0.2) as tax,
          NULL as duration,
          o.created_at as timestamp,
          'System' as printed_by,
          o.status,
          false as is_auto_print,
          NULL as game_data,
          jsonb_build_object(
            'items', o.items,
            'total', o.total
          ) as order_data,
          u.username as created_by_username,
          u.full_name as created_by_name,
          u.role as created_by_role
        FROM orders o
        LEFT JOIN users u ON o.created_by = u.id
        WHERE ${orderConditions}
      `
    }

    query += `
      )
      SELECT * FROM all_transactions
      ORDER BY timestamp DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `

    // Add pagination parameters
    params.push(limit, offset)

    // Get total count for pagination info
    let countQuery = `
      WITH all_transactions AS (
    `

    // Add games count query if type is not 'order'
    if (type !== 'order') {
      countQuery += `
        SELECT 'game' as source FROM games g
        LEFT JOIN users u ON g.created_by = u.id
        WHERE ${gameConditions}
      `
    }

    // Add UNION ALL if both types are included
    if (type !== 'order' && type !== 'game') {
      countQuery += ` UNION ALL `
    }

    // Add orders count query if type is not 'game'
    if (type !== 'game') {
      countQuery += `
        SELECT 'order' as source FROM orders o
        LEFT JOIN users u ON o.created_by = u.id
        WHERE ${orderConditions}
      `
    }

    countQuery += `
      )
      SELECT COUNT(*) as total FROM all_transactions
    `

    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)) // Remove limit and offset for count query
    ])

    // Format the response
    const formattedTransactions = result.rows.map(row => ({
      id: row.id,
      source: row.source,
      type: row.type,
      receiptNumber: row.receipt_number,
      tableNumber: row.table_number,
      amount: row.amount,
      tax: row.tax,
      duration: row.duration,
      timestamp: row.timestamp,
      printedBy: row.printed_by,
      status: row.status,
      isAutoPrint: row.is_auto_print,
      gameData: row.game_data,
      orderData: row.order_data,
      createdByUsername: row.created_by_username,
      createdByName: row.created_by_name,
      createdByRole: row.created_by_role
    }))

    const totalCount = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch transactions" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, status } = body

    const result = await pool.query(
      'UPDATE receipts SET status = $1, updated_at = $2 WHERE id = $3 RETURNING *',
      [status, new Date(), id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Receipt not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update receipt" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('DELETE /api/receipts called')

    // Authenticate user
    const authResult = await requireAuth(request)
    console.log('User authentication result:', authResult.success ? { userId: authResult.user?.id, role: authResult.user?.role } : 'No user')

    if (!authResult.success || !authResult.user) {
      console.log('Authentication failed - no user')
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user

    // Get data from request body instead of query params
    const body = await request.json()
    const { id, source } = body
    console.log('DELETE request body:', { id, source })

    if (!id) {
      console.log('Missing transaction ID')
      return NextResponse.json({ error: 'Transaction ID is required' }, { status: 400 })
    }

    if (!source) {
      console.log('Missing source type')
      return NextResponse.json({ error: 'Source type is required' }, { status: 400 })
    }

    // Check if user has permission to delete (admin only for now)
    if (user.role !== 'admin') {
      console.log('Permission denied - user role:', user.role)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    console.log('Permission check passed - proceeding with deletion')

    let result;
    console.log('Attempting to delete from source:', source)

    switch (source) {
      case 'receipt':
        console.log('Deleting from receipts table, id:', id)
        result = await pool.query('DELETE FROM receipts WHERE id = $1 RETURNING *', [id])
        break
      case 'game':
        console.log('Deleting from games table, id:', id)
        result = await pool.query('DELETE FROM games WHERE id = $1 RETURNING *', [id])
        break
      case 'order':
        console.log('Deleting from orders table, id:', id)
        result = await pool.query('DELETE FROM orders WHERE id = $1 RETURNING *', [id])
        break
      default:
        console.log('Invalid source type:', source)
        return NextResponse.json({ error: 'Invalid source type' }, { status: 400 })
    }

    console.log('Delete query result:', { rowCount: result.rowCount, rows: result.rows.length })

    if (result.rows.length === 0) {
      console.log('Transaction not found in database')
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 })
    }

    console.log('Successfully deleted transaction:', result.rows[0])
    return NextResponse.json({ success: true, deleted: result.rows[0] })
  } catch (error) {
    console.error('Failed to delete transaction:', error)
    return NextResponse.json({ error: 'Failed to delete transaction' }, { status: 500 })
  }
}
