import { NextResponse } from "next/server"
import pool from '@/lib/db'
import bcrypt from 'bcryptjs'

export async function GET() {
  try {
    console.log('🔄 Setting up admin user...')

    // Hash the password properly
    const hashedPassword = await bcrypt.hash('admin123', 10)
    console.log('🔐 Password hashed successfully')

    // First, delete any existing admin user to avoid conflicts
    await pool.query('DELETE FROM users WHERE username = $1', ['admin'])
    console.log('🗑️ Removed any existing admin user')

    // Create new admin user
    const result = await pool.query(
      'INSERT INTO users (username, password_hash, role, full_name, is_active) VALUES ($1, $2, $3, $4, $5) RETURNING id, username, role, full_name',
      ['admin', hashedPassword, 'admin', 'Administrator', true]
    )

    console.log('✅ Admin user created successfully:', result.rows[0])

    // Also check all users to see what we have
    const allUsers = await pool.query('SELECT id, username, role, full_name, is_active FROM users ORDER BY id')
    console.log('👥 All users in database:', allUsers.rows)

    return NextResponse.json({
      success: true,
      message: 'Admin user setup completed successfully',
      adminUser: result.rows[0],
      allUsers: allUsers.rows
    })
  } catch (error) {
    console.error('❌ Failed to setup admin user:', error)
    return NextResponse.json({ 
      success: false,
      error: error.message 
    }, { status: 500 })
  }
}
