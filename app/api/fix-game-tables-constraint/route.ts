import { NextResponse } from "next/server"
import pool from '@/lib/db'

// POST /api/fix-game-tables-constraint - Fix database constraint to allow duplicate game table numbers
export async function POST() {
  try {
    console.log('🔄 Running game tables constraint migration...')
    const results = []

    // Step 1: Remove the unique constraint on game table number
    try {
      await pool.query('ALTER TABLE gametables DROP CONSTRAINT IF EXISTS gametables_number_key')
      results.push({ step: 'Remove unique constraint', status: 'success', message: 'Removed unique constraint on gametables.number' })
    } catch (error) {
      results.push({ step: 'Remove unique constraint', status: 'warning', message: error.message })
    }

    // Step 2: Add composite unique constraint (number + assigned_user_id)
    try {
      await pool.query(`
        ALTER TABLE gametables ADD CONSTRAINT unique_gametable_number_per_user 
        UNIQUE (number, assigned_user_id)
      `)
      results.push({ step: 'Add composite constraint', status: 'success', message: 'Added composite unique constraint (number, assigned_user_id)' })
    } catch (error) {
      if (error.message.includes('already exists')) {
        results.push({ step: 'Add composite constraint', status: 'success', message: 'Composite constraint already exists' })
      } else {
        results.push({ step: 'Add composite constraint', status: 'error', message: error.message })
      }
    }

    // Step 3: Ensure user assignment columns exist
    try {
      await pool.query(`
        ALTER TABLE gametables 
        ADD COLUMN IF NOT EXISTS assigned_user_id INTEGER,
        ADD COLUMN IF NOT EXISTS assigned_username VARCHAR(100)
      `)
      results.push({ step: 'Add user assignment columns', status: 'success', message: 'Ensured assigned_user_id and assigned_username columns exist' })
    } catch (error) {
      results.push({ step: 'Add user assignment columns', status: 'warning', message: error.message })
    }

    // Step 4: Check current constraints
    const constraintCheck = await pool.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'gametables' AND constraint_type = 'UNIQUE'
    `)

    // Step 5: Check if columns exist
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'gametables' 
      AND column_name IN ('assigned_user_id', 'assigned_username')
    `)

    return NextResponse.json({
      success: true,
      message: 'Game tables migration completed successfully!',
      description: 'You can now create multiple game tables with the same number assigned to different users',
      results,
      currentConstraints: constraintCheck.rows,
      existingColumns: columnCheck.rows,
      summary: {
        total: results.length,
        successful: results.filter(r => r.status === 'success').length,
        warnings: results.filter(r => r.status === 'warning').length,
        errors: results.filter(r => r.status === 'error').length
      }
    })

  } catch (error) {
    console.error('Game tables migration error:', error)
    return NextResponse.json({ 
      error: "Game tables migration failed", 
      details: error.message 
    }, { status: 500 })
  }
}
