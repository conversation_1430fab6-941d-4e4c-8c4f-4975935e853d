import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// GET /api/draft-orders - Get all draft orders for user
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      'SELECT table_number, order_data FROM draft_orders WHERE user_id = $1 AND expires_at > NOW()',
      [authResult.user.id]
    )

    // Convert to table_number -> order_data mapping
    const draftOrders: Record<number, any> = {}
    result.rows.forEach(row => {
      draftOrders[row.table_number] = row.order_data
    })

    return NextResponse.json(draftOrders)
  } catch (error) {
    console.error('Error fetching draft orders:', error)
    return NextResponse.json(
      { error: 'Failed to fetch draft orders' },
      { status: 500 }
    )
  }
}

// POST /api/draft-orders - Save/update draft order
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { tableNumber, orderData } = await request.json()

    if (!tableNumber || !orderData) {
      return NextResponse.json({ error: 'Table number and order data are required' }, { status: 400 })
    }

    // Validate table number
    if (typeof tableNumber !== 'number' || tableNumber < 1) {
      return NextResponse.json({ error: 'Invalid table number' }, { status: 400 })
    }

    // Upsert draft order
    await pool.query(
      `INSERT INTO draft_orders (user_id, table_number, order_data, expires_at)
       VALUES ($1, $2, $3, NOW() + INTERVAL '24 hours')
       ON CONFLICT (user_id, table_number)
       DO UPDATE SET order_data = $3, updated_at = NOW(), expires_at = NOW() + INTERVAL '24 hours'`,
      [authResult.user.id, tableNumber, JSON.stringify(orderData)]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error saving draft order:', error)
    return NextResponse.json(
      { error: 'Failed to save draft order' },
      { status: 500 }
    )
  }
}

// DELETE /api/draft-orders - Delete draft order
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tableNumber = searchParams.get('tableNumber')

    if (!tableNumber) {
      return NextResponse.json({ error: 'Table number is required' }, { status: 400 })
    }

    await pool.query(
      'DELETE FROM draft_orders WHERE user_id = $1 AND table_number = $2',
      [authResult.user.id, parseInt(tableNumber)]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting draft order:', error)
    return NextResponse.json(
      { error: 'Failed to delete draft order' },
      { status: 500 }
    )
  }
}
