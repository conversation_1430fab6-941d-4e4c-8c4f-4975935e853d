import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// GET /api/user-preferences - Get all user preferences
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await pool.query(
      'SELECT preference_key, preference_value FROM user_preferences WHERE user_id = $1',
      [authResult.user.id]
    )

    // Convert to key-value object
    const preferences: Record<string, string> = {}
    result.rows.forEach(row => {
      preferences[row.preference_key] = row.preference_value
    })

    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error fetching user preferences:', error)
    return NextResponse.json(
      { error: 'Failed to fetch preferences' },
      { status: 500 }
    )
  }
}

// POST /api/user-preferences - Set user preference
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { key, value } = await request.json()

    if (!key) {
      return NextResponse.json({ error: 'Preference key is required' }, { status: 400 })
    }

    // Validate preference keys
    const allowedKeys = [
      'theme',
      'selected_category', 
      'selected_table',
      'search_query',
      'sound_enabled',
      'notifications_enabled'
    ]

    if (!allowedKeys.includes(key)) {
      return NextResponse.json({ error: 'Invalid preference key' }, { status: 400 })
    }

    // Upsert preference
    await pool.query(
      `INSERT INTO user_preferences (user_id, preference_key, preference_value)
       VALUES ($1, $2, $3)
       ON CONFLICT (user_id, preference_key)
       DO UPDATE SET preference_value = $3, updated_at = NOW()`,
      [authResult.user.id, key, value || null]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error setting user preference:', error)
    return NextResponse.json(
      { error: 'Failed to set preference' },
      { status: 500 }
    )
  }
}

// DELETE /api/user-preferences - Delete user preference
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json({ error: 'Preference key is required' }, { status: 400 })
    }

    await pool.query(
      'DELETE FROM user_preferences WHERE user_id = $1 AND preference_key = $2',
      [authResult.user.id, key]
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting user preference:', error)
    return NextResponse.json(
      { error: 'Failed to delete preference' },
      { status: 500 }
    )
  }
}
