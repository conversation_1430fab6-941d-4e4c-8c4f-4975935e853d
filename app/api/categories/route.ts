import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth, requireAdmin } from '@/lib/auth'

export async function GET() {
  try {
    const result = await pool.query('SELECT * FROM categories ORDER BY display_order')
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch categories" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { name, key, icon, isActive, displayOrder } = body

    // Validate required fields
    if (!name || !key) {
      return NextResponse.json({ error: "Name and key are required" }, { status: 400 })
    }

    const result = await pool.query(
      'INSERT INTO categories (name, key, icon, is_active, display_order) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [name, key, icon, isActive !== undefined ? isActive : true, displayOrder || 0]
    )

    return NextResponse.json(result.rows[0], { status: 201 })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create category" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { id, name, key, icon, isActive, displayOrder } = body

    // Validate required fields
    if (!id || !name || !key) {
      return NextResponse.json({ error: "ID, name, and key are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE categories SET name = $1, key = $2, icon = $3, is_active = $4, display_order = $5, updated_at = CURRENT_TIMESTAMP WHERE id = $6 RETURNING *',
      [name, key, icon, isActive, displayOrder, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Category not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update category" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 })
    }

    const result = await pool.query('DELETE FROM categories WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Category not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "Category deleted successfully" })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete category" }, { status: 500 })
  }
}