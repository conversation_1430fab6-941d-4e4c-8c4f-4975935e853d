import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

// Default permissions structure
const defaultPermissions = {
  waiter: {
    games: {
      viewAllTables: false,
      viewOwnTablesOnly: true,
      createNewGames: true,
      stopAnyGame: false,
      stopOwnGamesOnly: true,
      viewAllHistory: false,
      viewOwnHistoryOnly: true
    },
    bar: {
      viewAllOrders: false,
      viewOwnOrdersOnly: true,
      createOrders: true,
      editAnyOrder: false,
      editOwnOrdersOnly: true,
      viewAllHistory: false,
      viewOwnHistoryOnly: true
    },
    analytics: {
      viewAnalytics: false,
      viewOwnStats: true
    },
    receipts: {
      viewAllReceipts: false,
      viewOwnReceiptsOnly: true,
      reprintAnyReceipt: false,
      reprintOwnReceiptsOnly: true
    },
    settings: {
      accessSettings: false,
      manageUsers: false,
      managePermissions: false
    }
  },
  admin: {
    games: {
      viewAllTables: true,
      viewOwnTablesOnly: true,
      createNewGames: true,
      stopAnyGame: true,
      stopOwnGamesOnly: true,
      viewAllHistory: true,
      viewOwnHistoryOnly: true
    },
    bar: {
      viewAllOrders: true,
      viewOwnOrdersOnly: true,
      createOrders: true,
      editAnyOrder: true,
      editOwnOrdersOnly: true,
      viewAllHistory: true,
      viewOwnHistoryOnly: true
    },
    analytics: {
      viewAnalytics: true,
      viewOwnStats: true
    },
    receipts: {
      viewAllReceipts: true,
      viewOwnReceiptsOnly: true,
      reprintAnyReceipt: true,
      reprintOwnReceiptsOnly: true
    },
    settings: {
      accessSettings: true,
      manageUsers: true,
      managePermissions: true
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user

    // Check if permissions table exists, if not create it
    await pool.query(`
      CREATE TABLE IF NOT EXISTS permissions (
        id SERIAL PRIMARY KEY,
        role VARCHAR(50) NOT NULL UNIQUE,
        permissions JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Get permissions from database
    const result = await pool.query('SELECT role, permissions FROM permissions')
    
    if (result.rows.length === 0) {
      // Initialize with default permissions
      await pool.query(
        'INSERT INTO permissions (role, permissions) VALUES ($1, $2), ($3, $4)',
        ['waiter', JSON.stringify(defaultPermissions.waiter), 'admin', JSON.stringify(defaultPermissions.admin)]
      )
      return NextResponse.json(defaultPermissions)
    }

    // Convert array to object
    const permissions: any = {}
    result.rows.forEach(row => {
      permissions[row.role] = row.permissions
    })

    return NextResponse.json(permissions)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch permissions" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user
    if (user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { role, permissions } = await request.json()

    if (!role || !permissions) {
      return NextResponse.json({ error: "Role and permissions are required" }, { status: 400 })
    }

    if (!['waiter', 'admin'].includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 })
    }

    // Update permissions in database
    const result = await pool.query(
      `INSERT INTO permissions (role, permissions, updated_at) 
       VALUES ($1, $2, CURRENT_TIMESTAMP) 
       ON CONFLICT (role) 
       DO UPDATE SET permissions = $2, updated_at = CURRENT_TIMESTAMP 
       RETURNING role, permissions`,
      [role, JSON.stringify(permissions)]
    )

    return NextResponse.json({ role: result.rows[0].role, permissions: result.rows[0].permissions })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update permissions" }, { status: 500 })
  }
}
