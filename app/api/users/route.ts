import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAdmin } from '@/lib/auth'

// GET /api/users - Get all users for assignment dropdown
export async function GET(request: NextRequest) {
  try {
    // Require admin authentication to see user list
    const authResult = await requireAdmin(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    // Get all users from the database
    const result = await pool.query(`
      SELECT id, username, full_name, role, is_active 
      FROM users 
      WHERE is_active = true 
      ORDER BY username
    `)

    // Format the response for the dropdown
    const users = result.rows.map(user => ({
      id: user.id,
      username: user.username,
      fullName: user.full_name,
      role: user.role,
      displayName: user.full_name ? `${user.full_name} (${user.username})` : user.username
    }))

    return NextResponse.json(users)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}
