'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function RefreshTokenPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const refreshToken = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      const data = await response.json()
      setResult(data)
      
      if (response.ok) {
        // Reload the page to apply the new token
        setTimeout(() => {
          window.location.href = '/'
        }, 2000)
      }
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Refresh Authentication Token</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This will refresh your authentication token with the latest user role from the database.
        </p>
        
        <Button onClick={refreshToken} disabled={loading}>
          {loading ? 'Refreshing Token...' : 'Refresh Token'}
        </Button>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">Result:</h3>
            <pre className="mt-2 text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
            {result.message && (
              <p className="mt-2 text-green-600">
                Token refreshed! Redirecting to home page...
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
