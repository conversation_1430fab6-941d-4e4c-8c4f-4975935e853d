'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function FixGameTablesPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runFix = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔄 Fixing game tables constraint...')
      
      const response = await fetch('/api/fix-game-tables-constraint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const fixResult = await response.json()
        setResult({
          success: true,
          message: 'Game tables constraint fixed successfully!',
          fixResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Fix failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>🎱 Fix Game Tables Constraint</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will fix the database constraint to allow multiple game tables with the same number assigned to different users.
          </p>
          
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">What this fix does:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Removes unique constraint on game table number</li>
              <li>• Adds composite constraint (number + user)</li>
              <li>• Ensures user assignment columns exist</li>
              <li>• Enables duplicate table numbers for different users</li>
            </ul>
          </div>
          
          <Button onClick={runFix} disabled={loading} size="lg">
            {loading ? 'Fixing Constraint...' : 'Fix Game Tables Constraint'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Fix Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>After running this fix:</strong></p>
            <p>• You can create Game Table 1 for Admin</p>
            <p>• You can create Game Table 1 for Gesti</p>
            <p>• You can create Game Table 1 for Kamarieri2</p>
            <p>• All with complete isolation between users</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
