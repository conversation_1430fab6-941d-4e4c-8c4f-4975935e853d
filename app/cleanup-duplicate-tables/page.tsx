'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function CleanupDuplicateTablesPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const cleanupDuplicates = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🧹 Cleaning up duplicate tables...')
      
      const response = await fetch('/api/cleanup-duplicate-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const cleanupResult = await response.json()
        setResult({
          success: true,
          message: 'Duplicate tables cleaned up successfully!',
          cleanupResult
        })
      } else {
        const error = await response.text()
        setResult({
          error: 'Cleanup failed',
          status: response.status,
          errorText: error
        })
      }
      
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>🧹 Clean Up Duplicate Game Tables</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This will remove duplicate game table entries that may have been created during game operations.
          </p>
          
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">🔍 What this does:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Finds tables with the same number assigned to the same user</li>
              <li>• Keeps the original table (lowest ID)</li>
              <li>• Removes duplicate entries</li>
              <li>• Preserves all user assignments and table data</li>
              <li>• Shows final statistics by user</li>
            </ul>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="font-semibold text-green-800 mb-2">✅ Safe operation:</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Only removes exact duplicates</li>
              <li>• Preserves user isolation</li>
              <li>• Maintains table assignments</li>
              <li>• Does not affect active games</li>
            </ul>
          </div>
          
          <Button onClick={cleanupDuplicates} disabled={loading} size="lg">
            {loading ? 'Cleaning Up Duplicates...' : 'Clean Up Duplicate Tables'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">Cleanup Result:</h3>
              <pre className="mt-2 text-sm overflow-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="text-sm text-gray-500 space-y-1">
            <p><strong>After cleanup:</strong></p>
            <p>• Each user will have only one table per table number</p>
            <p>• Active Tables view will show correct table count</p>
            <p>• Multi-user isolation will work perfectly</p>
            <p>• No duplicate table entries in the interface</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
