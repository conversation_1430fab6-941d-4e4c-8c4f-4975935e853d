# Cache Troubleshooting Guide

If you're experiencing issues with the billiard dashboard application, such as:
- Need to open the app in incognito mode to see it
- Login issues or authentication problems
- Blank screens or loading issues
- Outdated data or UI not updating

## Quick Fix Options

### Option 1: Use the Built-in Cache Clear Button
1. Log into the application
2. Click on your user avatar in the top-right corner
3. Select "Clear Cache" from the dropdown menu
4. Confirm when prompted
5. The page will reload automatically

### Option 2: Manual Browser Cache Clear
If you can't access the application at all:

#### Chrome/Edge:
1. Press `Ctrl+Shift+Delete` (Windows) or `Cmd+Shift+Delete` (Mac)
2. Select "All time" as the time range
3. Check all boxes (Browsing history, Cookies, Cached images and files)
4. Click "Clear data"
5. Refresh the page

#### Firefox:
1. Press `Ctrl+Shift+Delete` (Windows) or `Cmd+Shift+Delete` (Mac)
2. Select "Everything" as the time range
3. Check all boxes
4. Click "Clear Now"
5. Refresh the page

#### Safari:
1. Go to Safari > Preferences > Privacy
2. Click "Manage Website Data"
3. Click "Remove All"
4. Confirm and refresh the page

### Option 3: Incognito/Private Mode (Temporary)
- Chrome: `Ctrl+Shift+N` (Windows) or `Cmd+Shift+N` (Mac)
- Firefox: `Ctrl+Shift+P` (Windows) or `Cmd+Shift+P` (Mac)
- Safari: `Cmd+Shift+N` (Mac)

## Why This Happens

The application stores authentication tokens and cached data in your browser's localStorage. Sometimes this data can become corrupted or outdated, causing issues. Clearing the cache removes this stored data and allows the application to start fresh.

## Prevention

- Use the built-in "Clear Cache" button periodically if you notice any issues
- Avoid force-closing the browser while the application is running
- Log out properly when finished using the application

## Still Having Issues?

If clearing the cache doesn't resolve your issue:
1. Check that you're using a supported browser (Chrome, Firefox, Safari, Edge)
2. Ensure JavaScript is enabled
3. Try accessing the application from a different device or network
4. Contact your system administrator
