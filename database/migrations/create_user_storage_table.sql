-- Create user_storage table for unified storage system
-- This replaces the separate user_preferences and draft_orders tables
-- with a more flexible key-value storage system

CREATE TABLE IF NOT EXISTS user_storage (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    storage_key VARCHAR(255) NOT NULL,
    storage_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, storage_key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_storage_user_id ON user_storage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_storage_key ON user_storage(storage_key);
CREATE INDEX IF NOT EXISTS idx_user_storage_user_key ON user_storage(user_id, storage_key);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_storage_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_storage_updated_at
    BEFORE UPDATE ON user_storage
    FOR EACH ROW
    EXECUTE FUNCTION update_user_storage_updated_at();

-- Migrate existing data from user_preferences table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_preferences') THEN
        INSERT INTO user_storage (user_id, storage_key, storage_value, created_at, updated_at)
        SELECT 
            user_id, 
            'pref_' || preference_key as storage_key,
            preference_value as storage_value,
            created_at,
            updated_at
        FROM user_preferences
        ON CONFLICT (user_id, storage_key) DO NOTHING;
        
        RAISE NOTICE 'Migrated data from user_preferences to user_storage';
    END IF;
END $$;

-- Migrate existing data from draft_orders table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'draft_orders') THEN
        INSERT INTO user_storage (user_id, storage_key, storage_value, created_at, updated_at)
        SELECT 
            user_id, 
            'draft_order_' || table_number::text as storage_key,
            order_data::text as storage_value,
            created_at,
            updated_at
        FROM draft_orders
        ON CONFLICT (user_id, storage_key) DO NOTHING;
        
        RAISE NOTICE 'Migrated data from draft_orders to user_storage';
    END IF;
END $$;

-- Function to clean up old storage entries (run periodically)
CREATE OR REPLACE FUNCTION cleanup_old_storage_entries()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Delete entries older than 30 days for temporary keys
    DELETE FROM user_storage
    WHERE storage_key LIKE 'temp_%'
    AND updated_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Delete draft orders older than 7 days
    DELETE FROM user_storage
    WHERE storage_key LIKE 'draft_%'
    AND updated_at < NOW() - INTERVAL '7 days';

    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Create a scheduled job to clean up old entries (if using pg_cron extension)
-- SELECT cron.schedule('cleanup-storage', '0 2 * * *', 'SELECT cleanup_old_storage_entries();');
