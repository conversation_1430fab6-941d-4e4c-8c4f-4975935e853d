-- Create draft_orders table to replace localStorage for pending table orders
-- This stores incomplete orders that users are building before submission

CREATE TABLE IF NOT EXISTS draft_orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    table_number INTEGER NOT NULL,
    order_data JSONB NOT NULL, -- Stores the complete order structure
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    UNIQUE(user_id, table_number)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_draft_orders_user_id ON draft_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_draft_orders_table_number ON draft_orders(table_number);
CREATE INDEX IF NOT EXISTS idx_draft_orders_expires_at ON draft_orders(expires_at);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_draft_orders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_draft_orders_updated_at
    BEFORE UPDATE ON draft_orders
    FOR EACH ROW
    EXECUTE FUNCTION update_draft_orders_updated_at();

-- Function to clean up expired draft orders (run periodically)
CREATE OR REPLACE FUNCTION cleanup_expired_draft_orders()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM draft_orders WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Create a scheduled job to clean up expired drafts (if using pg_cron extension)
-- SELECT cron.schedule('cleanup-draft-orders', '0 */6 * * *', 'SELECT cleanup_expired_draft_orders();');
