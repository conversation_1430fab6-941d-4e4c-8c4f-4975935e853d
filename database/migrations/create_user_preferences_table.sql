-- Create user_preferences table to replace localStorage for user settings
-- This stores user-specific preferences like theme, selected categories, etc.

CREATE TABLE IF NOT EXISTS user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, preference_key)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(preference_key);

-- Insert default preferences for existing users
INSERT INTO user_preferences (user_id, preference_key, preference_value)
SELECT id, 'theme', 'light'
FROM users
WHERE NOT EXISTS (
    SELECT 1 FROM user_preferences 
    WHERE user_preferences.user_id = users.id 
    AND preference_key = 'theme'
);

INSERT INTO user_preferences (user_id, preference_key, preference_value)
SELECT id, 'selected_category', 'drinks'
FROM users
WHERE NOT EXISTS (
    SELECT 1 FROM user_preferences 
    WHERE user_preferences.user_id = users.id 
    AND preference_key = 'selected_category'
);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();
