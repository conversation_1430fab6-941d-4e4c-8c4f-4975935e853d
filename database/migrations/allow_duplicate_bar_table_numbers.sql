-- Migration: Allow duplicate bar table numbers for different users
-- This enables each user to have their own independent set of table numbers (1-10)

-- Remove the unique constraint on table number
ALTER TABLE tables DROP CONSTRAINT IF EXISTS tables_number_key;

-- Add a composite unique constraint to ensure each user can only have one table with each number
-- This prevents a single user from having duplicate table numbers, but allows different users to have the same numbers
ALTER TABLE tables ADD CONSTRAINT unique_table_number_per_user 
UNIQUE (number, assigned_user_id);

-- Add an index for better performance when querying by user
CREATE INDEX IF NOT EXISTS idx_tables_assigned_user 
ON tables(assigned_user_id) 
WHERE assigned_user_id IS NOT NULL;

-- Add an index for better performance when querying by number and user
CREATE INDEX IF NOT EXISTS idx_tables_number_user 
ON tables(number, assigned_user_id);

-- Update any existing unassigned tables to have a default assignment
-- This prevents conflicts when assigning tables to users
UPDATE tables 
SET assigned_user_id = NULL, assigned_username = NULL 
WHERE assigned_user_id IS NULL;

-- Add a check constraint to ensure assigned_user_id and assigned_username are both set or both null
ALTER TABLE tables ADD CONSTRAINT check_user_assignment 
CHECK (
  (assigned_user_id IS NULL AND assigned_username IS NULL) OR 
  (assigned_user_id IS NOT NULL AND assigned_username IS NOT NULL)
);

-- Create a function to help with table assignment
CREATE OR REPLACE FUNCTION assign_table_to_user(
  p_table_number INTEGER,
  p_user_id INTEGER,
  p_username VARCHAR(100),
  p_table_name VARCHAR(100) DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  existing_table_id INTEGER;
  new_table_id INTEGER;
  final_table_name VARCHAR(100);
BEGIN
  -- Set default table name if not provided
  IF p_table_name IS NULL THEN
    final_table_name := 'Table ' || p_table_number;
  ELSE
    final_table_name := p_table_name;
  END IF;
  
  -- Check if this user already has a table with this number
  SELECT id INTO existing_table_id
  FROM tables 
  WHERE number = p_table_number AND assigned_user_id = p_user_id;
  
  IF existing_table_id IS NOT NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User already has a table with this number',
      'table_id', existing_table_id
    );
  END IF;
  
  -- Create the new table
  INSERT INTO tables (number, name, is_active, hourly_rate, assigned_user_id, assigned_username)
  VALUES (p_table_number, final_table_name, true, 0, p_user_id, p_username)
  RETURNING id INTO new_table_id;
  
  RETURN json_build_object(
    'success', true,
    'table_id', new_table_id,
    'table_number', p_table_number,
    'user_id', p_user_id,
    'username', p_username,
    'table_name', final_table_name
  );
END;
$$ LANGUAGE plpgsql;

-- Create a function to bulk create tables for a user (1-10)
CREATE OR REPLACE FUNCTION create_user_table_set(
  p_user_id INTEGER,
  p_username VARCHAR(100),
  p_table_count INTEGER DEFAULT 10
) RETURNS JSON AS $$
DECLARE
  i INTEGER;
  result JSON;
  created_tables JSON[] := '{}';
  table_name VARCHAR(100);
BEGIN
  -- Create tables 1 through p_table_count for the user
  FOR i IN 1..p_table_count LOOP
    table_name := p_username || ' - Table ' || i;
    
    -- Try to create each table
    SELECT assign_table_to_user(i, p_user_id, p_username, table_name) INTO result;
    
    -- Add to results array
    created_tables := array_append(created_tables, result);
  END LOOP;
  
  RETURN json_build_object(
    'success', true,
    'user_id', p_user_id,
    'username', p_username,
    'table_count', p_table_count,
    'results', created_tables
  );
END;
$$ LANGUAGE plpgsql;

-- Example usage (commented out):
-- SELECT create_user_table_set(1, 'Gesti', 10);
-- SELECT create_user_table_set(2, 'Kamarieri2', 10);
