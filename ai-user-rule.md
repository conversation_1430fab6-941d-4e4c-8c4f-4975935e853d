# AI Assistant Rule: Translation Key Handling

When I ask for changes or new features, please follow these guidelines for translation keys:

1. NEVER use raw translation keys in UI elements (e.g., never show `settings.updateGameTable` directly to users)

2. Always implement translations properly by:
   - Adding new translation keys to both English and Albanian sections in `app/i18n/locales/translations.json`
   - Using the translation function in components: `{t('keyname')}`
   - Ensuring keys follow the existing naming pattern (e.g., `section.action`)

3. When showing code examples, always demonstrate proper translation usage:
   ```tsx
   // CORRECT:
   <Button onClick={handleUpdate}>{t('settings.updateGameTable')}</Button>
   
   // INCORRECT - never do this:
   <Button onClick={handleUpdate}>settings.updateGameTable</Button>
   ```

4. For any new UI text, always provide both English and Albanian translations , translate all even hardcoded text

5. Test all UI changes with both language settings to ensure proper display