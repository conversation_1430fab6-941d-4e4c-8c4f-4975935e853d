// Simple script to migrate hardcoded bar tables to database
const fetch = require('node-fetch');

async function migrateBarTables() {
  try {
    console.log('🔄 Running bar tables migration...');
    
    const response = await fetch('http://localhost:3000/api/migrate-bar-tables', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();
    console.log('Migration result:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Migration completed successfully!');
    } else {
      console.log('❌ Migration failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Error running migration:', error.message);
  }
}

migrateBarTables();
